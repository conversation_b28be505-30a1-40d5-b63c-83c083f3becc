$( document ).ready(function() {
    $("#li_manufacture_indent").addClass('active');
    $("#menu_production_planning").addClass('selected');
    $(".chosen-select").chosen();
    loadMaterial();
    manufacturingIndentModalCloseEvent();
    materialListBlurEvent('material_required');
    loadAllUnits("#id_material-all_units");
    getOaDetails();
    $(".edit_material-removal-icon").click(function(){
        $("#edit_material_required").val("").removeAttr("readonly");
        $("#edit_material_id_hidden, #edit_make_id_hidden").val("");
        $(this).addClass("hide");

    });
    $( window ).scroll(function() {
      $( window ).resize();
    });
});

function applyMiFilter() {
    updateFilterText();
    getOaDetails();
    closeFilterOption();
}

$(window).load(function(){
    updateFilterText();
});

function updateFilterText() {
    $(".filtered-date b").text($("#reportrange").find("span").text());
    $(".filtered-party b").text($("#party option:selected").text());
    $(".filtered-mi-not-applicable b").text(($("#id_mi_not_applicable").is(":checked")? "Included":"Excluded"));
}


function loadMaterial(loadType = "") {
    var type = '';
    var dataToSend = {
        'type': type,
        'party_id': '',
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

    $("#material_required, #edit_material_required").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            if($(this).attr("id") != "edit_material_required") {
                ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
            }
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item"){
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#material_required").val(itemName).attr("readonly", true);
                $("#edit_material_required").val(itemName).attr("readonly", true);
                $("#material_is_service").val(ui.item.is_service);
                $("#manufacturing_unit_display").text(ui.item.unit);
                $(".material-removal-icon, .edit_material-removal-icon").removeClass('hide');
                $("#material_id_hidden").val(ui.item.id);
                $("#edit_material_id_hidden").val(ui.item.id);
                $("#make_id_hidden").val(ui.item.mid);
                $("#edit_make_id_hidden").val(ui.item.mid);
                $("#edit_manufacturing_unit_display").text(ui.item.unit);
                if (ui.item.alt_uom > 0) {
                    loadAlternateUnits(ui.item.id, ui.item.unit, '#id_material-alternate_units, #edit_id_material-alternate_units');
                    $("#manufacturing_unit_display").addClass("hide");
                    $("#edit_manufacturing_unit_display").addClass("hide");
                    $(".alternate_unit_select_box").removeClass("hide");
                }
                else {
                    $(".alternate_unit_select_box").addClass("hide");
                }

            }
            else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}

$(function () {
    $('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                $('#loading').hide();
                $("#material_id_hidden").val(response['item_id']);
                $("#edit_material_id_hidden").val(response['item_id']);
                $("#material_required").val(response['name']).attr("readonly","readonly");
                $("#edit_material_required").val(response['name']).attr("readonly","readonly");
                $("#material_required").closest("div").find(".material-removal-icon").removeClass("hide");
                $("#edit_material_required").closest("div").find(".edit_material-removal-icon").removeClass("hide");
                $("#make_id_hidden").val(response['make_id']);
                $("#edit_make_id_hidden").val(response['make_id']);
                $("#manufacturing_unit_display").text(response['unit_name']);
                $("#edit_manufacturing_unit_display").text(response['unit_name']);
                $("#material_is_service").val(response['is_service']);
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#addmaterials").trigger("click");
                }
                $('#add_material_modal').modal('hide')
            }
        });
        return false;
    });
});

function getOaDetails(){
    if(typeof(oTable) !== 'undefined') {
        oTable.destroy();
    }
    $("#mi_list").find("tbody").empty();
    $("#loading").show();
    var data = [];
    $.ajax({
        url: "/erp/production/get_oa_details/",
        type: "post",
        datatype: "json",
        data: {
           from_date: $(".fromdate").val(),
           to_date: $(".todate").val(),
           party: $("#party").val(),
           mi_not_applicable: $("#id_mi_not_applicable").is(":checked")
        },
        success: function (response) {
            checkSessionExpired(response);
            console.log(response)
            $.each(response.oa_details, function (i, item) {
                createMiListRow(item);
                TooltipInit();
            });
            listTableHoverIconsInit("mi_list");
            dataTableInit();
            $("#loading").hide();
        },
        error: function (xhr, errmsg, err) {
            swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
        }
    });
}

function createMiListRow(item, type) {
    var mi_permission = $("#mi_permission").val().toLowerCase();
    var editIcon = "";
    var mi_date = moment(item.mi_date).format("MMM DD, YYYY");
    var mi_qty = "";
    var mi_action = "";
    var add_new_forcast = '';
    var oa_qty_content = '';
    var is_oa_enabled = '';
    var forcast_hover_icon = '';
    var hover_mi_action = '';
    var mi_status = '';
    var alternate_oa_qty = Number(item.oa_qty).toFixed(3) / item.scale_factor;
    var completed = item.grn_qty.grn_qty;
    var alternate_unit_qty = Number(item.quantity).toFixed(3) / item.scale_factor;
    var completed_qty_percentage = ( completed / alternate_unit_qty ) * 100;
    if(mi_permission != "true") {
        mi_status = "disabled";
    }
     if(item.oa_id == ''){
        if(mi_permission == "true") {
            editIcon = `<span class="table-inline-icon hide" data-tooltip="tooltip" data-placement="left" title="Edit" role="button" onclick="editManufacturingIndent(this)">
                            <i class="fa fa-pencil"></i>
                        </span>`;
        }
        add_new_forcast = `<td colspan='2' class="exclude_export purpose-content" data-purpose="${item.purpose}">${item.purpose}</td>
                            <td class='exclude_export hide'></td>`;
        forcast_hover_icon = ` <span class="table-inline-icon-container">
                                    ${editIcon}
                                    <span class="table-inline-icon remarks-icon hide" data-tooltip="tooltip" data-placement="left" title="Remarks" role="button" onclick="showRemarksModal(this)">
                                        <i class="fa fa-quote-right"></i>
                                    </span>
                                </span>`;
     }
     else {
        if(item.mi_no != '') {
            forcast_hover_icon = `<span class="table-inline-icon-container">
                                        <span class="table-inline-icon remarks-icon hide" data-tooltip="tooltip" data-placement="left" title="Remarks" role="button" onclick="showRemarksModal(this)">
                                            <i class="fa fa-quote-right"></i>
                                        </span>
                                    <span>`
        }
        add_new_forcast = `<td class="exclude_export party-content" data-party-id="${item.party_id}">${item.party_name}</td>
                           <td class="exclude_export text-left oa-content" data-oa-id="${item.oa_id}">
                                <span class="oa-number" data-oa-no="${item.oa_no}">${item.oa_no}</span> </br>
                                <small>${moment(item.oa_date).format("MMM DD, YYYY")}</small>
                           </td>`
        oa_qty_content = `<span class="supplementary_qty pull-right">${Number(alternate_oa_qty).toFixed(2)}</span>`;
        is_oa_enabled = 'oa-enabled'
     }
     if(Number(item.mi_not_applicable) != 0) {

        if(mi_permission == "true") {
            mi_action = `<button class='mi_not_applicable' data-tooltip="tooltip" title="Mark this Indent as Not Applicable" data-placement="top" onclick="removeMiDetails(this, 'add')">
                            <span style="font-size: 11px">NA</span>
                            <i class="fa fa-close" aria-hidden="true" style="color: #000; padding-left: 8px"></i>
                         </button>`
        }
        else{
            mi_action = `<button class='mi_not_applicable'>
                            <span style="font-size: 11px">NA</span>
                         </button>`
        }
        hover_mi_action = `<span>${mi_action}</span>`;
     }
     else {
        if(item.mi_no == '') {
            if(mi_permission == "true") {
                mi_action = `<button class="btn btn-save" data-tooltip="tooltip" title="Create Manufacturing Indent" data-placement="top" onclick="createManufacturingIndent(this, 'oa', 'add-through-oa')">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                            </button>
                            <button class="btn btn-delete" data-tooltip="tooltip" title="Mark this Indent as Not Applicable" data-placement="top" onclick="removeMiDetails(this, 'remove')">
                               <i class="fa fa-close" aria-hidden="true" style="color: #FFF;"></i>
                            </button>`;
            }
            hover_mi_action = `<span>${mi_action}</span>${forcast_hover_icon}`;
        }
        else {
            mi_action = item.mi_no  + "<br /><small>" + mi_date + "</small>";
            var miType = (item.oa_id != '') ? 'oa' : 'forecast';
            mi_qty = ` <button id="required_mi_qty" class="btn btn-save" data-tooltip="tooltip" title="Update Required Quantity" data-placement="top" onclick="createManufacturingIndent(this, '${miType}', 'edit-qty-through-${miType}')">
                            <i class="fa fa-check" aria-hidden="true"></i>
                         </button>
                         <button class="btn btn-delete" data-tooltip="tooltip" title="cancel" data-placement="top" onclick="restoreDefaultQuantity(this)">
                            <i class="fa fa-close" aria-hidden="true" style="color: #FFF;"></i>
                         </button>`
            hover_mi_action = `<span class="table-inline-icon-bg-no">${mi_action}</span>${forcast_hover_icon}`;
        }
     }
     var item_name = item.item_name
     if(item.is_service == 1){
        item_name = item_name + "<span class='service-item-flag'></span>";
     }
     var delivery_date= moment(item.delivery_date).format("MMM DD, YYYY");
     if (delivery_date == "Invalid date"){
        delivery_date = "";
     }



     var row = `<tr id="${item.mi_id}_${item.item_id}_${item.make_id}" data-oa_id="${item.oa_id}" data-min-qty ="${item.item_pur_qty}" data-alternate_unit = "${item.alternate_unit_id}">
                     ${add_new_forcast}
                     <td class="exclude_export item-content" data-item-id="${item.item_id}" data-item-name="${item.item_name}" data-make-id="${item.make_id}" >${item_name}
                        <input type="hidden" class="item_remarks" value='${item.remarks}' />
                     </td>
                     <td class="exclude_export text-right qty-content" data-alternate_unit_id = "${item.alternate_unit_id}" data-default-qty="${Number(alternate_unit_qty).toFixed(3)}" data-max-qty="${Number(alternate_oa_qty).toFixed(3)}" data-min-quantity ="${item.item_pur_qty}">
                        <span>
                            <input type="text" class="form-control text-right quantity ${is_oa_enabled}" maxlength="16"
                            placeholder="Reqd Qty" value="${Number(alternate_unit_qty).toFixed(3)}" onfocus="setNumberRangeOnFocus(this,12,3)"
                            onKeyUp="showUpdateButton(this)" ${mi_status}/>
                            <span class="item_unit_name unit_display pull-right" data-unit-name="${item.unit_name}">${item.unit_name}</span>
                            ${oa_qty_content}
                        </span>
                         <span class="qty_update_button hide">${mi_qty}</span>
                         ${alternate_unit_qty > 0 ?
                         `<span class='status-container'  data-placement="left" data-tooltip="tooltip" >
                            <span >
                                <span class="progress" style="margin-bottom:6px; margin-top:7px; display: block;">
                                    <span class="progress-bar progress-bar-success" role="progressbar" style="width:${completed_qty_percentage}%; max-width: 100%">
                                        ${Math.round(completed_qty_percentage)}%
                                    </span>
                                </span>
                                <span class='qty-label'>${completed} / ${alternate_unit_qty} ${item.unit_name}</span>
                            </span>
                         </span>` : ''
                         }
                     </td>
                     <td class="exclude_export text-center delivery-date" data-default-date="${delivery_date}">${delivery_date}</td>
                     <td class="exclude_export text-left mi-content" data-mi-id="${item.mi_id}" data-mi-no="${item.mi_no}">
                        ${hover_mi_action}
                     </td>

                    <td class="hide">${item.purpose}</td>
                    <td class="hide">${item.party_name}</td>
                    <td class="hide">${item.oa_no}</td>
                    <td class="hide">
                        ${item.oa_date!= "" ? `${moment(item.oa_date).format("MMM DD, YYYY")}` : ''}
                    </td>
                    <td class="hide">${item.item_name}</td>
                    <td class="hide">${Number(alternate_unit_qty).toFixed(3)}</td>
                    <td class="hide">
                        ${item.oa_no != "" ? `${Number(alternate_oa_qty).toFixed(3)}` : ''}</td>
                    <td class="hide">${item.unit_name}</td>
                    <td class="hide">${delivery_date}</td>
                    <td class="hide">${item.mi_no}</td>
                    <td class="hide">
                        ${item.mi_date!= "" ? `${moment(item.mi_date).format("MMM DD, YYYY")}` : ''}
                    </td>

                 </tr>`;
     $("#mi_list tbody").append(row);
}

function showRemarksModal(current) {
    loadItemRemarks(current);
    $("#mi_remarks_modal").modal("show");
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var currentRow = $(current).closest("tr");
    var remarks_mi_no = currentRow.find(".mi-content").attr("data-mi-no")
    $("#mi_remarks_modal .modal-title").text(`Remarks - ${remarks_mi_no}`);
    $("#mi_remarks_modal .modal-title").attr("data-edit-id", currentRow.attr("id"));
}

function saveNewRemarks(current) {
    $(".error-border").removeClass('error-border');
    $(".custom-error-message").remove();
    var ControlCollections = [
        {
            controltype: 'textbox',
            controlid: 'id_mi-remarks',
            isrequired: true,
            errormsg: 'Remarks is required.'
        }
    ];
    var result = JSCustomValidator.JSvalidate(ControlCollections);
    if (result) {
        var remarksData = [
           {
               mi_id: $("#mi_remarks_modal .modal-title").attr("data-edit-id"),
               remarks: $("#id_mi-remarks").val()
           }
        ];
        $("#loading").show();
        console.log(remarksData)
        $.ajax({
            url: '/erp/production/remarks_manufacture_indent/',
            type: "POST",
            dataType: "json",
            data: { 'mi_dict': JSON.stringify(remarksData) },
            success: function (response) {
                if(response.response_message.toLowerCase() == "success") {
                    swal({
                        title: '<span style="color: #44ad6b;">SUCCESS</span>',
                        text: `Remarks Added Successfully`,
                        type: "success"
                    });
                    var updatedRemarks = JSON.stringify(response.remarks)
                    $(`#mi_list tr#${remarksData[0]['mi_id']}`).find(".item_remarks").val(updatedRemarks);
                    var currentEle = $(`#mi_list tr#${remarksData[0]['mi_id']}`).find(".remarks-icon");
                    loadItemRemarks(currentEle);
                    $("#id_mi-remarks").val("");
                    $("#loading").hide();
                }
                else {
                    swal("Failure", `Due to technical issue, unable to request the process.<br />Please contact support.`, "warning")
                }

            },
            error: function (xhr, errmsg, err) {
                checkSessionExpired(xhr.responseText);
            }
        });
    }
}

function resetMIDetails(){
    $(".mi_purpose").val("");
    $(".material-removal-icon").addClass('hide');
    $(".mi_material_id").val("");
    $(".mi_make_id").val("");
    $(".mi_qty").val(0.00);
    $(".mi_unit").html("&nbsp;");
    $(".edit_unit_display").html("&nbsp;");
    $(".mi_delivery_due").datepicker("setDate", new Date());
    $("#material_required").val("").removeAttr("readonly");
    $("#manufacturing_unit_display").removeClass("hide");
    $(".alternate_unit_select_box").addClass("hide");
}
function manufacturingIndentModalCloseEvent() {
    $('#edit_manufacturing_indent').on('hidden.bs.modal', function () {
      resetMIDetails();
    });
}
function editManufacturingIndent(current) {
    var currentRow = $(current).closest("tr");
    $("#edit_manufacturing_indent .modal-title").text("Edit MI - "+currentRow.find(".mi-content").attr("data-mi-no"));
    $("#edit_manufacturing_indent .modal-title").attr("data-edit-item-id", currentRow.attr("id"));
    $("#edit_manufacturing_indent .modal-title").attr("data-edit-qty", currentRow.attr("data-min-qty"));
    $("#edit_manufacturing_indent .modal-title").attr("data-alternate_unit_qty", currentRow.attr("data-alternate_unit"));
    $("#edit_manufacturing_purpose").val(currentRow.find(".purpose-content").attr("data-purpose"));
    $("#edit_material_required").val(currentRow.find(".item-content").attr("data-item-name")).attr("readonly", "readonly");
    $("#edit_material_id_hidden").val(currentRow.find(".item-content").attr("data-item-id"));
    $("#edit_make_id_hidden").val(currentRow.find(".item-content").attr("data-make-id"));
    $("#edit_manufacturing_qty").val(currentRow.find(".qty-content").attr("data-default-qty"));
    $("#edit_manufacturing_unit_display").text(currentRow.find(".item_unit_name").attr("data-unit-name"));
    $("#edit_manufacturing_delivery_due").val(currentRow.find(".delivery-date").attr("data-default-date"));
    $("#edit_manufacturing_indent").modal("show");
    $(".edit_material-removal-icon").removeClass('hide');
    $("#edit_manufacturing_unit_display").removeClass("hide");
    $(".alternate_unit_select_box").addClass("hide");
}

function showUpdateButton(current){
   var default_qty = $(current).closest("tr").find("td.qty-content").attr("data-default-qty");
   var new_qty = Number($(current).closest("tr").find(".quantity").val());
   if(new_qty == default_qty){
        $(current).closest("tr").find(".qty_update_button").addClass('hide');
   }
   else {
        $(current).closest("tr").find(".qty_update_button").removeClass('hide');
   }
}

function restoreDefaultQuantity(current){
    var currentTr = $(current).closest("tr");
    currentTr.find(".qty_update_button").addClass('hide');
    var default_quantity =  currentTr.find("td.qty-content").attr("data-default-qty");
    currentTr.find(".quantity").val(default_quantity);
}

function validateManufacturingIndentField(actionType) {
    if(actionType == "add-through-forecast"){
        var validatorField = ['manufacturing_purpose', 'material_required', 'manufacturing_qty'];
    }
    else if(actionType == "edit-through-forecast"){
        var validatorField = ['edit_manufacturing_purpose', 'edit_material_required', 'edit_manufacturing_qty'];
        if($("#edit_material_id_hidden").val() == "") {
            $("#edit_material_required").val("");
        }
    }
    $(".error-border").removeClass('error-border');
	$(".custom-error-message").remove();
	var ControlCollections = [
		{
			controltype: 'textbox',
			controlid: validatorField[0],
			isrequired: true,
			errormsg: 'Purpose is required.'
		},
		{
		   controltype: 'textbox',
		    controlid: validatorField[1],
			isrequired: true,
			errormsg: 'Item Name is required.'
		},
		{
		   controltype: 'textbox',
		   controlid: validatorField[2],
		   isrequired: true,
		   errormsg: 'Quantity is required.',
		   mindigit: 0.001,
           mindigiterrormsg: 'Quantity is required.'
		},

	];
	var result = JSCustomValidator.JSvalidate(ControlCollections);
	return result;
}

function loadItemRemarks(current) {
    var remarks = $(current).closest("tr").find(".item_remarks").val();
    if (remarks!= ""){
        remarkList = JSON.parse(remarks);
    }else{
        remarkList = []
    }
    if (remarkList != null && remarkList.length > 0) {
        var history = '<div class="chat-container">';
        $.each(remarkList, function(i, remark) {
            var sDate = moment(remark.date).format('MMM D, YYYY');
            if(sDate == 'Invalid date') sDate =' ';
            history += ` <div class="chat-list">
                            <span class="chat-list-name">${remark.by}</span>
                            <span class="chat-list-date">${sDate}</span>
                            <span class="chat-list-description">${remark.remarks}</span>
                        </div>`;
        });
        history += '</div>';
        $(".remarks-content-container").html(history);
    }
    else {
        var history = `<div class="chat-container">
                            <div class="chat-list text-center">
                                <span class="chat-list-name">No Remarks</span>
                            </div>
                        </div>`;
        $(".remarks-content-container").html(history);
    }
}

function createManufacturingIndent(current, indentType, actionType){
    var purchase_qty = $(current).closest("tr").find("td.qty-content").attr("data-min-quantity");
    var check_purchase_qty = Number($("#edit_manufacturing_indent .modal-title").attr("data-edit-qty"))
    if(actionType == "add-through-forecast" || actionType == "edit-through-forecast"){
        var validateManufacturingIndent = validateManufacturingIndentField(actionType);
    }
    else{
       validateManufacturingIndent = true
    }
    if(validateManufacturingIndent) {
        if ($('#material_id_hidden').val() == "" && actionType == "add-through-forecast") {
            var materialName =  $("#material_required").val();
            var materialUnit = $("#id_material-all_units").val();
            addNewMaterial(materialName, materialUnit,"","0");
        }
        else {
            if(actionType == "add-through-oa" || actionType == "edit-qty-through-oa" ) {
                if( Number($(current).closest("tr").find(".quantity").val()) > $(current).closest("tr").find("td.qty-content").attr("data-max-qty")) {
                    $(current).closest("tr").find(".qty_update_button").removeClass('hide');
                    swal("Max Quantity Exceeded","Manufacturing Indent Quantity cannot exceed the OA Quantity. ","warning");
                    return false;
                }
                else {
                    $(current).closest("tr").find(".qty_update_button").addClass('hide');
                }
            }
            if(actionType == "edit-qty-through-forecast" || actionType == "edit-qty-through-oa") {
                if( Number($(current).closest("tr").find(".quantity").val()) < purchase_qty) {
                    swal("Min Quantity Exceeded",`Manufacturing Indent Quantity Should Not be Lesser Than Purchase Quantity<br /><br /><b>Purchased Qty: ${purchase_qty}</b>`,"warning");
                    return false;
                }
                else {
                    $(current).closest("tr").find(".qty_update_button").addClass('hide');
                }
            }
            if( Number($("#edit_manufacturing_qty").val()) < check_purchase_qty) {
                    swal("Min Quantity Exceeded",`Manufacturing Indent Quantity Should Not be Lesser Than Purchase Quantity<br /><br /><b>Purchased Qty: ${check_purchase_qty}</b>`,"warning");
                return false;
            }
            var currentTr = $(current).closest("tr");
            if(indentType == "oa") {
                var newManufacturingIndent = [
                    {
                        "mi_id": currentTr.find("td.mi-content").attr("data-mi-id"),
                        "oa_id": currentTr.find("td.oa-content").attr("data-oa-id"),
                        "item_id": currentTr.find("td.item-content").attr("data-item-id"),
                        "item_name": currentTr.find("td.item-content").attr("data-item-name"),
                        "make_id": currentTr.find("td.item-content").attr("data-make-id"),
                        "quantity": currentTr.find(".quantity").val(),
                        "delivery_date": moment(currentTr.find("td.delivery-date").attr("data-default-date")).format("YYYY-M-D"),
                        "indent_type": indentType,
                        "purpose": "",
                        "unit_name": currentTr.find(".item_unit_name").text(),
                        "alternate_unit_id": currentTr.find("td.qty-content").attr("data-alternate_unit_id"),
                    }
                ]
            }
            else {
                var mi_id = "";
                if(current != "") {
                    mi_id = currentTr.find("td.mi-content").attr("data-mi-id");
                }

                var idPrefix = "";
                if(actionType == "edit-through-forecast") {
                    idPrefix = 'edit_';
                    mi_id = $("#edit_manufacturing_indent .modal-title").attr("data-edit-item-id");
                }

                if(actionType == "edit-qty-through-forecast") {
                    var newManufacturingIndent = [
                        {
                            "mi_id": currentTr.find("td.mi-content").attr("data-mi-id"),
                            "oa_id": "",
                            "item_id": currentTr.find("td.item-content").attr("data-item-id"),
                            "item_name": currentTr.find("td.item-content").attr("data-item-name"),
                            "make_id": currentTr.find("td.item-content").attr("data-make-id"),
                            "quantity": currentTr.find(".quantity").val(),
                            "delivery_date": moment(currentTr.find("td.delivery-date").attr("data-default-date")).format("YYYY-M-D"),
                            "indent_type": indentType,
                            "purpose": currentTr.find(".purpose-content").text(),
                            "unit_name":currentTr.find(".item_unit_name").text(),
                            "alternate_unit_id": currentTr.find("td.qty-content").attr("data-alternate_unit_id"),
                        }
                    ]
                }
                else {
                    var alternate_unit_id = "";
                    if(actionType == "add-through-forecast"){
                        if($(".alternate_unit_select_box").is(":visible")){
                            alternate_unit_id = $("#id_material-alternate_units").val();
                        }
                    }
                    if(actionType == "edit-through-forecast") {
                        if($(".alternate_unit_select_box").is(":visible")){
                            alternate_unit_id = $(`#${idPrefix}id_material-alternate_units`).val();
                        }
                    }
                    var newManufacturingIndent = [
                        {
                            "mi_id": mi_id,
                            "oa_id": "",
                            "item_id": $(`#${idPrefix}material_id_hidden`).val(),
                            "item_name": $(`#${idPrefix}material_required`).val(),
                            "make_id": $(`#${idPrefix}make_id_hidden`).val(),
                            "quantity": $(`#${idPrefix}manufacturing_qty`).val(),
                            "delivery_date": moment($(`#${idPrefix}manufacturing_delivery_due`).val()).format("YYYY-M-D"),
                            "indent_type": indentType,
                            "purpose": $(`#${idPrefix}manufacturing_purpose`).val(),
                            "unit_name": $(`#${idPrefix}manufacturing_unit_display`).text(),
                            "alternate_unit_id":  alternate_unit_id
                        }
                    ]
                }
            }
            $("#loading").show();
            newManufacturingIndent = JSON.stringify(newManufacturingIndent);
            console.log("newManufacturingIndent", newManufacturingIndent)
            $.ajax({
                url: "/erp/production/save_manufacture_indent/",
                type: "post",
                datatype: "json",
                data: {'indent_dict': newManufacturingIndent } ,
                success: function (response) {
                    checkSessionExpired(response);
                    console.log("response", response.mi_details)
                    var make_name = constructDifferentMakeName(response.mi_details.make_name);
                    var itemDescription = response.mi_details.item_name;
                    if (make_name != ""){
                    itemDescription += " [" + make_name + "]";
                    }
                    response.mi_details.item_name =itemDescription;
                    var swal_msg = "";
                    var remove_row = `${response.mi_details.mi_id}_${response.mi_details.item_id}_${response.mi_details.make_id}`
                    var edit_item_name = $("#edit_manufacturing_indent .modal-title").attr("data-edit-item-id");
                    if(response.response_message.toLowerCase() == "success" && response.mi_details != false) {
                        if(typeof(oTable) !== 'undefined') {
                            oTable.destroy();
                        }
                        if(actionType != "add-through-forecast") {
                            swal_msg = "Manufacturing Indent has been updated successfully.";
                            if(actionType == "add-through-oa") {
                                $(current).closest("tr").remove();
                                var oaRelatedRow = $(`#mi_list tr[data-oa_id=${response.mi_details.oa_id}]`);
                                $.each(oaRelatedRow, function(details, i){
                                    $(this).find("td.mi-content").empty();
                                    $(this).find("td.mi-content").attr({"data-mi-id": response.mi_details.mi_id, "data-mi-no": response.mi_details.mi_no });
                                    var mi_row = `  <span class="table-inline-icon-bg-no hide">${response.mi_details.mi_no}<br>
                                                        <small>${response.mi_details.mi_date}</small>
                                                    </span>
                                                    <span class="table-inline-icon-container">
                                                        <span class="table-inline-icon remarks-icon" data-tooltip="tooltip" data-placement="left" title="" role="button" onclick="showRemarksModal(this)" data-original-title="Remarks">
                                                            <i class="fa fa-quote-right"></i>
                                                        </span>
                                                    <span>`;
                                    var update_button = `<button id="required_mi_qty" class="btn btn-save" data-tooltip="tooltip" title="Update Required Quantity" data-placement="top" onclick="createManufacturingIndent(this, 'oa', 'edit-qty-through-oa')">
                                                            <i class="fa fa-check" aria-hidden="true"></i>
                                                         </button>
                                                         <button class="btn btn-delete" data-tooltip="tooltip" title="Cancel" data-placement="top" onclick="restoreDefaultQuantity(this)">
                                                            <i class="fa fa-close" aria-hidden="true" style="color: #FFF;"></i>
                                                         </button>`;

                                    $(this).find("td.mi-content").html(mi_row);
                                    var currentTrId = $(this).attr("id");
                                    currentTrId = currentTrId.replace("null", response.mi_details.mi_id);
                                    $(this).attr("id", currentTrId);
                                    $(this).find(".qty_update_button").html(update_button);

                                });
                            }
                            else{
                                $(`tr#${remove_row}`).remove();
                            }
                        }
                        else{
                            swal_msg = "Manufacturing Indent has been created successfully.";
                        }
                        $(`tr#${edit_item_name}`).remove();
                        createMiListRow(response.mi_details, "new");
                        listTableHoverIconsInit("mi_list");
                        dataTableInit();
                        resetMIDetails();
                        $("#loading").hide();
                        swal({
                            title: '<span style="color: #44ad6b;">SUCCESS</span>',
                            text: `${swal_msg}<br /> <br />Manufacturing Indent No.: <b>${response.mi_details.mi_no}</b>`,
                            type: "success"
                        })
                        $(current).closest("tr").find(".qty_update_button").addClass('hide');
                        $("#edit_manufacturing_indent").modal("hide");
                    }
                    else {
                        swal("Failure", `Due to technical issue, unable to create Manufacturing Indent. <br />Please contact support.`, "warning")
                    }
                },
                error: function (xhr, errmsg, err) {
                    swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
                }
            });
        }
    }
}

function removeMiDetails(current, action){
    var oa_number = $(current).closest("tr").find(".oa-number").attr("data-oa-no");
    var add_mi_details = `<span><button class="btn btn-save" data-tooltip="tooltip" title="" data-placement="top" onclick="createManufacturingIndent(this, 'oa', 'add-through-oa')" data-original-title="Create Manufacturing Indent">
                               <i class="fa fa-plus" aria-hidden="true"></i>
                            </button>
                            <button class="btn btn-delete" data-tooltip="tooltip" title="" data-placement="top" onclick="removeMiDetails(this, 'remove')" data-original-title="Mark this Indent as Not Applicable">
                               <i class="fa fa-close" aria-hidden="true" style="color: #FFF;"></i>
                            </button></span>`
    var applicable_text = '';
    var confirm_text = '';
    if(action == 'add'){
        applicable_text = `"Do you want to make this OA Number <b>${oa_number}</b> as Applicable!"`;
    }
    else {
        applicable_text = `"Do you want to make this OA Number <b>${oa_number}</b> as Not Applicable!"`;
    }
    swal({
		title: "Are you sure?",
		text: applicable_text,
		type: "warning",
		showCancelButton: true,
		confirmButtonColor: "#209be1",
		confirmButtonText: "Yes, do it!",
		closeOnConfirm: true
	},
	function(){
        var currentTr = $(current).closest("tr");
        var removeMIDetails = [
            {
                "oa_id": currentTr.find("td.oa-content").attr("data-oa-id"),
                "item_id": currentTr.find("td.item-content").attr("data-item-id"),
                "make_id": currentTr.find("td.item-content").attr("data-make-id"),
                "quantity": "",
                "delivery_date": "",
                "indent_type": "",
                "purpose": "",
                "action": action
            }
        ]
        removeMIDetails = JSON.stringify(removeMIDetails);
        $.ajax({
            url: "/erp/production/manufacture_indent_remove/",
            type: "post",
            datatype: "json",
            async: false,
            data: {'indent_dict': removeMIDetails } ,
            success: function (response) {
                checkSessionExpired(response);
                if(action == "remove") {
                    $(current).closest("tr").addClass('hide');
                }
                else{
                    $(current).closest("tr").find("td.mi-content").html(add_mi_details);
                }
                if(response.response_message.toLowerCase() == "success") {
                    var messageType = (action == "add") ? "Removed": "Applied";
                    setTimeout(function(){
                        swal("SUCCESS", `MI Not Applicable ${messageType} successfully`, "success");
                    },250);
                }
                TooltipInit();
            },
            error: function (xhr, errmsg, err) {
                swal({title: "", text: "Unexpected error! Please contact support.", type: "error"});
            }
        });
	});
}
var oTable;
var oSettings;
function dataTableInit(){
    oTable = $('#mi_list').DataTable({
        fixedHeader: true,
        "pageLength": 50,
        "bSortCellsTop": true,
        "order": [ 5, "asc" ],
        "search": {
            "smart": false
        },
        "columns": [
            null,null,null,null,
            { "type": "date" },
            null,null,
            null,null,null,null,null,null,null,null,null,null
        ]
    });
    oTable.on("draw",function() {
        var keyword = $('#mi_list_filter > label:eq(0) > input').val();
        $('#mi_list').unmark();
        $('#mi_list').mark(keyword,{});
    });
    oTable.on('page.dt', function() {
      $('html, body').animate({
        scrollTop: $(".dataTables_wrapper").offset().top - 15
       }, 'slow');
    });
    oSettings = oTable.settings();
}

function checkSessionExpired(xhr) {
    if(typeof xhr === 'string') {
        if (xhr.indexOf("Your session has expired") != -1){
            location.reload();
        }
    }
}