"""

"""
import re
from datetime import datetime
from deepdiff import DeepDiff

from erp.accounts import logger
from erp.helper import getUser
from settings import SQLASession
from util.api_util import response_code
from util.changelog import ChangeLog
from collections import OrderedDict

__author__ = 'charlesmichel'


class SalesEstimateChangelog(ChangeLog):
	"""
	Changelog to do mongodb operations
	"""
	material = {}
	collection = 'sales_estimate'
	uid = "se_id"

	def __init__(self):
		self.db_session = SQLASession()
		super(SalesEstimateChangelog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.module_data_set = {
			'notes': 'Notes', 'ref_date': 'Ref Date', 'ref_no': 'Ref No', 'project': 'Project', 'expiry_date': 'Expiry Date', 'status': 'Status',
			'payment_terms': 'Payment Terms', 'party': 'Party', 'currency_conversion_rate': 'Currency Conversion Rate',
			'special_instructions': 'Special Instructions', 'currency': 'Currency', 'type': 'Type', 'purpose': 'Purpose',
			'items': {'title': 'Material', 'name': 'Name', 'hsn': 'HSN / SAC CODE', 'qty': 'Qty', 'unit_price': 'Unit Price', 'discount': 'Discount'},
			'taxes': {'title': 'Taxes', 'name': 'Name', 'rate': 'Net Rate'},
			'tags': {'title': 'Tag', 'tag': 'Name'},
			'remarks': {'title': 'Remarks', 'remark': 'Description', 'date': 'Date', 'by': 'By'},
			'contacts': {'title': 'Sales Person', 'name': 'Name'},
			'round_off': 'Round Off'
			}
		self.status = {0: 'draft', -1: 'rejected', 2: 'approved', 3: 'client approved', 4: 'client rejected'}
		logger.info('Sales Estimate Changelog module initiated :)')


	def buildInsertDataStructure(self, user=None, data=None):
		"""
		Build/formulate material data to insert on mongoDB
		:param user:
		:param data:
		:return:
		"""
		response = dict()
		response['tags'] = []
		response['contacts'] = []
		response['items'] = []
		response['remarks'] = []
		response['taxes'] = []
		response['modified_at'] = datetime.now()
		try:
			for tag in data.tags:
				item = dict()
				item['tag'] = str(tag.tag.tag)
				response['tags'].append(item)
			response['party'] = data.party.name
			response['project'] = data.projects.name
			response['type'] = data.type
			response['purpose'] = data.purpose
			response['round_off'] = float(data.round_off)
			for contact in data.se_contacts:
				item = dict()
				item['name'] = str(contact.contact.contact.name)
				response['contacts'].append(item)
			response['username'] = [user.first_name, user.last_name]
			response['expiry_date'] = str(data.expiry_date)
			response['ref_no'] = str(data.ref_no)
			response['ref_date'] = str(data.ref_date)
			for material in data.items:
				item = dict()
				item['name'] = str(material.item.name)
				item['hsn'] = str(material.hsn_code)
				item['qty'] = int(material.quantity)
				item['unit_price'] = str(material.unit_rate)
				item['discount'] = float(material.discount)
				response['items'].append(item)
			response['currency'] = str(data.currency)
			response['currency_conversion_rate'] = str(data.currency_conversion_rate)
			response['payment_terms'] = str(data.payment_terms)
			response['status'] = self.status[int(data.status)]
			for remark in data.remarks:
				item = dict()
				item['remark'] = remark['remarks']
				item['date'] = remark['date']
				item['by'] = remark['by']
				response['remarks'].append(item)
			response['special_instructions'] = str(data.special_instructions)
			response['notes'] = str(data.notes)
			for tax in data.taxes:
				item = dict()
				item['code'] = str(tax.tax.code)
				item['name'] = str(tax.tax.name)
				item['rate'] = float(tax.tax.net_rate)
				response['taxes'].append(item)

		except Exception as e:
			logger.exception(e)
		return response

	def queryInsert(self, user_id=None, enterprise_id=None, data=None):
		"""
		:param user_id:
		:param enterprise_id:
		:param data:
		:return:
		"""
		response = {}
		try:
			user = getUser(enterprise_id=enterprise_id, user_id=user_id)
			formatted_data = self.buildInsertDataStructure(user=user, data=data)
			response = self.insert(id=data.id, enterprise_id=enterprise_id, data=formatted_data)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, se_id=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param se_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(se_id), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for material in query:
				material['created_at'] = str(material['created_at'])
				material['log']['modified_at'] = str(material['log']['modified_at'])
				result.append(material)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, se_id=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param se_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(se_id), enterprise_id=int(enterprise_id), identifier=modified_at)
			for voucher in query:
				del voucher['log']['modified_at']
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True)
				if 'iterable_item_added' in diff:
					for item_added in diff['iterable_item_added'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(item_added[1][i]) == list else str(item_added[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append('%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))

				if 'iterable_item_removed' in diff:
					for item_removed in diff['iterable_item_removed'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(
									str(l_data) for l_data in item_removed[1][i]) if type(
									item_removed[1][i]) == list else str(item_removed[1][i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append(
								'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						self.response_log.append('<b>%s</b> of ledger <b>%s</b> has changed to <b>%s</b>' % (var_chg[1].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				if type(title) == dict:
					if key == 'makes':
						for item in new[key]:
							log = '%s <b>%s</b> added with MPN <b>%s</b><br />' % (self.module_data_set[key]['title'], str(item['make']),str(item['part_no']))
							self.response_log.append('%s' % log)
					else:
						for item in new[key]:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(item[i]) == list else str(item[i])) if i is not 'title' else ''
							self.response_log.append('%s' % log)
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return False

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log


class InvoiceChangeLog(ChangeLog):
	material = {}
	collection = 'invoice'
	uid = "inv_id"

	def __init__(self):

		super(InvoiceChangeLog, self).__init__(collection=self.collection, uid=self.uid)
		self.response_log = []
		self.type = {'GST': 'Tax Invoice', 'Trading': 'Trading', 'Service': 'Service/Labour', 'BoS': 'Bill Of Supply', 'Excise': 'Excise',
		             'DC': 'DC', 'JDC': 'JDC', 'JIN': 'JIN', 'Issue': 'Issue'}
		self.module_data_set = dict()
		common = OrderedDict()
		common['project_name'] = 'Project'
		common['reverse_charge'] = 'Reverse Charge'
		common['goods_supplied'] = 'Goods Supplied'
		common['invoice_type'] = 'Type'
		common['party_name'] = 'Party'
		common['sales_account_name'] = 'Sales Account Name'
		common['customer_name'] = 'Customer Name'
		common['customer_address'] = 'Customer Address'
		common['gstin'] = 'GST No'
		common['e_gstin'] = 'E Commerce GST No'
		common['po_no'] = 'PO NO'
		common['po_date'] = 'PO Date'
		common['invoice_no'] = 'Invoice No'
		common['issued_date'] = 'Issued Date'
		common['approved_date'] = 'Approved Date'
		common['transport_mode'] = 'Transport Mode'
		common['road_permit'] = 'Road Permit'
		common['lrn&dt'] = 'LR No and Date'
		common['pckg_slp_no'] = 'Package Slip No'
		common['pkg_des'] = 'Package Description'
		self.module_data_set = common
		items = OrderedDict()
		items['title'] = 'Material'
		items['oa_number'] = 'OA NO'
		items['dc_number'] = 'DC NO'
		items['item_name'] = 'Name'
		items['hsn_code'] = 'HSN / SAC CODE'
		items['quantity'] = 'Qty'
		items['unit_rate'] = 'Unit Price'
		items['discount'] = 'Discount'
		items['CGST_Rate'] = 'CGST Rate'
		items['SGST_Rate'] = 'SGST Rate'
		items['IGST_Rate'] = 'IGST Rate'
		items['remarks'] = 'Remarks'
		items['faulty'] = 'Faulty'
		qir = OrderedDict()
		qir['title'] = 'QIR'
		qir['id'] = "ID"
		qir['quantity'] = 'Quantity'
		qir['remarks'] = 'Remarks'
		items['QIR'] = qir
		self.module_data_set['items'] = items
		pkct_fwd = OrderedDict()
		pkct_fwd['title'] = 'Packing & Forwarding'
		pkct_fwd['hsn'] = 'HSN / SAC CODE'
		pkct_fwd['rate'] = 'Unit Price'
		pkct_fwd['discount'] = 'Discount'
		pkct_fwd['CGST_Rate'] = 'CGST Rate'
		pkct_fwd['SGST_Rate'] = 'SGST Rate'
		pkct_fwd['IGST_Rate'] = 'IGST Rate'
		self.module_data_set['pkct_fwd'] = pkct_fwd
		trns_frit = OrderedDict()
		trns_frit['title'] = 'Packing & Forwarding'
		trns_frit['hsn'] = 'HSN / SAC CODE'
		trns_frit['rate'] = 'Unit Price'
		trns_frit['discount'] = 'Discount'
		trns_frit['CGST_Rate'] = 'CGST Rate'
		trns_frit['SGST_Rate'] = 'SGST Rate'
		trns_frit['IGST_Rate'] = 'IGST Rate'
		self.module_data_set['trns_frit'] = trns_frit
		taxes = OrderedDict()
		taxes['title'] = 'Taxes'
		taxes['name'] = 'Name'
		taxes['rate'] = 'Net Rate'
		self.module_data_set['taxes'] = taxes
		remarks = OrderedDict()
		remarks['title'] = 'Remarks'
		remarks['remark'] = 'Description'
		remarks['by'] = 'By'
		remarks['date'] = 'Date'
		self.module_data_set['remarks'] = remarks
		cmn = OrderedDict()
		cmn['currency'] = 'Currency'
		cmn['currency_conversion_rate'] = 'Currency Conversion Rate'
		cmn['payment_terms'] = 'Payment Terms'
		cmn['special_instruction'] = 'Special Instructions'
		cmn['notes'] = 'Notes'
		cmn['round_off'] = 'Round Off'
		cmn['grand_total'] = 'Grand Total'
		self.module_data_set.update(cmn)

	def buildInsertDataStructure(self, user=None, data=None):
		response = dict()
		response['username'] = [user.first_name, user.last_name]
		response['modified_at'] = datetime.now()
		response['items'] = []
		response['taxes'] = []
		response['remarks'] = []
		response['pkct_fwd'] = []
		response['trns_frit'] = []
		for item in data.items:
			temp = dict()
			temp['item_name'] = str(item.item.name)
			temp['hsn_code'] = str(item.hsn_code)
			temp['quantity'] = int(item.quantity)
			temp['unit_rate'] = float(item.rate)
			temp['discount'] = float(item.discount)
			temp['faulty'] = "Checked" if str(item.is_faulty) == "True" else 'UnChecked'
			for i in range(0, len(item.taxes)):
				if str(item.taxes[i].tax.type) == "CGST":
					temp['CGST_Rate'] = str(item.taxes[i].tax.net_rate)
				if str(item.taxes[i].tax.type) == "SGST":
					temp['SGST_Rate'] = str(item.taxes[i].tax.net_rate)
				if str(item.taxes[i].tax.type) == "IGST":
					temp['IGST_Rate'] = str(item.taxes[i].tax.net_rate)
			temp['remarks'] = str(item.remarks)
			if str(item.oa_no) != "None" and data.goods_already_supplied == False:
				temp['oa_number'] = str(item.inv_oa.getSimpleCode())
			if str(item.delivered_dc_id) != "None" and data.goods_already_supplied == True:
				temp['dc_number'] = str(item.delivered_dc.getSimpleCode())
			temp['QIR'] = []
			for ins in item.inspection_log:
				t_dict = OrderedDict()
				t_dict['id'] = str(ins['sample_no'])
				t_dict['quantity'] = str(ins['quantity'])
				t_dict['remarks'] = str(ins['remarks'])
				temp['QIR'].append(t_dict)
			response['items'].append(temp)
		for remark in data.remarks:
				item = dict()
				item['remark'] = remark['remarks']
				item['date'] = remark['date']
				item['by'] = remark['by']
				response['remarks'].append(item)
		for i in range(0, len(data.taxes)):
			item = dict()
			item['name'] = str(data.taxes[i].tax.name)
			item['rate'] = str(data.taxes[i].tax.net_rate)
			response['taxes'].append(item)

		for k in range(0, 2):
			if len(data.charges) > 0 and str(data.charges[k].hsn_code) != '':
				temp = dict()
				temp['hsn'] = str(data.charges[k].hsn_code)
				temp['discount'] = str(data.charges[k].discount)
				temp['rate'] = str(data.charges[k].rate)
				for i in range(0, len(data.charges[k].taxes)):
						if str(data.charges[k].taxes[i].tax.type) == "CGST":
							temp['CGST_Rate'] = str(data.charges[k].taxes[i].tax.net_rate)
						if str(data.charges[k].taxes[i].tax.type) == "SGST":
							temp['SGST_Rate'] = str(data.charges[k].taxes[i].tax.net_rate)
						if str(data.charges[k].taxes[i].tax.type) == "IGST":
							temp['IGST_Rate'] = str(data.charges[k].taxes[i].tax.net_rate)
				if str(data.charges[k].item_name) == "Packing & Forwarding":
					response['pkct_fwd'].append(temp)
				else:
					response['trns_frit'].append(temp)
		response['currency_conversion_rate'] = float(data.currency_conversion_rate)
		response['currency'] = str(data.currency.name)
		response["party_name"] = str(data.customer.name) if data.customer else ""
		response["project_name"] = str(data.project.name) if data.project else ""
		response['sales_account_name'] = str(data.sale_account_ledger)
		response['invoice_type'] = self.type[str(data.type)]
		response['reverse_charge'] = "Checked" if str(data.tax_payable_on_reverse_charge) == "True" else "UnChecked"
		response['goods_supplied'] = "Checked" if str(data.goods_already_supplied) == "True" else "UnChecked"
		response['customer_name'] = str(data.ship_to_name)
		response['customer_address'] = str(data.deliver_to)
		response['gstin'] = str(data.gstin)
		response['e_gstin'] = str(data.ecommerce_gstin)
		response['po_no'] = str(data.po_no) if data.po_no != None else ""
		response['po_date'] = str(data.po_date) if data.po_date != None else ""
		response['issued_date'] = data.issued_on.strftime("%m/%d/%Y, %H:%M:%S") if data.issued_on != None else ""
		response['approved_date'] = data.approved_on.strftime("%m/%d/%Y, %H:%M:%S") if data.approved_on != None else ""
		response['transport_mode'] = str(data.transport_mode) if data.transport_mode != None else ""
		response['road_permit'] = str(data.road_permit_no) if data.road_permit_no != None else ""
		response['lrn&dt'] = str(data.lr_no) if data.lr_no != None else ""
		response['invoice_no'] = str(data.getSimpleCode())
		response['pckg_slp_no'] = str(data.packing_slip_no) if data.packing_slip_no != None else ""
		response['pkg_des'] = str(data.packing_description) if data.packing_description != None else ""
		response['notes'] = str(data.notes)
		response['payment_terms'] = str(data.payment_terms)
		response['special_instruction'] = str(data.special_instruction)
		response['round_off'] = float(data.round_off)
		response['grand_total'] = float(data.grand_total)
		return response

	def queryInsert(self, userid=None, enterprise_id=None, data=None):
		response = {}

		try:
			user = getUser(enterprise_id=enterprise_id, user_id=userid)
			formatted_data = self.buildInsertDataStructure(user=user, data=data)
			response = self.insert(id=int(data.id), enterprise_id=enterprise_id, data=formatted_data)
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogList(self, se_id=None, enterprise_id=None, offset=None, limit=None):
		"""
		get listed voucher log data
		:param se_id:
		:param enterprise_id:
		:return:
		"""
		response = {}
		result = []
		try:
			query = self.fetchLog(id=int(se_id), enterprise_id=int(enterprise_id), offset=offset, limit=limit)
			for material in query:
				material['created_at'] = str(material['created_at'])
				material['log']['modified_at'] = str(material['log']['modified_at'])
				result.append(material)
			response = response_code.success() if len(result) > 0 else response_code.failure()
			response['data'] = result
		except Exception as e:
			logger.exception(e)
		return response

	def fetchLogData(self, se_id=None, enterprise_id=None, modified_at=None):
		"""
		get individual log data
		:param se_id:
		:param enterprise_id:
		:param modified_at:
		:return:
		"""
		response = response_code.failure()
		result = []
		try:
			query = self.fetchLogDetails(id=int(se_id), enterprise_id=int(enterprise_id), identifier=modified_at)
			for voucher in query:
				del voucher['log']['modified_at']
				result.append(voucher)
			response = response_code.success() if len(result) > 0 else response
			if len(result) > 1:
				response['data'] = self.buildLogData(result[1]['log'], result[0]['log'])
			elif len(result) > 0:
				response['data'] = self.buildLogData(None, result[0]['log'])
			response['data'] = response['data'] if len(response['data']) > 0 else ['No change detected']
		except Exception as e:
			logger.exception(e)
			response = response_code.failure()
		return response

	def buildLogData(self, old=None, new=None):
		"""

		:param old:
		:param new:
		:return:
		"""
		for key, title in self.module_data_set.items():
			self.fieldLogData(key=key, title=title, old=old, new=new)
		return self.response_log

	def fieldLogData(self, key=None, title=None, old=None, new=None):
		"""

		:param key:
		:param title:
		:param old:
		:param new:
		:return:
		"""
		if old is not None and key in old:
			if type(new[key]) == list and type(old[key]) == list:
				diff = DeepDiff(old[key], new[key], ignore_order=True)
				if 'iterable_item_added' in diff:
					for item_added in diff['iterable_item_added'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								if i != 'QIR':
									log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_added[1][i]) if type(item_added[1][i]) == list else str(item_added[1][i])) if len(item_added) > 1 and i in item_added[1] and i is not 'title' else ''
								elif item_added[1].get('QIR') != None:
									for qir in item_added[1]['QIR']:
										log += '<br /> <b>%s</b> added with following values: <br />' % self.module_data_set[key]['QIR']['title']
										for j, ele in self.module_data_set[key]['QIR'].items():
											log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append('%s named <b>%s</b> was added' % (self.module_data_set[key], item_added[1]))

				if 'iterable_item_removed' in diff:
					for item_removed in diff['iterable_item_removed'].items():
						if type(self.module_data_set[key]) is not str:
							log = '<b>%s</b> removed with following values: <br />' % self.module_data_set[key]['title']
							for i, elem in self.module_data_set[key].items():
								if i != 'QIR':
									log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item_removed[1][i]) if type(item_removed[1][i]) == list else str(item_removed[1][i])) if len(item_removed) > 1 and i in item_removed[1] and i is not 'title' else ''
								elif item_removed[1].get('QIR') != None:
									for qir in item_removed[1]['QIR']:
										log += '<br /> <b>%s</b> removed with following values: <br />' % self.module_data_set[key]['QIR']['title']
										for j, ele in self.module_data_set[key]['QIR'].items():
											log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
							self.response_log.append('%s' % log)
						else:
							self.response_log.append(
								'%s named <b>%s</b> was removed' % (self.module_data_set[key], item_removed[1]))
				if 'values_changed' in diff:
					for item in diff['values_changed'].keys():
						var_chg = re.findall("\[(.*?)\]", item.replace('\'', ''))
						self.response_log.append('<b>%s</b> of ledger <b>%s</b> has changed to <b>%s</b>' % (var_chg[1].capitalize(), old[key][int(var_chg[0])][var_chg[1]], new[key][int(var_chg[0])][var_chg[1]]))
			else:
				diff = DeepDiff(old[key], new[key])
				if 'values_changed' in diff:
					if str(diff['values_changed']['root']['old_value']) == '' and str(
							diff['values_changed']['root']['new_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> added' % (title, diff['values_changed']['root']['new_value']))
					elif str(diff['values_changed']['root']['new_value']) == '' and str(
							diff['values_changed']['root']['old_value']) != '':
						self.response_log.append(
							'%s <b>%s</b> removed' % (title, diff['values_changed']['root']['new_value']))
					else:
						self.response_log.append('%s changed from <b>%s</b> to <b>%s</b>' % (
							title,
							diff['values_changed']['root']['old_value'],
							diff['values_changed']['root']['new_value']))
		elif key in new:
			if type(new[key]) == list:
				valid_od = type(OrderedDict())
				if type(title) == dict or type(title) == valid_od:
					for item in new[key]:
						log = '<b>%s</b> added with following values: <br />' % self.module_data_set[key]['title']
						for i, elem in self.module_data_set[key].items():
							if i != 'QIR':
								log += ' <b>%s</b>: <i>%s</i>' % (elem, ', '.join(str(l_data) for l_data in item[i]) if type(item[i]) == list else str(item[i])) if i is not 'title' and i in item else ''
							elif item.get('QIR') != None:
								for qir in item['QIR']:
									log += '<br /> <b>%s</b> added with following values: <br />' % self.module_data_set[key]['QIR']['title']
									for j, ele in self.module_data_set[key]['QIR'].items():
										log += ' <b>%s</b>: <i>%s</i>' % (ele, ', '.join(str(l_data) for l_data in qir[j]) if type(qir[j]) == list else str(qir[j])) if j is not 'title' and j in qir else ''
						self.response_log.append('%s' % log)
				else:
					self.response_log.append('%s <b>%s</b> added' % (title, ', '.join(str(l_data) for l_data in new[key])))
			elif new[key] != '':
				self.response_log.append('%s <b>%s</b> added' % (title, new[key]))
		logger.info('tags LOG DATA updated %s' % self.response_log)
		return False

