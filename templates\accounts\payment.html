{% extends "accounts/sidebar.html" %}
{% block payment %}
 <style type="text/css">
 	#party_basic_details td {
 		font-size: 16px;
 		font-weight: bold;
 	}

 	.table .table_text_box,
	.table td.table_text_box input[type="text"],
	.table.tableWithText td input[type="text"],
	.table.tableWithText td select {
		padding: 4px 6px;
		height: 26px;
		box-shadow: 0 0;
		font-size: 12px;
	}

	.loading-main-container {
    	display: block;
    }
 </style>
<link rel="stylesheet" href="/site_media/css/bootstrap-toggle.min.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-tour.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-tour.js?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-toggle.min.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/payment.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/tags.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/mailer.js?v={{ current_version }}"></script>


<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Bill Settlements</span>
	</div>
	<div class="container-fluid" style="margin-top: 15px; padding: 0;">
		<div class="col-sm-12">
			<input type="hidden" name="bill_settlement" id="id_party_email" value="">
			<input type="hidden" name="bill_settlement" id="id_party_name">
			<input type="hidden" name="bill_settlement" id="id_ledger_id" value="">
			<input type="hidden" name="bill_settlement" id="id_ledger_name" value="">
			<input type="hidden" name="bill_settlement" id="id_mail_subject" value="">
			<input type="hidden" name="bill_settlement" id="id_ledger_mail" value="">
			<div class="hide">
				<input type="hidden" name="bill_settlement" id="bill_settlement_flag" value="1">
				<input type="checkbox" id="payment_type" class="hide" checked="checked" />
			</div>
			<ul class="nav nav-tabs" id="bill-switch">
			  <li id="bill_payables" data-toggle="payable" data-title= "1" class="active"><a data-toggle="tab" role="button"><h5>PAYABLES</h5></a></li>
			  <li id="bill_receivables" data-toggle="receivable" data-title="0"><a data-toggle="tab" role="button"><h5>RECEIVABLES</h5></a></li>
			</ul>
			<div class="row bill-settlement-contianer" style="border: solid 1px #ddd; border-top: none; display: block; margin: 0;padding-bottom: 15px;">
				<div class="col-sm-6 hide" id="div_payment_debtors" style="max-width: 500px; margin-top: 10px;">
					<label class="party_selector">ACCOUNT</label><span class="mandatory_mark"> *</span>
					<select id="payment_debtors">
						<option value="0">--Select Ledger--</option>
						{% for ledger in debtors %}
							<option value="{{ ledger.0 }}">{{ ledger.1 }}</option>
						{% endfor %}
					</select>
				</div>
				<div class="col-sm-6" id="div_payment_creditors" style="max-width: 500px; margin-top: 10px;">
					<label class="party_selector">ACCOUNT</label><span class="mandatory_mark"> *</span>
					<select id="payment_creditors">
						<option value="0">--Select Ledger--</option>
						{% for ledger in creditors %}
							<option value="{{ ledger.0 }}">{{ ledger.1 }}</option>
						{% endfor %}
					</select>
				</div>
				<div class="col-sm-6 party_ageing_details hide" style="margin-top: 15px; float: right; margin-bottom: 5px;">
					<table border="0" class="side-table">
						<tr>
							<td class="side-header text-center" rowspan="2">Ageing</td>
							<td class="side-header text-center">0-30 DAYS</td>
							<td class="side-header text-center">31-60 DAYS</td>
							<td class="side-header text-center">61-90 DAYS</td>
							<td class="side-header text-center">>90 DAYS</td>
						</tr>
						<tr>
							<td class="amt_ageing_1 text-right">0.00</td>
							<td class="amt_ageing_2 text-right">0.00</td>
							<td class="amt_ageing_3 text-right">0.00</td>
							<td class="amt_ageing_4 text-right">0.00</td>
						</tr>
					</table>
					<div class="col-sm-12 form-group remove-padding" style="margin-top: 15px;" >
						<input type="button" data-toggle="modal" data-target="#outstanding-settle-modal" id="settle_outstanding" class="btn btn-save pull-right" value="Settle Outstanding" />
					</div>
				</div>
				<div class="col-sm-12 bill_outstanding_summary remove-padding hide">
						<div class="col-sm-6"></div>
						<div class="col-sm-4" style="">
							<label>Date Range</label>
							<div id="reportrange" class="report-range form-control">
								<i class="glyphicon glyphicon-calendar"></i>&nbsp;
								<span></span> <b class="caret"></b>
								<input type="hidden" class="fromdate" id="from_date"  name="from_date" value="{{from_date}}"/>
								<input type="hidden" class="todate" id="to_date"   name="to_date" value="{{to_date}}"/>
							</div>
						</div>
						<div class="col-sm-2 pull-right" style="text-align:right;margin-top:18px;">
							<button class="btn transparent-btn" id="id_print_details" data-tooltip="tooltip" title="Print Payment Advice" style="margin-right:10px;"><i class="fa fa-print" aria-hidden="true" data-tooltip="tooltip" style="font-size: 18px;"></i></button>
							<button id="id_download_csv_file" class="btn btn-add-new  export_csv" data-tooltip="tooltip" title="Download receipt Acknowledgement as CSV"><i class="fa fa-download" aria-hidden="true"></i></button>
						</div>
						<div class="hide">
							<input type="button" id="id_csv_download_name" value="" />
						</div>
					<div class="col-sm-12">
						<table class="table custom-table table-bordered table-striped tableWithText" id="bill_past_settlement" style="width: 100%;">
							<thead>
								<tr>
									<th>BILL NO</th>
									<th>BILL DATE</th>
									<th>VALUE</th>
									<th>AMOUNT SETTLED</th>
									<th>OUTSTANDING</th>
									<th>DATE</th>
									<th>TXN No</th>
									<th>TXN VALUE</th>
								</tr>
							</thead>
							<tbody> </tbody>
						</table>

						<div id="id_bill_past_settlement_pdf">
							<table class="table custom-table table-bordered table-striped tableWithText bill_past_settlement_pdf hide" id="bill_past_settlement_pdf" style="width: 100%;">
								<thead>
									<tr>
										<th>BILL NO</th>
										<th>BILL DATE</th>
										<th>VALUE</th>
										<th>AMOUNT SETTLED</th>
										<th>OUTSTANDING</th>
									</tr>
								</thead>
								<tbody> </tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-6">
				<div style="width: calc(100% - 190px); margin-left: 15px; float: left;">
					<div class="col-sm-12 form-group hide" style="padding: 0;" id="tags"><!-- Tags are hidden on the 2.16.3 release -->
						<ul id="ul-tagit-display" class="tagit-display form-control">
							{% for tag_form in tags_formset.initial_forms %}
								<li class="li-tagit-display" id="{{ tag_form.prefix }}">
									<div hidden="hidden">{{ tag_form.tag }}
										{{ tag_form.DELETE }}{{ tag_form.ORDER }}</div>
									<label class="label-tagit-display"  id="id_{{tag_form.prefix}}-tag_label">{{ tag_form.tag.value }}</label>&nbsp;
									<a class="delete_tag" id="{{tag_form.prefix}}-del_button"></a>
									&nbsp;
								</li>
							{% endfor %}
							<li id="tag-__dummy__" hidden="hidden">
								<div hidden="hidden">{{ tags_formset.empty_form.tag }}
								{{ tags_formset.empty_form.ORDER }}
								{{ tags_formset.empty_form.DELETE }}</div>
								<label class="label-tagit-display"  id="id_{{tags_formset.empty_form.prefix}}-tag_label"></label>&nbsp;
								<a class="delete_tag" id="{{tags_formset.empty_form.prefix}}-del_button"></a>
								&nbsp;
							</li>

							<span>
								{{ tags_formset.management_form }}
								<span id="{{ tags_formset.empty_form.prefix }}" class="text_for_tag" >
									{{ tags_formset.empty_form.tag }}
									{{ tags_formset.empty_form.ORDER }}
									<span hidden="hidden">{{ tags_formset.empty_form.DELETE }}</span>
								</span>
							</span>
						</ul>
						<span style="position: absolute; margin-top: -8px; color: #aaa; font-size: 11px;">Type and press enter to add new tag.</span>
					</div>
					<div class="col-sm-2 hide" style="margin-top: 21px;">
						<input id="add_tag" class="btn btn-save btn-margin-1" value="+" type="button">
					</div>
				</div>
			</div>
		</div>

		<!-- Bill modals and JS are segregated -->
		{% include "accounts/bills.html" %}
		<div style="display:none;"><span id="id-advance_particulars_json"></span><span id="id-billable_particulars_json"></span></div>
	</div>
</div>
<div id="outstanding-settle-modal" class="modal fade" role="dialog">
  	<div class="modal-dialog" style="width: 85%;">
	    <div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
        		<h4 class="modal-title">Settle Outstanding</h4>
      		</div>
      		<div class="modal-body">
        		<div class="col-sm-12 form-group">
        			<div class="col-sm-6 remove-padding">
    					<div class="col-sm-6 form-group">
							<label>Amount<span class="mandatory_mark"> *</span></label>
							<input name="amount" placeholder="Enter Amount" type="text" class="form-control"
							       id="id_payment_amt" value="0.00" maxlength="16" onfocus="setNumberRangeOnFocus(this,13,2)" onblur="autoFillPayment()">
						</div>
						<div class="col-sm-6 form-group">
							<label class="payment_selector">Paid from</label><span class="mandatory_mark"> *</span>
							<select id="fund_ledgers">
								<option value="0">--Select Ledger--</option>
								{% for ledger in fund_ledgers %}
									<option value="{{ ledger.0 }}">{{ ledger.1 }}</option>
								{% endfor %}
							</select>
						</div>
						<div class="col-sm-6 form-group">
							<label>Date<span class="mandatory_mark"> *</span></label>
							<input name="payment_date" placeholder="Enter Date" type="text" class="form-control daterange-single hide" id="id_payment_date">
							<div id="payment_date" class="report-date form-control">
								<i class="glyphicon glyphicon-calendar"></i>&nbsp;
								<span></span> <b class="caret"></b>
							</div>
						</div>
						<div class="col-sm-6 form-group">
							<label>Instrument No.</label>
							<input name="instrument_no" placeholder="DD No./Cheque No/RTGS ref.No" type="text" class="form-control" id="id-instrument_no" maxlength="20">
						</div>
    				</div>
        			<div class="col-sm-6 party_ageing_details hide" id="id_ageing_table" style="margin-top: 15px; margin-bottom: 5px;">
						<table class="table custom-table table-bordered  side-table" style="width: 100%;">
							<tr>
								<td class="side-header text-center" rowspan="2" style="text-align:center;">Ageing</td>
								<td class="side-header text-center" style="text-align:center;">0-30 DAYS</td>
								<td class="side-header text-center" style="text-align:center;">31-60 DAYS</td>
								<td class="side-header text-center" style="text-align:center;">61-90 DAYS</td>
								<td class="side-header text-center" style="text-align:center;">>90 DAYS</td>
								<td class="side-header text-center advance_outstanding_pdf hide" rowspan="2" style="text-align:center;">Advances Outstanding</td>
								<td class="advance_outstanding_value text-right advance_outstanding_pdf hide" rowspan="2" >0.00</td>
							</tr>
							<tr>
								<td class="amt_ageing_1 text-right">0.00</td>
								<td class="amt_ageing_2 text-right">0.00</td>
								<td class="amt_ageing_3 text-right">0.00</td>
								<td class="amt_ageing_4 text-right">0.00</td>
							</tr>
						</table>
					</div>
				</div>

				<div class="col-sm-12 party_details_header hide">
					<div class="col-sm-6 text-left">
						<label>Party Name: <span class="mycolor-blue-2" style="font-size: 16px;" id="party_name"></span></label>
					</div>
					<div class="col-sm-6 text-right">
						<button class="btn transparent-btn pull-right" id="id_print_outstanding_bill"><i class="fa fa-print" aria-hidden="true" data-tooltip="tooltip" title="Print" style="font-size: 18px;"></i></button>
						<label style="margin-right: 12px;margin-top: 7px;">Ledger Balance: <span class="mycolor-blue-2" style="font-size: 16px; text-transform: capitalize;" id="party_ledger_balance"></span></label>
					</div>
				</div>
				<div class="col-sm-12" style="margin-top: 3px;">
					<div class="col-sm-12 party-bill-details-container" id="id_party-bill-details-container">
						<table class="table custom-table table-bordered table-striped tableWithText" id="party_bill_details" style="width: 100%;">
							<thead>
								<tr>
									<th style="width: 3%;">S.No</th>
									<th style="width: 7%;">Bill No.</th>
									<th style="width: 6%;">Bill Date</th>
									<th style="width: 8%;">
										Ageing (Days)
										<small style="text-align: right; display: block; font-size: 70%;"><span class="mycolor-blue-2" id="party_cr_period" style="font-size: 11px;">365</span> Cr. Days</small>
									</th>
									<th style="width: 5%;">Bill Value</th>
									<th style="width: 8%;">Already Settled<br/>
										<small style="text-align: right; display: block; font-size: 70%;">In Excess: <span class="mycolor-blue-2 box-clickable" style="font-size: 12px;" id="unsettled_excess" role="button">0.00</span></small></th>
									<th style="width: 8%;">Pending Amount<br/>
										<small style="text-align: right; display: block; font-size: 70%;">Un-billed: <span class="mycolor-blue-2" style="font-size: 12px;" id="unsettled_billable" role="button">0.00</span></small></th>
									<th style="width: 8%; max-width: 160px; padding-bottom: 15px;" class="textbox_header_amt_md outstanding_pdf">
										<a role="button" style="margin-top: -3px;" class="pull-right reset_adv_amt" data-tooltip="tooltip" data-placement="left" title="Clear this column"><i class="fa fa-undo"></i></a>
										<span class="amount_header">Paid</span> in Advance<br />
										<small style="text-align: right; display: block; font-size: 70%;">Balance : <span class="mycolor-blue-2" style="font-size: 12px;" id="unsettled_advance" role="button">0.00</span></small>
									</th>
									<th style="width: 8%;" class="textbox_header_amt_md outstanding_pdf">
										<span class="amount_header">Paid</span> Now<br/>
										<a role="button" style="margin-top: -17px;" class="pull-right reset_pay_now" data-tooltip="tooltip" data-placement="left" title="Clear this column"><i class="fa fa-undo"></i></a>
									</th>
								</tr>
							</thead>
							<tbody> </tbody>
						</table>
					</div>
					<div class="col-sm-6 form-group">
						<label>Narration</label>
						<textarea name="narration" rows="3" placeholder="Enter Narration" class="form-control" id="id-narration" maxlength="300"></textarea>
					</div>

				</div>
      		</div>
      		<div class="modal-footer">
				{% if access_level.edit %}
					<input type="button" id="create_payment_voucher" class="btn btn-save pull-right hide" value="Create Voucher" />
					<input type="button" id="bill_settlement" class="btn btn-save pull-right" value="Settle Bill" />
				{% else %}
					<input type="button" id="disabled_payment" class="btn btn-save pull-right hide" value="Create Voucher" style="opacity: 0.7; cursor: not-allowed;" data-tooltip="tooltip" title="You do not have adequate permission to Create Voucher. Please contact the administrator." />
					<input type="button" id="disabled_settle" class="btn btn-save pull-right" value="Settle Bill" style="opacity: 0.7; cursor: not-allowed;" data-tooltip="tooltip" title="You do not have adequate permission to Settle Bill. Please contact the administrator." />
				{% endif %}
      		</div>
    	</div>
  	</div>
</div>

<div id="bill_outstanding_document_modal" class="modal fade" role="dialog">
  	<div class="modal-dialog modal-lg">
		<div class="modal-content">
      		<div class="modal-header">
        		<button type="button" class="close" data-dismiss="modal">&times;</button>
		        <button class="btn transparent-btn" onclick="openMailPopup()" data-tooltip="tooltip" data-placement="left" title="Email Outstanding bill" style="float: right;margin-right: 20px"><i class="fa fa-envelope" style="font-size: 14px;" aria-hidden="true"></i></button>
        		<h4 class="modal-title">Preview Document</h4>
      		</div>
      		<div class="modal-body text-center">
      			<div id="bill_outstanding_document_container"></div>
   			</div>
   			<div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
   		</div>
  	</div>
</div>

<script type="text/javascript">
	$("#loading").hide();
	$('.nav-pills li').removeClass('active');
	$('#li_payment').addClass('active');

	function openMailPopup() {
		new Mailer().prepareEmailPopup().getPartyMailID(
			file_name=$("#id_csv_download_name").val(), type= "accounts", ledger_id= $("#id_ledger_id").val(),
			mail_id=$("#id_party_email").val(), subject = $("#id_mail_subject").val(),
			ledger_name = $("#id_ledger_name").val()).show();
		return false;
	}
</script>
{% endblock %}
