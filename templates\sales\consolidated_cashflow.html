{% extends "purchase/sidebar.html" %}
{% block project_fore_cast %}
    <style>
        .tr-header td {
            font-weight: bold;
            font-size: 16px;
            border-bottom: 2px solid #666
        }

        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            border: 1px solid #dddddd;
            text-align: left;
            padding: 8px;
            position: relative;
        }

        th {
            background-color: #f2f2f2;
        }

        tr:hover {
            background-color: #f5f5f5;
        }
        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }
        #revenueTable th,
        #revenueTable td {
            white-space: nowrap;
            width:150px !important;
        }
        #revenueTable th:first-child, #revenueTable td:first-child {
          position: sticky;
          left: 0;
          background-color: #f2f2f2;
          z-index: 1;
        }
        .totalRevenue{
            font-size:14px;
        }
        .totalExpense{
            background-color:#f2f2f2;
        }
        .totalCashFlow{
            background-color:#f2f2f2;
        }
        .reJson{
            font-size:14px;
        }
        body {
            overflow-y: scroll;
        }
    </style>
{% if logged_in_user|canView:'ACCOUNTS' %}
    <script type="text/javascript" src="/site_media/js/Component/projects.js?v={{ current_version }}"></script>
    <div class="right-content-container">
        <div class="page-title-container">
            <span class="page-title">{{ template_title }}</span>
        </div>
        <div class="container" style="margin-top: 20px;">
            <div class="project_forecast hide" style="overflow-y: scroll;">
                <div style="padding:20px">
                    <h4>ACTIVITY LOG</h4>
                </div>
                <button style="margin-right:25px; margin-top:-53px;" type="button" onClick="close()" class="closeBtn pull-right">×</button>
                <ul class="history-project_forecast history-log-container timeline"></ul>
            </div>
            <div class="row">
                <div class="col-lg-12 remove-padding">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="component_project proj_forcast" data-id="project" data-name="project" style="max-width: 300px"></div>
                        </div>
                         <h2 style="text-align:center;margin-top:10%">Budgeted Cashflow</h2>
                            <div class="csv_export_button pull-right">
                                <a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#revenueTable'), 'consolidated_report.csv']);" data-tooltip="tooltip" title="Download Consolidated Report as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
                         </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-container" style="width: 90%;text-align: center;margin-left: 5%;margin-bottom:20px;">
            <table class="table table-bordered" id="revenueTable"></table>
        </div>
        <span class="hide" id="versionChangeNote" style="margin-top: 1%;background-color:#f9e7e4;padding:10px;margin-left: 5%;font-size: 15px;"><b>Note</b> : This is the Data for Version changes. We can't modify this.</span>
    </div>
    <div class="hide">
        <form id="consolidated_cashflow_form" method="post" action="">
            {%csrf_token%}
            <input type="hidden" id="project_id" name="project_id" value="" />
        </form>
    </div>

    <script>
        $(document).ready(function () {
            const cashAllocatedValue = $('#workingCapitalValue').val();
            setTimeout(projectIdFetch, 2000);
        });
        const workingCapitalValue = $("#workingCapitalValue");
        function projectIdFetch(){
            let projectId = $('#project').val();
            const project = JSON.parse(localStorage.getItem('project'));
            $('#li_project_forecast').addClass('active');
            $("#menu_accounts").addClass("selected");
            if(project && project.type == 'Secondary'){
                $("#project").val(project.id).attr("disabled", true).trigger('chosen:updated');
                projectId = project.id
            }
            actualProjectsBudget(projectId);
            $('#project').change(function() {
                    actualProjectsBudget($(this).val());
            });
            fetchAnotherTableData(projectId);
            $("select#project").change(function() {
                const projectId = $(this).val();
                fetchAnotherTableData(projectId);
                $('#versionChangeNote').addClass('hide');
                $('#forecastSave').prop('disabled', false);
                $('.project_forecast').addClass('hide');
            });
        }
        const fetchAnotherTableData = async (projectId) => {
            projectId = $('#project').val();
            try {
                const actualProjectsBudget = await $.ajax({
                    url: '/erp/sales/json/total_credit_debit/',
                    type: "GET",
                    data: {'project_id': projectId, 'is_ledger': true, 'is_root_level' : true}
                });
                populateTable(actualProjectsBudget, "#revenueTable");
            }
            catch (error) {
                console.error("Error fetching another table data:", error);
            }
        };

        let responseData;;
        const populateTable = (data, tableId) => {
            responseData = data;
            $("#loading").show();
            $(tableId).empty();

            if (!data.data || data.data.months.length === 0) {
                $("#loading").hide();
                const emptyTable = `<tr><td class='text-center'>No data to display</td><tr>`;
                $(tableId).append(emptyTable);
                return;
            }

            const defaultRow = `<thead><tr></tr></thead><tbody></tbody>`;
            $(tableId).append(defaultRow);

            const months = data.data.months;

            const revenueData = data.data.revenue;
            const revenueTypes = Object.keys(revenueData);

            const expensesData = data.data.expenses;
            const expensesTypes = Object.keys(expensesData);

            const $tableHead = $(tableId + ' thead');
            const $tableBody = $(tableId + ' tbody');

            const $row = $('<tr> <th style="border-bottom:none">');
            months.forEach(month => {
            const $headerCell = $('<th colspan=2 class="text-center">').text(month);
            const $csv_header = $('<th hidden="hidden" class="text-center reJson">').text(month);
            $row.append($headerCell);
            $row.append($csv_header);
            });
            $tableHead.append($row);

            const $rowNew = $('<tr> <th style="border-top:none">');
            months.forEach(month => {
                const $rowSplitId = [$('<th class="text-center reJson">').text("Actual"),$('<th class="text-center reJson">').text("Budget")];
                $rowNew.append($rowSplitId);
            });

            $tableHead.append($rowNew);


            const totalRevenueBudget = {};
            const totalRevenueActual = {};
            const revenueListBudget = [];
            const revenueListActual = [];
            months.forEach(month => {
                totalRevenueActual[month] = 0;
                totalRevenueBudget[month] = 0;
                revenueTypes.forEach(incomeType => {
                    totalRevenueBudget[month] += revenueData[incomeType][month].budget ? parseFloat(revenueData[incomeType][month].budget) : 0;
                     totalRevenueActual[month] += revenueData[incomeType][month].actual ? parseFloat(revenueData[incomeType][month].actual) : 0;
                });
            });

            const $totalRowRevenue = $('<tr class="tr-header td totalRevenue_head">');
            const $totalHeaderRevenue = $('<td class="reJson totalRevenue">').text('Revenue');
            $totalRowRevenue.append($totalHeaderRevenue);

            months.forEach(month => {
                const $totalCell = [$('<td class="text-right reJson totalRevenue">').text(totalRevenueActual[month]),$('<td class="text-right reJson totalRevenue">').text(totalRevenueBudget[month])];
                revenueListBudget.push(totalRevenueBudget[month] ? totalRevenueBudget[month] : 0);
                revenueListActual.push(totalRevenueActual[month] ? totalRevenueActual[month] : 0);
                $totalRowRevenue.append($totalCell);
            });

            $tableBody.append($totalRowRevenue);


            revenueTypes.forEach(incomeType => {
                const $newRow = $('<tr>');
                const $incomeTypeCell = $('<td class="revenueHead">').text(incomeType);
                $newRow.append($incomeTypeCell);

                    months.forEach(month => {
                        const $incomeCell = [
                            $('<td class="text-right actual">').text(revenueData[incomeType][month].actual? revenueData[incomeType][month].actual : 0),
                            $('<td class="text-right budget">').text(revenueData[incomeType][month].budget ? revenueData[incomeType][month].budget : 0)
                        ];
                        $newRow.append($incomeCell);
                    });

                    $tableBody.append($newRow);
            });

            const totalExpensesActual = {};
            const totalExpensesBudget = {};
            const expensesListBudget = [];
            const expensesListActual = [];
            months.forEach(month => {
                totalExpensesActual[month] = 0;
                totalExpensesBudget[month] = 0;
                expensesTypes.forEach(incomeType => {
                    totalExpensesActual[month] += expensesData[incomeType][month].actual ? parseFloat(expensesData[incomeType][month].actual) : 0;
                     totalExpensesBudget[month] += expensesData[incomeType][month].budget ? parseFloat(expensesData[incomeType][month].budget) : 0;
                });
            });

            const $totalRowExpenses = $('<tr class="tr-header td totalExpense_head">');
            const $totalHeaderExpenses = $('<td class="reJson totalExpense">').text('Expenses');
            $totalRowExpenses.append($totalHeaderExpenses);

            months.forEach(month => {
                const $totalCell = [$('<td class="text-right reJson totalExpense">').text(totalExpensesActual[month]),$('<td class="text-right reJson totalExpense">').text(totalExpensesBudget[month])];
                expensesListActual.push(totalExpensesActual[month] ? totalExpensesActual[month] : 0)
                 expensesListBudget.push(totalExpensesBudget[month] ? totalExpensesBudget[month] : 0)
                $totalRowExpenses.append($totalCell);
            });

            $tableBody.append($totalRowExpenses);

            expensesTypes.forEach(expenseType => {
                const $newRow = $('<tr>');
                const $expenseTypeCell = $('<td class="expenseHead">').text(expenseType);
                $newRow.append($expenseTypeCell);

                    months.forEach(month => {
                        const $expenseCell = [
                            $('<td class="text-right side-content actual">').text(expensesData[expenseType][month].actual ? expensesData[expenseType][month].actual : 0),
                            $('<td class="text-right side-content budget">').text(expensesData[expenseType][month].budget ? expensesData[expenseType][month].budget : 0)
                            ];

                            $newRow.append($expenseCell);
                    });
                    $tableBody.append($newRow);
            });


            let cumulativeSumBudget = 0;
            let cumulativeSumActual = 0;
            const $totalRow = $('<tr class="tr-header td totalCashFlow_head">');
            const $totalHeader = $('<td class="reJson cashFlowHead">').text('Cash Flow');
            $totalRow.append($totalHeader);

            for (let i = 0; i < revenueListBudget.length; i++) {
                const diffBudget = revenueListBudget[i] - expensesListBudget[i];
                const diffActual = revenueListActual[i] - expensesListActual[i];
                cumulativeSumBudget += diffBudget;
                cumulativeSumActual += diffActual;
                const $totalCell = [$('<td class="text-right reJson totalCashFlow">').text(cumulativeSumActual.toFixed(2)),$('<td class="text-right reJson totalCashFlow">').text(cumulativeSumBudget.toFixed(2))];
                $totalRow.append($totalCell);
            }
            $tableBody.append($totalRow);
            $("#workingCapitalValue").text(data.data.working_capital);
            $("#project_owner").val(data.data.project_owner);
            $("#project_owner_email").val(data.data.email);
            $("#project_owner_no").val(data.data.phone_no);
            $("#loading").hide();
        };

        function cashFlowOverView(){
           window.location.href = '/erp/sales/get_project_wise_cashflow_overview/';
        }
    </script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}
