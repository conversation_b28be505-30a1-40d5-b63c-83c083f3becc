<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>Release Notes</title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link type="image/png" rel="icon" href="/erp/public/document/?file_name=xs-logo-with-border.png">
	<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap.css">
	<link type="text/css" rel="stylesheet" href="/site_media/css/roboto-font.css">
	<link type="text/css" rel="stylesheet" href="/site_media/css/jquery.treemenu.css">
	<script type="text/javascript" src="/site_media/js/jquery.js"></script>
	<script type="text/javascript" src="/site_media/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="/site_media/js/jquery.treemenu.min.js"></script>

	<style type="text/css">
		body {
			font-family: 'roboto', sans-serif;
		}

		.main-container {
			width: calc(100% - 175px);
			border: solid 1px #ccc;
			margin-top: 28px;
			padding: 0;
			margin-bottom: 20px;
			font-family: roboto;
			float: left;
		}

		@media only screen and (max-width: 700px) {
		  .main-container {
				width: 100%;
			}
		}

		.main-header,
		.sub-header {
			color: #004195;
		}

		.remove-padding {
		  	padding: 0 !important;
		}

		.nav_invoice_template li {
			padding: 2px;
		    border: solid 1px #ccc;
		    margin-bottom: -1px;
		    font-size: 14px;
		    transition: all 0.4s;
		    cursor: pointer;
		}

		.nav_invoice_template li a {
			text-decoration: none;
			color: #333;
		    width: calc(100% - 50px);
    		display: inline-block;
		}

		.inv_template_editor {
			width: 500px;
		    padding: 5px 15px;
		    float: left;
		    border: 1px solid #ccc;
		    min-height: 257px;
		    margin-left: 15px;
		    margin-bottom: 10px;
		}

		.nav_invoice_template li:hover {
			background: rgba(32, 155, 225, 0.3);
		}

		.nav_invoice_template li a.active {
			/*background: rgba(32, 155, 225, 1);*/
			color: #209be1;
			font-size: 20px;
			text-shadow: 0 0 #000;
		}

		.nav-header {
			color: #000;
		    padding: 10px;
		    display: inline-block;
		    width: 100%;
		    font-size: 20px;
		    background: #fff;
		    border-right: 1px solid #209be1;
		}

		#release_docs {
			width: calc(100% - 175px)
		}

		@media only screen and (max-width: 700px) {
		  	#release_docs {
				width: 100%;
			}
		}
	</style>
</head>
	<body>
		<div class="container" style="margin-top: 20px;">
			<div class="logo" style="font-size: 26px;">
			<span style="font-family: 'Expletus Sans', cursive; font-size: 40px; margin-right: 10px; color: #209be1;">xs<b><u>erp</u></b></span>
				Release Notes
			</div>
		</div>
		<div class="container-fluid" style="padding: 0;">
			<hr />
		</div>
		<div class="container" style="color: #3c4043; font-size: 14px; letter-spacing: 0.2px;">
			<ul class="tree inv_template_side_bar nav_invoice_template" style="width: 170px; float: left; padding: 0">
				<span style="font-size: 20px;">Version</span>
				<li class="current_version">
					<a class="html_load " data-version="2_16">2.16</a>
					<ul class="level-2">
						<li><a class="html_load" data-version="2_16_1">2.16.1</a></li>
						<li><a class="html_load" data-version="2_16_2">2.16.2</a></li>
						<li><a class="html_load" data-version="2_16_3">2.16.3</a></li>
						<li><a class="html_load" data-version="2_16_4">2.16.4</a></li>
						<li><a class="html_load current_click" data-version="2_16_5">2.16.5</a></li>
					</ul>
				</li>
				<li><a class="html_load" data-version="2_15">2.15</a>
					<ul class="level-2">
						<li><a class="html_load" data-version="2_15_1">2.15.1</a></li>
						<li><a class="html_load" data-version="2_15_2">2.15.2</a></li>
						<li><a class="html_load" data-version="2_15_3">2.15.3</a></li>
						<li><a class="html_load" data-version="2_15_4">2.15.4</a></li>
					</ul>
				</li>
				<li><a class="html_load" data-version="2_14">2.14</a>
					<ul class="level-2">
				    	<li><a class="html_load" data-version="2_14_1">2.14.1</a></li>
				    	<li><a class="html_load" data-version="2_14_2">2.14.2</a></li>
				    	<li><a class="html_load" data-version="2_14_3">2.14.3</a></li>
				    	<li><a class="html_load" data-version="2_14_4">2.14.4</a></li>
				    </ul>
				</li>
				<li><a class="html_load" data-version="2_13">2.13</a>
			  		<ul class="level-2">
				    	<li><a class="html_load" data-version="2_13_1">2.13.1</a></li>
				    	<li><a class="html_load" data-version="2_13_2">2.13.2</a></li>
				    </ul>
			  	</li>
			  	<li><a>2.12</a>
			  		<ul class="level-2">
				    	<li><a class="html_load" data-version="2_12_1">2.12.1</a></li>
				    	<li><a class="html_load" data-version="2_12_2">2.12.2</a></li>
				    </ul>
			  	</li>
			  	<li><a>2.11</a></li>
			  	<li><a>2.10</a></li>
			  	<li><a>2.9</a></li>
			  	<li><a>2.8</a>
			  		<ul class="level-2">
				    	<li><a>2.8.1</a></li>
					    <li><a>2.8.2</a></li>
					    <li><a>2.8.3</a></li>
					    <li><a>2.8.4</a></li>
					    <li><a>2.8.5</a></li>
					    <li><a>2.8.6</a></li>
					    <li><a>2.8.7</a></li>
				  	</ul>
				</li>
				<li><a>2.7</a></li>
				<li><a>2.6</a>
			  		<ul class="level-2">
				    	<li><a>2.6.1</a></li>
				  	</ul>
				</li>
				<li><a>2.5</a></li>
				<li><a>2.4</a>
			  		<ul class="level-2">
				    	<li><a>2.4.1</a></li>
				  	</ul>
				</li>
				<li><a>2.3</a>
			  		<ul class="level-2">
				    	<li><a>2.3.1</a></li>
				  	</ul>
				</li>
				<li><a>2.2.1</a></li>
				<li><a>2.0</a></li>
			</ul>
			<div class="release_docs_container">
				<iframe id="release_docs" src="" style="border: none;"></iframe>
			</div>
			<div class="html_document_container">
			</div>
		</div>
	</body>
	</html>

	<script type="text/javascript">

		$(document).ready(function(){
			$(".tree").treemenu({delay:500});
		});

		$(window).load(function() {
			var pageHeight = Number($(document).height() - 120);
			$("#release_docs").attr("height",pageHeight);
			$(".nav_invoice_template").find("li").first().find("a").click();
			$(".current_version .toggler").trigger("click");
			$(".current_click").click();
		});

		$(".nav_invoice_template li a").click(function() {
			var file_name = $(this).text();
			if($(this).hasClass("html_load")) {
			    $.ajax({
	                url: "/erp/public/document/",
		            type: "GET",
		            dataType: "json",
		            data:{file_name: file_name+".html"},
		            success: function (response) {
		                $(".html_document_container").html(response)
		            },
		            error: function (xhr, errmsg, err) {
		                console.log(xhr.status + ": " + xhr.responseText);
		            }
		        });
				$(".release_docs_container").addClass("hide");
				$(".html_document_container").removeClass("hide");
				$(".nav_invoice_template li a").removeClass("active");
				$(this).addClass("active");
			}
			else {
				$(".release_docs_container").removeClass("hide");
				$(".html_document_container").addClass("hide");
				$("#release_docs").attr("src", "/erp/public/document/?file_name="+file_name+".pdf");
				$(".nav_invoice_template li a").removeClass("active");
				$(this).addClass("active");
			}
		})


	</script>
</html>