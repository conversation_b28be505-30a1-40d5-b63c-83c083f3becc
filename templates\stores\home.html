{% extends "stores/sidebar.html" %}
{% block storesHome %}
{% if access_level.view %}
<script>
    var myCalendar;
	function doOnLoad() {
		myCalendar = new dhtmlXCalendarObject(["calendar","calendar1"]);
		myCalendar.setDateFormat("%d/%m/%Y");
	}
</script>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/jquery_ui.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/account.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/dashboard.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery_ui.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>

<style type="text/css">
.account-box table td.stock-heading {
	width: 420px;
	border-right:  solid 1px transparent !important;
}
</style>
<div class="right-content-container">
	<div class="page-title-container">
		<span class="page-title">Dashboard</span>
	</div>
	<div class="container-fluid">
		<!-- Page Heading -->
		<!-- new -->
		<div class="container">
			<div class="accounts-container" style="margin: 0;">
				<div class="row">
					<div class="col-sm-6">
						<h3>Stock Statement</h3>
						<div class="account-box">
							<table class="table table-bordered">
								<tr class="account-table-header">
									<th colspan="2" style="padding: 4px;">
										<form id="home_date_submit" method="post" action="/erp/stores/home/" class="form-inline"> {% csrf_token %}
											<div class="col-sm-12" style="padding: 0 4px;">
												<label>Date Range: </label>
												<div id="reportrange" class="report-range form-control" style="margin: 3px; width: 265px;">
													<i class="glyphicon glyphicon-calendar"></i>&nbsp;
													<span></span> <b class="caret"></b>
													<input type="hidden" id="fromdate" name="from_date" value="{{ from_date }}" class="fromdate" />
													<input type="hidden" id="todate" name="to_date" value="{{ to_date }}" class="todate" />
												</div>
												<input type="button" id="load_dashboard_form" name="load_store_dashboard" value="Refresh" onclick="getStockStatement()" class=" btn btn-save">
											</div>
											 <div class="clearfix"></div>
										 </form>
									</th>
								<tr>
								<tr>
									<td class="stock-heading">Opening Stock</td>
									<td align="right"><label id="open_stock" name="op_stock" class="sale_txt_box"><span id="opening_ss"></span> </label> </td>
								</tr>
								<tr >
									<td class="stock-heading">Receipts</td>
									<td align="right"><label id="receipts" class="sale_txt_box"> <span id="receipts_ss"></span></label></td>
								</tr>
								<tr>
									<td class="stock-heading">Issues</td>
									<td align="right"><label id="issues" class="sale_txt_box"><span id="issues_ss"></span></label></td>
								</tr>
								<tr>
									<td class="stock-heading">Closing Stock</td>
									<td align="right"><label id="closing_stock" class="sale_txt_box"><span id="closing_ss"></span>  </label></td>
								</tr>
							</table>
						</div>
					</div>
					<div class="col-sm-6 status-container" style="margin-top: 0;">
						<h3>Status</h3>
						<div class="dashboard-status-box">
							<table class="table" style="margin-bottom: 0;">
								<tr class="dashboard-status-box-content" role="button">
									<form id="indent_pending" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading'>Indent Pending
										<input type="hidden" id="3_since" value="" name="since"/>
										<input type="hidden" id="3_till" value="" name="till"/>
										<input type="hidden" id="pending" value="pending" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_indent" value="" class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
								<tr class="dashboard-status-box-content" role="button">
									<form id="indent_pending_due_po" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading' style="padding-left: 58px !important;">Due to P.O
										<input type="hidden" id="4_since" value="" name="since"/>
										<input type="hidden" id="4_till" value="" name="till"/>
										<input type="hidden" id="pending_due_po" value="pending_due_po" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_po" value="" class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
								<tr class="dashboard-status-box-content" role="button">
									<form id="indent_pending_due_material" method="post" action="/erp/stores/indent_list/loadindents/"> {% csrf_token %}
									<td class='dashboard-status-box-heading' style="padding-left: 58px !important;">Due to Material
										<input type="hidden" id="5_since" value="" name="since"/>
										<input type="hidden" id="5_till" value="" name="till"/>
										<input type="hidden" id="pending_due_material" value="pending_due_material" name="status"/>
									</td>
									<td align="right"><input type="submit" id="btn_pending_material" value="" class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
								<tr class="dashboard-status-box-content hide" role="button">
									<td class='dashboard-status-box-heading'>Accounted</td>
									<form id="grn_accounted" method="post" action="#"> {% csrf_token %}
									<td align="right"><input type="submit" id="btn_grn_accounted" value="" class="dashboard-status-box-btn" />  </td>
									</form>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>
			<div class="content_bg">
				<div class="statement_box">
					<div class="clearfix"></div>
					<div class="row">
						<div class="clearfix"></div>
						<div class="col-lg-6 text-left monthly_stockhide">
							<h3>Monthly Closing Stock</h3>
						  	<div id="chart_closing_stock">
							    <div id='loadingmessage1_ie' class="text-center" style='display:none;margin-top:95px'>
									<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width:75px"/>
								    <br>
								    Please wait...
								</div>
						    </div>
						</div>
						<div class="col-lg-6 text-left stock_mixhide">
							<h3>Stock Mix</h3>
							<div id="chart_stock_mix">
								<div id='loadingmessage2_ie' class="text-center" style='display:none;margin-top:95px'>
									<img src='{{ MEDIA_URL }}../images/loading_spinner.gif' style="width:75px"/>
									<br>
								    Please wait...
								</div>
							</div>
						</div>
					</div>
					<div class="clearfix"></div>
				</div>
			</div>			
		</div>
	</div>
</div>
<script type="text/javascript" src="/site_media/js/loader.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/countdown-timer.js?v={{ current_version }}"></script>
<script type = "text/javascript" language = "javascript">

$(document).ready(function() {
	$('#raise_frm_date').val($('#fromdate').val());
	$('#raise_t_date').val($('#todate').val());
	getStockStatement();
	getIndentStatement();

    $("#fromdate").change(function(){
        var var_fdate = $('#fromdate').val();
        $('#raise_frm_date').val(var_fdate);
    });
    $("#todate").change(function(){
        var var_tdate = $('#todate').val();
        $('#raise_t_date').val(var_tdate);
    });
	document.getElementById('open_stock').innerHTML = formatNumber(document.getElementById('open_stock').innerHTML);
	document.getElementById('receipts').innerHTML = formatNumber(document.getElementById('receipts').innerHTML);
	document.getElementById('issues').innerHTML = formatNumber(document.getElementById('issues').innerHTML);
	document.getElementById('closing_stock').innerHTML = formatNumber(document.getElementById('closing_stock').innerHTML);
	$('.nav-pills li').removeClass('active');
	$('#li_st_dashboard').addClass('active');
	setTimeout(function(){
	LoadChart();
	},50);
});

function getStockStatement() {
			var since = $('#fromdate').val();
			var till = $('#todate').val();
	        $.ajax({
		        url: '/erp/stores/json/get_stock_statement/',
		        type: "POST",
		        dataType: "json",
		        data: {since: since, till: till},
		        success: function (json) {
			        $("#opening_ss").html(json.data[0].toFixed(2));
			        $("#receipts_ss").html(json.data[1].toFixed(2));
			        $("#issues_ss").html(json.data[2].toFixed(2));
			        $("#closing_ss").html(json.data[3].toFixed(2));
		        }
		    });
 }

 function getIndentStatement() {
	        $.ajax({
		        url: '/erp/stores/json/get_indent_statement/',
		        type: "POST",
		        dataType: "json",
		        data: {},
		        success: function (json) {
			        $("#btn_pending_indent").val(json.data[2]);
			        $("#btn_pending_po").val(json.data[3]);
			        $("#btn_pending_material").val(json.data[4]);
		        }
		    });
 }

function formatNumber(num) {
    var n1, n2;
    num = num + '' || '';
    // works for integer and floating as well
    n1 = num.split('.');
    n2 = n1[1] || null;
    n1 = n1[0].replace(/(\d)(?=(\d\d)+\d$)/g, "$1,");
    num = n2 ? n1 + '.' + n2 : n1;
    return num;
}

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}
var ClosingStock = [];
var StockMix = [];
function LoadChart(){

	$.ajaxSetup({
                headers: {"X-CSRFToken": getCookie('csrftoken')}
            });
    $.ajax({
        url: 'erp/stores/json/dashboard_data/',
        type: "POST",
        async: true,
        data: {},
        beforeSend: function () {
            $("#loadingmessage1_ie").show();
            $("#loadingmessage2_ie").show();
        },
        success: function (response) {
                ClosingStock = [['Year', 'Closing Stock']];
                for (let i=0;i<response['monthly_closing_stock'].length; i++) {
                    var obj = response['monthly_closing_stock'][i];
					ClosingStock.push([obj.month, obj.closing_stock]);
				}
                $('#loadingmessage1_ie').hide();
                $('#loadingmessage2_ie').hide();
                google.charts.load('current', {packages: ['corechart', 'bar']});
				google.charts.setOnLoadCallback(drawCSchart);
				StockMix = [['Category', 'Value']];
				for (let i=0;i<response['stock_mix'].length; i++) {
                    var obj = response['stock_mix'][i];
                    if(obj.value >= 0){
                        StockMix.push([obj.category, obj.value]);
                    }
				}
				google.charts.load('current', {'packages':['corechart']});
  	            google.charts.setOnLoadCallback(drawStockMixChart);
        },
        error: function (jqXHR, textStatus, errorThrown) {
            console.log(xhr.status + ": " + xhr.responseText);
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}

function drawCSchart() {
  	var data = google.visualization.arrayToDataTable(ClosingStock);
    var options = {
        title: '',
        colors: ['#33ac71'],
        hAxis: {
          	title: 'Month',
         	minValue: 0
        },
        vAxis: {
          title: 'Rupee',
          	viewWindow: {
		        min: 0
		    }
        },
        height: 350
    };
    var chart = new google.visualization.ColumnChart(document.getElementById('chart_closing_stock'));
    chart.draw(data, options);
}	

function drawStockMixChart() {
    var data = google.visualization.arrayToDataTable(StockMix);
    var options = {
        title: '',
       'chartArea': {'width': '80%', 'height': '65%'},
       	width: '100%',
        height: 350
    };

    var chart = new google.visualization.PieChart(document.getElementById('chart_stock_mix'));
    chart.draw(data, options);
}

$(".dashboard-status-box-content").click(function(){
	$(this).closest('tr').find('form').submit();
});
</script>
{% else %}
	<div class="text-center" style="margin-top: 100px;">
		<h3>You don't have adequate permission to access this module.</h3>
		<h4>Please Contact your Administrator.</h4>
	</div>
{% endif %}
{% endblock %}