import os
import time
from shutil import copyfile

from num2words import num2words
from PyPDF2 import PdfFileReader, PdfFileWriter
from reportlab.pdfgen.canvas import Canvas
from reportlab.platypus import Paragraph

from util import helper, logger
from util.document_fonts import registeredFonts
from util.document_properties import getStyleSheet, DEFAULT_TARGET_FILE, DUMMY_DOC_PATH, CANCEL_WATERMARK_DOC_PATH, \
	HEADER_Label_Original_DOC_PATH, HEADER_Label_Supplier_DOC_PATH, HEADER_Label_Extra_Copy_DOC_PATH, \
	HEADER_Label_Transport_DOC_PATH
from util.helper import getAbsolutePath, writeFile
from util.properties import LABEL_STYLE, TEMP_DOC_PATH

__author__ = 'kalaivanan'

registeredFonts()
styles = getStyleSheet()


class PDFGenerator(object):
	"""
	An abstract/base class that needs to be implemented for generating PDFs for respective modules
	"""
	_COLON_H4 = Paragraph(":", styles[LABEL_STYLE])

	def __init__(self, target_file_path=DEFAULT_TARGET_FILE):
		self.target_file_path = helper.getAbsolutePath(target_file_path)
		self.canvas = Canvas(helper.getAbsolutePath(target_file_path))

	def generatePDF(self, source=None):
		"""
			Paints the canvas with the desired Document and saves it in the location specified in the target_file_path

		:param source:
		:return:
		"""
		try:
			self.paintCanvas(source)
			logger.debug('Canvas painting done...')
			self.canvas.save()
		except:
			logger.exception('PDF creation failed...')

	def paintCanvas(self, source=None):
		"""
			Method that is supposed to paint all the ingredient elements of the document. Abstract method - to be overridden
		by the extending class.

		:param source:
		:return:
		"""
		return self.canvas

	def _addStoryFrameToCanvas(self, story=(), frame=None, canvas=None):
		"""
		Resets the Current Position to draw on the Canvas and draws the story on-to the frame specified

		:param story:
		:param frame:
		:param canvas:
		:return:
		"""
		frame._reset()
		frame.addFromList(story, canvas)

	@staticmethod
	def addWaterMark(watermark_file=None, target_file_path=None, label=[]):
		"""
		Adds a single-page PDF doc as watermark to document being worked upon.

		:param target_file_path:
		:param label:
		:param watermark_file:
		:return:
		"""
		logger.debug('Adding Watermark for a document...')
		# Place-holder dummy
		dummy_path = DUMMY_DOC_PATH % time.clock()
		copyfile(target_file_path, dummy_path)
		# Work on the dummy
		document = PdfFileReader(file(dummy_path, "rb"))
		watermarked_document = PdfFileWriter()
		if label != []:
			for header in label:
				if header == "original":
					watermark_file = file(getAbsolutePath(HEADER_Label_Original_DOC_PATH), "rb")
				elif header == "supplier":
					watermark_file = file(getAbsolutePath(HEADER_Label_Supplier_DOC_PATH), "rb")
				elif header == "transport":
					watermark_file = file(getAbsolutePath(HEADER_Label_Transport_DOC_PATH), "rb")
				else:
					watermark_file = file(getAbsolutePath(HEADER_Label_Extra_Copy_DOC_PATH), "rb")
				for page in document.pages:
					# To Push Water mark to background, write the target doc over the watermark
					watermark_pdf = PdfFileReader(watermark_file)
					watermark_pdf.getPage(0).mergePage(page)
					watermarked_document.addPage(watermark_pdf.getPage(0))
		else:
			for page in document.pages:
				# To Push Water mark to background, write the target doc over the watermark
				watermark_pdf = PdfFileReader(watermark_file)
				page.mergePage(watermark_pdf.getPage(0))
				watermarked_document.addPage(page)
		# overwrite the target file
		output_stream = file(target_file_path, "wb")
		watermarked_document.write(output_stream)
		output_stream.close()
		os.remove(dummy_path)  # Delete the dummy file
		return watermarked_document

	@staticmethod
	def addLabel(
			target_file_path=DEFAULT_TARGET_FILE, label="", pdf_generator=None, source=None, config_info=None, enterprise=None,
			logged_in_user=None, invoice_id=None, doc_status=None):
		"""
		Adds Labels to the document.

		:return:
		"""
		logger.info('Adding Label "%s" for Invoice!' % label)
		labeled_document = PdfFileWriter()
		for header in label:
			is_receiver_sign_enabled = False
			labeled_doc_path = TEMP_DOC_PATH % (source.getCode().replace("/", "_"), header)
			if header["label"] == "original":
				header_text = "ORIGINAL FOR RECIPIENT"
				if header["signature"]:
					is_receiver_sign_enabled = True
			elif header["label"] == "supplier":
				header_text = "TRIPLICATE FOR SUPPLIER"
				if header["signature"]:
					is_receiver_sign_enabled = True
			elif header["label"] == "transport":
				header_text = "DUPLICATE FOR TRANSPORTER"
				if header["signature"]:
					is_receiver_sign_enabled = True
			else:
				header_text = "EXTRA COPY"
				if header["signature"]:
					is_receiver_sign_enabled = True

			document_pdf = pdf_generator.generatePDF(
				source=source, config_info=config_info, enterprise=enterprise, logged_in_user=logged_in_user, invoice_id=invoice_id,
				doc_status=doc_status, header_text=header_text, is_receiver_sign_enabled=is_receiver_sign_enabled)

			writeFile(document_pdf, labeled_doc_path)
			labeled_pdf = PdfFileReader(file(getAbsolutePath(labeled_doc_path), "rb"))
			logger.info("Page count: %s" % len(labeled_pdf.pages))
			for page in labeled_pdf.pages:
				labeled_document.addPage(page)
			os.remove(getAbsolutePath(labeled_doc_path))

		output_stream = file(getAbsolutePath(target_file_path), "wb")
		labeled_document.write(output_stream)
		output_stream.close()
		return labeled_document

	@staticmethod
	def addCancelWaterMark(target_file_path=DEFAULT_TARGET_FILE):
		"""
		Adds a water-mark 'CANCELLED' denoting the document is being cancelled. The water-mark is a single-page PDF doc,
		stored at the location, denoted by the file-path constant 'CANCEL_WATERMARK_DOC_PATH'.

		:return:
		"""
		logger.debug('Adding Rejected Watermark for a Invoice!!')
		return PDFGenerator.addWaterMark(
			file(getAbsolutePath(CANCEL_WATERMARK_DOC_PATH), "rb"), target_file_path=getAbsolutePath(target_file_path))

	@staticmethod
	def addBackgroundImage(background_file=None, target_file_path=DEFAULT_TARGET_FILE):
		"""
		Adds a background image for elegant template. The background is a single-page PDF doc,
		stored at the location, denoted by the file-path constant 'background_file'.

		:return:
		"""
		logger.debug('Adding background for a Invoice!!')
		return PDFGenerator.addWaterMark(
			file(getAbsolutePath(background_file), "rb"), target_file_path=getAbsolutePath(target_file_path))

	def _appendToPDF(self, source_file_path=None, target_file_path=None):
		"""

		:param source_file_path:
		:param target_file_path:
		:return:
		"""
		logger.debug("Appending a PDF document to another...")
		# Place-holder dummy
		dummy_path = DUMMY_DOC_PATH % time.clock()
		copyfile(target_file_path, dummy_path)
		source_doc = PdfFileReader(file(source_file_path, "rb"))
		target_doc = PdfFileReader(file(dummy_path, "rb"))
		appended_doc = PdfFileWriter()
		for page in target_doc.pages:
			appended_doc.addPage(page)
		for page in source_doc.pages:
			appended_doc.addPage(page)
		result_stream = file(target_file_path, "wb")
		appended_doc.write(result_stream)
		result_stream.close()
		os.remove(dummy_path)

	@staticmethod
	def getTotalInWords(value=None, currency=None):
		"""

		:param value:
		:param currency:
		:return:
		"""
		try:
			if int(str(value).split('.')[1]) != 0:
				return num2words(int(str(value).split('.')[0]), lang='en_IN') + " " + currency.name + " and " + num2words(
					int(str(value).split('.')[1])) + " " + currency.minor_unit
			else:
				return num2words(int(str(value).split('.')[0]), lang='en_IN') + " " + currency.name
		except OverflowError:
			if int(str(value).split('.')[1]) != 0:
				return num2words(int(str(value).split('.')[0])) + " " + currency.name + " and " + num2words(
					int(str(value).split('.')[1])) + " " + currency.minor_unit
			else:
				return num2words(int(str(value).split('.')[0])) + " " + currency.name
		except Exception as e:
			logger.exception('Something went wrong while getting Total in Words - %s' % e)
