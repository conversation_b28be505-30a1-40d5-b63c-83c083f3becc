"""
"""
from ftplib import FTP

from settings import FTP_IP, FTP_USER, FTP_PASSWORD, FTP_PATH
from util import logger

__author__ = 'nandha'


class FTPUtil:
	def __init__(self):
		"""Empty initializer
		"""
		logger.debug("Ftp initiated")
		self.data = ""

	def downloadCallback(self, data=None):
		"""

		:param data:
		:return:
		"""
		logger.debug("Download callback %s" % type(data))
		self.data = self.data + data

	def upload(self, file_path=None, filename=None, fp=None):
		"""
		
		:param file_path:
		:param filename:
		:param fp:
		:return:
		"""
		ftp = None
		try:
			logger.info("Uploading file to ftp %s" % filename)
			logger.debug("Connecting ftp server")
			ftp = FTP(FTP_IP)
			
			ftp_command_result = ftp.getwelcome()
			logger.debug("Welcome message: " + ftp_command_result)
			ftp_command_result = ftp.login(user=FTP_USER, passwd=FTP_PASSWORD)
			logger.debug("Authentication: " + ftp_command_result)
			ftp.cwd(FTP_PATH)

			file_path = str(file_path)
			if file_path.endswith("/"):
				file_path = file_path[:-1]
			if file_path.startswith("/"):
				file_path = file_path[1:]
			try:
				ftp_command_result = ftp.cwd(file_path)
			except Exception as e:
				logger.info("Creating remote directory %s due to %s" % (file_path, e.message))
				ftp.mkd(file_path)
				ftp_command_result = ftp.cwd(file_path)
			logger.debug(ftp_command_result)

			ftp.storbinary("STOR %s" % filename, fp=fp, blocksize=65535)
			fp.close()
			return True
		except Exception as e:
			logger.exception("Could not upload file - %s " % e.message)
			raise
		finally:
			if ftp:
				ftp.quit()

	def download(self, file_path=None, filename=None):
		"""
		
		:param file_path:
		:param filename:
		:return:
		"""
		ftp = None
		try:
			logger.info("Downloading file from ftp %s" % filename)
			ftp = FTP(FTP_IP)
			logger.debug("Connecting ftp server")
			
			ftp_command_result = ftp.getwelcome()
			logger.debug("Welcome message: " + ftp_command_result)
			ftp_command_result = ftp.login(user=FTP_USER, passwd=FTP_PASSWORD)
			logger.debug("Authentication: " + ftp_command_result)

			file_path = str(file_path)
			if not file_path.endswith("/"):
				file_path = "%s/" % file_path
			if not file_path.startswith("/"):
				file_path = "/%s" % file_path
			ftp_command_result = ftp.cwd(FTP_PATH + file_path)
			logger.debug(ftp_command_result)
			
			size = ftp.size(filename=filename)
			logger.info("Ftp download %s bytes" % size)
			logger.info(ftp.retrbinary("RETR %s" % filename, callback=self.downloadCallback, blocksize=size))
			return self.data
		except Exception as e:
			logger.exception("Could not download file - %s " % e.message)
			raise
		finally:
			if ftp:
				ftp.quit()

	def delete(self, file_path=None, filename=None):
		"""

		:param file_path:
		:param filename:
		:return:
		"""
		ftp = None
		try:
			logger.info("Deleting file from ftp %s" % filename)
			ftp = FTP(FTP_IP)
			logger.debug("Connecting ftp server")

			ftp_command_result = ftp.getwelcome()
			logger.debug("Welcome message: " + ftp_command_result)
			ftp_command_result = ftp.login(user=FTP_USER, passwd=FTP_PASSWORD)
			logger.debug("Authentication: " + ftp_command_result)

			file_path = str(file_path)
			if not file_path.endswith("/"):
				file_path = "%s/" % file_path
			if not file_path.startswith("/"):
				file_path = "/%s" % file_path
			ftp_command_result = ftp.cwd(FTP_PATH + file_path)
			logger.debug(ftp_command_result)
			ftp.delete(filename)
			logger.info("Deleted the file %s from FTP" % filename)
			return True
		except Exception as e:
			logger.exception("Could not delete file - %s " % e.message)
			raise
		finally:
			if ftp:
				ftp.quit()
