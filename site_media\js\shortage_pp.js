function generateShortageReportPP(){
        $('#loading').show();
        if($("#report-table").hasClass("dataTable")) {
            $('#report-table').DataTable().clear();
            $('#report-table').DataTable().destroy();
        }
        $('#report-table thead tr').remove();
        $('#report-table tbody tr').remove();
        var selected_boms=""
        var selected_pp_nos=""
        var selected_location_ids =""
        var selected_pp_ids =""
        var include_po = $("#include_item_order").is(":checked")
        $("#shortage-list-table tbody tr").each(function() {
            selected_boms += $(this).find("td[name='material_cat_code']").text() + "[:]" + $(this).find("td[name='material_qty']").text() + "[:]"
            selected_pp_nos += $(this).find("td[name='pp_no']").text().trim() + "[:]"
            selected_location_ids += $(this).find("td[name='pp_no'] input#location_id").val() + "[:]";
            selected_pp_ids += $(this).find("td[name='pp_no'] input#pp_id").val() + "[:]";
        });

		var headerRow =`<th rowspan="3" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">S.No.</th>
						<th rowspan="3" style="width: 250px; min-width: 250px; max-width: 250px;" class="align-middle">Name</th>
                        <th rowspan="3" style="width: 150px; min-width: 150px; max-width: 150px;" class="align-middle">Drawing No</th>
						<th rowspan="3" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">UOM</th>
						<th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock</th>
						<th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Required</th>
						<th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage</th>`

        var csv_header =  `<th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">S. No</th>
                            <th hidden="hidden" style="width: 250px; min-width: 250px; max-width: 250px;">Name</th>
                            <th hidden="hidden" style="width: 150px; min-width: 150px; max-width: 150px;">Drawing No</th>

                            <th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">UOM</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock - Qty</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Required - Qty</th>
                            <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage - Qty</th>`
        $.ajax({
            url: "/erp/stores/materialshortagepplist/",
            type: "post",
            datatype: "json",
            data: {"cat_list": selected_boms, "include_po": include_po, "pp_no_list" : selected_pp_nos, "location_list" : selected_location_ids, "pp_id_list" : selected_pp_ids},
            success: function(response) {
                try {
                    if (response.response_message == "Internal server error") {
                        swal("", response.custom_message, "error")
                    } else if (response["data"] != "") {
                        var materials = response["data"]
                        var bom_header = response["catalogue_materials_name"]
                        var pp_no = response["pp_no_list"]
                        var pp_id = response["pp_id"]
                        var location_name = response["location_name"]
                        var transfer_id_list = response['transfer_id_list']
                        all_location_stock = JSON.stringify(response["stock_transfer_req_location_qty"])
                        var supHeaderRow =  ""
                        var subHeaderRow =  ""
                        if(include_po == true){
                            headerRow = `${headerRow} <th style="width: 100px; min-width: 100px; max-width: 100px;" align="center" rowspan="3">Pending PO</th>`
                            csv_header = `${csv_header} <th hidden="hidden">Pending PO</th>`
                        }
                        var pp_permission = $("#pp_permission").val().toLowerCase();
                        const canEdit = $('#access_level').val();
                        for(i=0; i<response["bom_count"]; i++){
                            const transferPPItem = JSON.stringify(transfer_id_list[pp_id[i]] || []);
                            const encodedTransferPPItem = encodeURIComponent(transferPPItem)
                            headerRow = `${headerRow} <th style="width: 450px; min-width: 450px; max-width: 450px;text-align:left" align="left" colspan="5">`;
                            if(pp_permission == "true"){
                                headerRow += `<button class="btn btn-primary save-alloted-qty" style="cursor: pointer;background-color:green" onclick="saveAllAllotedQty('${pp_no[i]}',$(this))">Allocate</button><button class="btn btn-primary save-alloted-qty stock-transfer-btn" data-pp-id="${pp_id[i]}" style="cursor: pointer;background-color:green;margin-left: 1%;" onclick="triggerCreateStockTransfer('${pp_no[i]}','${pp_id[i]}', '${encodedTransferPPItem}')" ">Stock transfer request</button><span style="margin-left: 16%;">${pp_no[i]}</span></th>`;
                            }
                            headerRow += `<span style="margin-left: 50%;">${pp_no[i]}</span></th>`;
                            supHeaderRow = `${supHeaderRow} <th style="width: 300px; min-width: 300px; max-width: 300px;" align="left" colspan="5">${bom_header[i]} - ${location_name[i]}</th>`
                            subHeaderRow = `${subHeaderRow} <th class="hide" align="left">Location Wise Stock</th><th align="left">Required</th><th align="left">Shortage </th><th class="hide" align="left">Location Wise Shortage</th><th align="left">Already <br/>Allocated </th><th align="left">Issued </th><th align="left" style="width:120px">Allocate </th>`
                            csv_header = `${csv_header}<th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Required </th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Shortage </th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Allocated </th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Issued </th>`
                        }
                        csv_header += `<th style="display: none;"></th>`;
                        $('#report-table thead').append(`<tr class="exclude_export tr-report-header">${headerRow} </tr>`)
                        $('#report-table thead').append(`<tr class="exclude_export tr-report-header">${supHeaderRow} </tr>`)
                        $('#report-table thead').append(`<tr class="exclude_export tr-report-subheader">${subHeaderRow} </tr>`)
                        $('#report-table thead').append(`<tr class="tr-report-download">${csv_header}></tr>`)
                        shortagelist_construct = {}
                        var item_list;
                        for(i =1; i<= materials.length;i++){
                            var item = materials[i-1]
                            shortagelist_construct[item.item_id] = item.present_qty
                           var itemTypeFlag = "";
                            var item_name = item.item_name;
                            if(item.is_service == true){
                                itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if(item.is_stockable == 0 && item.is_service != true){
                               itemTypeFlag += `<span class='non_stock-flag'></span>`;
                            }
                            if(typeof(Storage) !== "undefined") {
                                if (sessionStorage.clickcount) {
                                    sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
                                } else {
                                    sessionStorage.clickcount = 1;
                                }
                            }
                            var requ_qty=[];
                            var pp_no_list="";
                            var pp_id_list=[];
                            var location_list=[]
                            var cat_code_list=[]
                            $.each(item.contains, function(i, contains) {
                                requ_qty.push(contains.cat_code+ "[:]" + contains.req_qty+ "[:]");
                                pp_no_list += contains.pp_no+ "[:]";
                                pp_id_list += contains.pp_id+ "[:]";
                                location_list.push(contains.location_id+ "[:]");
                                cat_code_list.push(item.item_id+ "[:]");
                            });
                            if(item.hasChildren) {
                                dataP = item.item_id+ "_" + sessionStorage.clickcount
                                item_parent_material = item.contains[1]["bom_material"]
                                sessionStorage.setItem('bom_header', JSON.stringify(pp_no))
                                childArrow = '<i class="fa fa-plus fa-for-arrow" role="button" onclick="return appendMaterial(\''
                                + item.item_id + '\',\'' + sessionStorage.clickcount + '\',\'' + dataP + '\',\'' + item_parent_material +  '\',\'' + requ_qty + '\',\'' + pp_no_list + '\',\'' + pp_id_list + '\',\'' + location_list + '\',\'' + cat_code_list + '\')"></i><a style="padding-left: 26px;display: block;margin-top: -12px;">'+item_name+itemTypeFlag+'</a>';
                            } else {childArrow = item_name+""+itemTypeFlag;}

                            var row = `<td class='bom-sno text-left'>${ i }</td>
                                <td class='text-left bom-name'>
                                    ${childArrow}
                                </td>
                                <td class='text-left item-location-id' data-location_id="${item.contains[1].location_id}">${item.drawing_no}</td>
                                <td class="text-center item-unit">${item.unit}</td>
                                <td class="text-right restore-values field-changes item-available-qty" data-toggle="modal" data-target="#available_stock_modal" data-stock='${all_location_stock}' data-item_id="${item.item_id}" style="cursor: pointer;" data-default-value="${item.present_qty.toFixed(2)}">${item.present_qty.toFixed(2)}</td>
                                <td class="text-right restore-values field-changes item-required-qty" data-default-value="${item.required_qty.toFixed(2)}">${item.required_qty.toFixed(2)}</td>
                                <td class="text-right restore-values field-changes item-shortage-qty" data-default-value="${item.shortage_qty.toFixed(2)}">${item.shortage_qty.toFixed(2)}</td>`
                            var bom_materials = item.contains
                            if(include_po == true){
                                 row = `${row} <td align="center">${item.pending_po.toFixed(2)}</td>`
                            }
                            const bom_material_values = Object.values(bom_materials);
                            result_set = {}
                            bom_material_values.forEach(function (bom_material){
                                for(j=0; j < response["bom_count"]; j++){
                                    if(bom_material.pp_no == pp_no[j] && (!result_set[pp_no[j]] || result_set[pp_no[j]] == false)){
                                          result_set[pp_no[j]] = bom_material
                                    }else if(!result_set[pp_no[j]]){
                                          result_set[pp_no[j]] = false
                                    }
                                }
                            });
                            for(const key in result_set){
                                const bom_material = result_set[key]
                                var locationQty = 0;
                                var location_id_qty = bom_material.location_id
                                var locationQtyData = response.location_wise_qty[bom_material.pp_id]
                                if (locationQtyData) {
                                    locationQtyData.forEach(data => {
                                        if (data.item_id == item.item_id && data.location_id == bom_material.location_id) {
                                            locationQty = data.qty;
                                        }
                                    });
                                }
                                row = (bom_material ? `${row}
                                            <td class="text-right field-changes restore-values item-location-qty hide exclude_export" data-location_id="${bom_material.location_id}" data-pp_id="${bom_material.pp_id}" data-bom_header="${bom_material.pp_no}" data-item_id="${item.item_id}" data-default_value="${locationQty.toFixed(2)}">${locationQty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restorse-values bom_required_qty" data-bom_header="${bom_material.pp_no}" data-req_value="${bom_material.req_qty * bom_material.rate}" data-rate_value="${bom_material.rate}" data-default-value="${bom_material.req_qty.toFixed(2)}" data-item_id="${item.item_id}">${bom_material.req_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restore-values bom_shortage_material exclude_export hide" data-item_id="${item.item_id}" data-default-value="${bom_material.short_qty.toFixed(2)}">${bom_material.short_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes location-wise-shortage" data-bom_header="${bom_material.pp_no}" data-item_id="${item.item_id}">-</td>
                                            <td align="right" class="field-changes restore-values already_alloted_qty" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.allocated_qty.toFixed(2)}" data-requested_value="${bom_material.requested_qty}">
                                            ${bom_material.allocated_qty -bom_material.issued_qty > 0 && pp_permission == "true"  ? `
                                                <div class="qty-container" style="display: flex; flex-direction: column; align-items: center;">
                                                    <div style="display: flex; align-items: center; width: 100%; justify-content: space-between;">
                                                        <span class="table-inline-icon field-changes" data-tooltip="tooltip" data-placement="left" title="" role="button" onclick="saveDeAllotedQty('${bom_material.pp_no}',${item.item_id},this)" data-original-title="Deallocate">
                                                            <i class="fa fa-arrow-down field-changes" style="color:#d53c3c;font-size: 15px;"></i>
                                                        </span>
                                                        <input type="text" class="field-changes form-control text-right deallocated_material" data-bom_header="${bom_material.pp_no}" value="${(bom_material.allocated_qty - bom_material.issued_qty).toFixed(2)}"><br/>
                                                        <span class="allocated-qty" style="width:100px;">${bom_material.allocated_qty.toFixed(2)}</span>
                                                    </div>
                                                        <span style="margin-top: 5px;">Deallocate</span>
                                                </div>` : `<span class="allocated-qty" style="width:100px;">${bom_material.allocated_qty.toFixed(2)}</span>`}

                                                </div>
                                            </td>
                                            <td align="right" class="field-changes restore-values issued_qty" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.issued_qty.toFixed(2)}">${bom_material.issued_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes exclude_export"> <input type="text" class="field-changes form-control text-right bom_alloted_material" onfocus="checkDuplicate(this,'${selected_pp_nos}')" data-bom_header="${bom_material.pp_no}" data-item_id="${item.item_id}" data-default-value="${bom_material.allocated_qty.toFixed(2)}"/> </td>` : `${row}
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right" class="exclude_export"> - </td>`)
                            }
                            $('#report-table tbody').append(`<tr data-toggle='close' data-padding='0' align="center" valign="middle" data-id="${ item.item_id }" id= "${ item.item_id }_${ sessionStorage.clickcount }" >${row} </tr>`);
                        }
                        $('#report-table tbody tr').each(function () {
                            const row = $(this);
                            const item_id = row.data('id');
                            response.pp_no_list.forEach((pp_no, index) => {
                                const isFirst = index == 0;
                                fetchLocationQty(row, item_id, pp_no,null,isFirst);
                                initializeLocationQty(pp_no,isFirst);
                            });
                        });
                        sessionStorage.setItem("shortagelist_construct" , JSON.stringify(shortagelist_construct))
                        if ((response.child_materials && response.child_materials.length > 0)) {
                            response.child_materials.forEach(childMaterial => {
                                const parentId = Object.values(childMaterial)[0].parent_id;
                                const $icon = $(`#report-table tr[data-id="${parentId}"] .fa-plus`);
                                if ($icon.length) {
                                    $icon.trigger('click');
                                }
                            });
                        }
                    }
                    else{
                    $('#report-table tbody').append(`<tr><th class="text-center" style="width: 100px; min-width: 100px; max-width: 100px">No Matching Results Found!</td></tr>`);
                    }
                } catch(e) {
                    console.log(e);
                } finally {
                    $('#loading').hide();
                    $("#loadingmessage_changelog_listing_ie").hide();
                }
            },
            error: function (xhr, errmsg, err) {
                console.log(xhr.status + ": " + xhr.responseText);
            }
        });

        function setDragableSno() {
            var dragRowSno = 0;
            $('#shortage-list-table .drag_serial_number').each(function(){
                $(this).text(Number(++dragRowSno));
            });
        }
        $('.material_shortage_report').removeClass("hide");
}

function appendMaterial(cat_code, dataChild, dataP, parent_material_name, requ_qty,pp_no_list,pp_id_list,location_list,cat_code_list){
    var include_po = $("#include_item_order").is(":checked")
    var dataParent = dataP;
    var bom_header = JSON.parse(sessionStorage.getItem('bom_header'))
    var shortagelist_construct = JSON.parse(sessionStorage.getItem("shortagelist_construct"))
    var current_bom_sno = $("#"+dataP).find(".bom-sno").text().trim();
    var constructedRow = '';
    var bom_count = bom_header.length
    var pp_permission = $('#pp_permission').val();
    var headerRow =`<th rowspan="3" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">S.No.</th>
                    <th rowspan="3" style="width: 250px; min-width: 250px; max-width: 250px;" class="align-middle">Name</th>
                    <th rowspan="3" style="width: 150px; min-width: 150px; max-width: 150px;" class="align-middle">Drawing No</th>
                    <th rowspan="3" style="width: 60px; min-width: 60px; max-width: 60px;" class="align-middle">UOM</th>
                    <th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Allocated Stock </th>
                    <th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock </th>
                    <th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Location Wise Available Stock </th>
                    <th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Required</th>
                    <th rowspan="3" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage</th>`

    var csv_header =  `<th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">S. No</th>
                        <th hidden="hidden" style="width: 250px; min-width: 250px; max-width: 250px;">Name</th>
                        <th hidden="hidden" style="width: 150px; min-width: 150px; max-width: 150px;">Drawing No</th>

                        <th hidden="hidden" style="width: 60px; min-width: 60px; max-width: 60px;">UOM</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Available Stock - Qty</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Required - Qty</th>
                        <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">Shortage - Qty</th>`

    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#report-table #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#report-table #"+cat_code + "_" + dataChild).removeClass('exclude_export');
        $("#report-table #"+cat_code + "_" + dataChild).attr('data-toggle','close');
        $("#"+dataP).find(".field-changes").removeClass('disabled-field').addClass('enabled-field');
        shortageListConstruct();
        const pp_no_list_array = pp_no_list.split('[:]');
        pp_no_list_array.forEach(function(pp_no) {
            if(pp_no){
                removeDuplicate(pp_no);
            }
        });
    }
    else {
        $("#report-table #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#report-table #"+cat_code + "_" + dataChild).addClass('exclude_export');
        $("#report-table #"+cat_code + "_" + dataChild).attr('data-toggle','open');
        $("#"+dataP).find(".field-changes").removeClass('enabled-field').addClass('disabled-field');
        $("#loadingmessage_changelog_listing_ie").show();

        $.ajax({
            url: "/erp/stores/appendmaterialshortagelistdata/",
            type: "post",
            datatype: "json",
            data: {"cat_list": requ_qty, "include_po": include_po, "cat_code": cat_code, "pp_no_list":pp_no_list,"pp_id_list":pp_id_list,"location_list":location_list,"cat_code_list":cat_code_list},
            success: function(response) {
                try {
                    if (response.response_message == "Internal server error") {
                        swal("", response.custom_message, "error")
                    }
                    else {
                        var materials = response["data"]
                        var subHeaderRow =  ""
                        if(include_po == true){
                            headerRow = `${headerRow} <th style="width: 100px; min-width: 100px; max-width: 100px;" align="center" rowspan="3">Pending PO</th>`
                            csv_header = `${csv_header} <th hidden="hidden">Pending PO</th>`
                        }
                        all_location_stock = JSON.stringify(response['stock_transfer_req_location_qty'])
                        for(i=0; i<bom_count; i++){
                            headerRow = `${headerRow} <th style="width: 300px; min-width: 300px; max-width: 300px;" align="left" colspan="3">${bom_header[i]}</th>`
                            csv_header = `${csv_header} <th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Required qty</th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Shortage qty</th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Allocated qty</th><th hidden="hidden" style="width: 100px; min-width: 100px; max-width: 100px;">${bom_header[i]} - Issued qty</th>`
                        }
                        csv_header += `<th style="display: none;"></th>`;
                        for(i =1; i<= materials.length;i++){
                            var item = materials[i-1]
                            shortagelist_construct[item.item_id] = item.present_qty

                            if(typeof(Storage) !== "undefined") {
                                if (sessionStorage.clickcount) {
                                    sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                                } else {
                                    sessionStorage.clickcount = 1;
                                }
                            }
                            var itemTypeFlag = "";
                            var item_name = item.item_name;
                            if(item.is_service == true){
                                itemTypeFlag += `<span class="service-item-flag"></span>`;
                            }
                            if(item.is_stockable == 0 && item.is_service != true){
                               itemTypeFlag += `<span class='non_stock-flag'></span>`;
                            }
                            var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                            var requ_qty=""
                            $.each(item.contains, function(i, contains) {
                            requ_qty += contains.cat_code+ "[:]" + contains.req_qty+ "[:]"
                            });

                            if(item.hasChildren) {
                                dataP = item.item_id+ "_" + sessionStorage.clickcount
                                item_parent_material = parent_material_name
                                childArrow = '<a  style="display: block;margin-top: -12px;">'+item_name+itemTypeFlag+'</a>';
                            } else {childArrow = item_name+""+itemTypeFlag;}

                            var row = `<td class='bom-sno text-left'>${current_bom_sno}.${ i }</td>
                                <td class='text-left' style='padding-left:${dataPadding-20}px'>
                                <span style='padding-left:30px; display: block' class='tree-view bom-name'>
                                    ${childArrow}
                                </span>
                                </td>
                                <td class='text-left'>${item.drawing_no}</td>
                                <td class="text-center">${item.unit}</td>
                                <td class="text-right restore-values field-changes item-available-qty" data-toggle="modal" data-target="#available_stock_modal" data-stock='${all_location_stock}' data-item_id="${item.item_id}" style="cursor: pointer;" data-default-value="${item.present_qty.toFixed(2)}">${item.present_qty.toFixed(2)}</td>
                                <td class="text-right restore-values field-changes item-required-qty" data-default-value="${item.required_qty.toFixed(2)}">${item.required_qty.toFixed(2)}</td>
                                <td class="text-right restore-values field-changes item-shortage-qty" data-default-value="${item.shortage_qty.toFixed(2)}">${item.shortage_qty.toFixed(2)}</td>`
                            var bom_materials = item.contains
                            if(include_po == true){
                                 row = `${row} <td align="center">${item.pending_po.toFixed(2)}</td>`
                            }
                            const bom_material_values = Object.values(bom_materials);
                            result_set = {}
                            bom_material_values.forEach(function (bom_material){
                                for(j=0; j < bom_count; j++){
                                    if(bom_header[j] != ''){
                                        if(bom_material.pp_no == bom_header[j] && (!result_set[bom_header[j]] || result_set[bom_header[j]] == false)){
                                              result_set[bom_header[j]] = bom_material
                                        }else if(!result_set[bom_header[j]]){
                                              result_set[bom_header[j]] = false
                                        }
                                    }
                                }
                            });
                            for(const key in result_set){
                                const bom_material = result_set[key]
                                var locationQty = 0;
                                var location_id_qty = bom_material.location_id
                                var locationQtyData = response.location_wise_qty
                                var locationQtyData = response.location_wise_qty[bom_material.pp_id]
                                 if (locationQtyData) {
                                    locationQtyData.forEach(data => {
                                        if (data.item_id == item.item_id && data.location_id == bom_material.location_id) {
                                            locationQty = data.qty;
                                        }
                                    });
                                }
                                row = (bom_material ? `${row}
                                            <td class="text-right field-changes restore-values item-location-qty exclude_export hide"  data-location_id="${bom_material.location_id}" data-pp_id="${bom_material.pp_id}" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default_value="${locationQty.toFixed(2)}">${locationQty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restore-values bom_required_qty" data-req_value="${bom_material.req_qty * bom_material.rate}" data-rate_value="${bom_material.rate}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.req_qty.toFixed(2)}" data-item_id="${item.item_id}" data-parent_id="${item.cat_code}" >${bom_material.req_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes restore-values bom_shortage_material exclude_export hide" data-default-value="${bom_material.short_qty.toFixed(2)}">${bom_material.short_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes location-wise-shortage" data-bom_header="${bom_material.pp_no}" data-item_id="${item.item_id}">-</td>
                                            <td align="right" class="field-changes restore-values already_alloted_qty" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.allocated_qty.toFixed(2)}" data-requested_value="${bom_material.requested_qty}">
                                            ${bom_material.allocated_qty -bom_material.issued_qty > 0 && pp_permission == "true" ? `
                                                <div class="qty-container" style="display: flex; flex-direction: column; align-items: center;">
                                                    <div style="display: flex; align-items: center; width: 100%; justify-content: space-between;">
                                                        <span class="table-inline-icon field-changes" data-tooltip="tooltip" data-placement="left" title="" role="button" onclick="saveDeAllotedQty('${bom_material.pp_no}',${item.item_id},this)" data-original-title="Deallocate">
                                                            <i class="fa fa-arrow-down" style="color:#d53c3c;font-size: 15px;"></i>
                                                        </span>
                                                        <input type="text" class="field-changes form-control text-right deallocated_material" data-bom_header="${bom_material.pp_no}" value="${(bom_material.allocated_qty - bom_material.issued_qty).toFixed(2)}"><br/>
                                                        <span class="allocated-qty"style="width: 100px;" >${bom_material.allocated_qty.toFixed(2)}</span>
                                                    </div>
                                                        <span style="margin-top: 5px;">Deallocate</span>
                                                </div>` : `<span class="allocated-qty" style="width: 100px;" >${bom_material.allocated_qty.toFixed(2)}</span>`}

                                                </div>
                                            </td>
                                            <td align="right" class="field-changes restore-values issued_qty" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.issued_qty.toFixed(2)}">${bom_material.issued_qty.toFixed(2)}</td>
                                            <td align="right" class="field-changes exclude_export"> <input type="text" onfocus="checkDuplicate(this,'${pp_no_list}')" class="field-changes form-control text-right bom_alloted_material" data-item_id="${item.item_id}" data-bom_header="${bom_material.pp_no}" data-default-value="${bom_material.allocated_qty.toFixed(2)}"/></td>` : `${row}
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right"> - </td>
                                            <td align="right" class="exclude_export"> - </td>`)
                            }
                            constructedRow += `<tr data-toggle='close' data-padding="${ dataPadding }" align="center" valign="middle" data-id="${ item.item_id }" data-parent= "${ dataParent }" id= "${ item.item_id }_${ sessionStorage.clickcount }" data-child="${ item.cat_code }_${ dataChild }">${row}</tr>`;
                        }
                        sessionStorage.setItem("shortagelist_construct" , JSON.stringify(shortagelist_construct))
                        $('#report-table #'+dataParent).after(constructedRow);
                        let seenDataIds = {};
                            Object.entries(result_set).forEach(([key, bom_material], index) => {
                                const isFirst = index == 0;
                                const rows = $(`#report-table tbody tr td.bom_required_qty[data-parent_id="${cat_code}"]`).closest('tr');
                                $(rows).each(function () {
                                    const row = $(this);
                                    const item_id = row.data('id');
                                    if (bom_material) {
                                        const parent_id = bom_material.parent_id || null;
                                        fetchLocationQty(row, item_id, bom_material.pp_no, parent_id, isFirst);
                                    }
                                });
                            });
                            bom_header.forEach(pp_no => {
                                if(pp_no){
                                    initializeLocationQty(pp_no);
                                }
                            });
                    }
                } catch(e) {
                    console.log(e);
                } finally {
                    $('#loading').hide();
                $("#loadingmessage_changelog_listing_ie").hide();
                }
            },

        });
}
}

function shortageListConstruct(){
    var shortagelist_data = JSON.parse(sessionStorage.getItem("shortagelist_construct"));
    $.each(shortagelist_data, function(item_id, available_qty) {
        var currentAvaialbeQty = available_qty;
        $("#report-table").find("tr[data-toggle='close']").each(function(){
            if (item_id == $(this).attr('data-id')){
                $(this).find(".item-available-qty").text(currentAvaialbeQty.toFixed(2));
                var bom_cur_ava = $(this).find(".item-available-qty").text()
                $(this).find(".bom_required_qty").each(function(){
                    var bom_cur_req = $(this).text()
                    var bom_cur_balance = Number(bom_cur_ava) -  Number(bom_cur_req)
                    var bom_shortQty = 0;

                    if (bom_cur_balance >= 0){
                        bom_shortQty = 0
                        bom_cur_ava = bom_cur_balance;
                    }
                    else {
                        bom_shortQty = bom_cur_balance * (-1);
                        bom_cur_ava = 0;
                    }
                    $(this).next("td").text(Number(bom_shortQty).toFixed(2));
                });

                var balanceQty = Number(currentAvaialbeQty) - Number($(this).find(".item-required-qty").text()).toFixed(2);
                var shortageQty = 0;
                if(balanceQty > 0) {
                    shortageQty = 0;
                    currentAvaialbeQty = balanceQty;
                }
                else {
                    shortageQty = balanceQty * (-1);
                    currentAvaialbeQty = 0;
                }
                $(this).find(".item-shortage-qty").text(Number(shortageQty).toFixed(2));
            }
        });
    });
}
function saveAllAllotedQty(pp_no,$allot_button) {
    $allot_button.prop('disabled',true);
    let isValid = true;
    let errorMsg = '';
    let allocatedMaterials = [];
    let allValuesAreZero = true;
    let duplicateAllocatedQty = {};
    $('#report-table tbody tr').each(function () {
        const $row = $(this);
        const itemId = $row.find('.bom_required_qty').data('item_id');
        let is_expanded = 0;
        $('#report-table tbody tr').each(function () {
            const otherRowItemId = $(this).find('.bom_required_qty').data('item_id');
            if (itemId == otherRowItemId && $(this)[0] != $row[0]) {
                is_expanded = 1;
                return false;
            }
        });
        $row.find('.bom_required_qty').filter(function () {
            return $(this).data('bom_header') == pp_no;
        }).each(function () {
            const itemId = $(this).data('item_id');
            const parentIdData = $row.find(".bom_required_qty[data-bom_header='" + pp_no + "']").data('parent_id');
            const parentId = typeof parentIdData !== 'undefined' ? parseFloat(parentIdData) : null;

            const requiredQty = parseFloat($row.find(".bom_required_qty[data-bom_header='" + pp_no + "']").text());
            const shortageQty = parseFloat($row.find(".location-wise-shortage[data-bom_header='" + pp_no + "']").text());
            const locationQty = parseFloat($row.find(".item-location-qty[data-bom_header='" + pp_no + "']").text());
            const pp_id = parseFloat($row.find(".item-location-qty[data-bom_header='" + pp_no + "']").data("pp_id"));
            const location_id = parseFloat($row.find('.item-location-qty[data-bom_header="' + pp_no + '"]').data('location_id'));
            if ($row.find('td i.fa-minus').length > 0) {
                is_expanded = 1;
            } else if ($row.find('td i.fa-plus').length > 0 || $row.find('td i.fa-minus').length == 0) {
                is_expanded = 0;
            }

            const $allocatedInput = $row.find(".bom_alloted_material[data-bom_header='" + pp_no + "']:not(.disabled-field)").filter(function () {
                return $(this).data('bom_header') == pp_no;
            });

            const already_alloted_qty = parseFloat($row.find('.already_alloted_qty[data-bom_header="' + pp_no + '"]').data('default-value'));
            const inputValue = parseFloat($allocatedInput.val()) || 0;
            if (inputValue > 0) allValuesAreZero = false;

            const allocatedQty = is_expanded ? 0 : inputValue;
            if (((allocatedQty > requiredQty - already_alloted_qty) || inputValue > locationQty) && inputValue != 0) {
                isValid = false;
                errorMsg = `Allocated quantity cannot exceed ${shortageQty == 0 ? 'required' : 'location'} quantity.\n`;
                $allocatedInput.addClass('error-border');
            } else {
                $allocatedInput.removeClass('error-border');
            }

            if (!duplicateAllocatedQty[itemId]) {
                duplicateAllocatedQty[itemId] = {
                    totalAllocatedQty: 0,
                    count: 0,
                    locationQty: locationQty,
                    rows: []
                };
            }

            duplicateAllocatedQty[itemId].totalAllocatedQty += inputValue;
            duplicateAllocatedQty[itemId].count++;
            duplicateAllocatedQty[itemId].rows.push($row);
            for (const itemId in duplicateAllocatedQty) {
                const { totalAllocatedQty, count, locationQty, rows } = duplicateAllocatedQty[itemId];
                if (count >= 3 && totalAllocatedQty > locationQty && inputValue != 0) {
                    isValid = false;
                    errorMsg = `Total allocated quantity across duplicate rows exceeds the location quantity.`;

                    rows.forEach(function ($row) {
                        $row.find('.bom_alloted_material').addClass('error-border');
                    });
                    break;
                }
            }
            allocatedMaterials.push({
                item_id: itemId,
                parent_id: parentId,
                pp_no: pp_no,
                allocated_qty: allocatedQty,
                required_qty: requiredQty,
                pp_id: pp_id,
                location_id: location_id,
                is_expanded : is_expanded
            });
        });
    });
    if (allValuesAreZero) {
        swal("Warning", "All allocated quantities are zero.", "warning");
        $allot_button.prop('disabled',false);
        return;
    }

    if (!isValid) {
        swal(errorMsg);
        $allot_button.prop('disabled',false);
        return;
    }

    $.ajax({
        url: "/erp/production/json/allocated_material_quantity/",
        type: "POST",
        dataType: "json",
        data: {
            "allocated_materials": JSON.stringify(allocatedMaterials),
            "enterprise_id": $('#enterprise_id').val(),
            "status" : 1
        },
        success: function () {
            const successMessage = "Materials have been allocated successfully";
            swal({ title: "", text: successMessage, type: "success" }, function () {
                localStorage.setItem("triggerGenerateShortageReportPP", "true");
                $('#loading').show();
                window.location.reload();
            });
        },
        error: function (xhr) {
            console.error(xhr.status + ": " + xhr.responseText);
        }
    });
}


function filterShortageMaterials() {
    let shortageItems = [];
    $('#report-table tbody tr').each(function() {
        let shortageQty = parseFloat($(this).find('.item-shortage-qty').text().trim());
        if (shortageQty > 0) {
            let ppNo = $(this).data('pp_no');
            let location = $(this).data('location_id');
            let materialName = $(this).find('.bom-sno').next().text().trim();
            shortageItems.push({
                material_name: materialName,
                pp_no: ppNo,
                location: location,
                shortage_qty: shortageQty
            });
        }
    });
    return shortageItems;
}

function populateStockTransferModal(shortageItems) {
    let modalBody = $('#stock_transfer_request_modal .modal-body');
    modalBody.empty();

    if (shortageItems.length > 0) {
        let tableContent = `<table class="table">
                                <thead>
                                    <tr>
                                        <th>Material</th>
                                        <th>PP No</th>
                                        <th>Location</th>
                                        <th>Shortage Qty</th>
                                    </tr>
                                </thead>
                                <tbody>`;

        shortageItems.forEach(function(item) {
            tableContent += `<tr>
                                <td>${item.material_name}</td>
                                <td>${item.pp_no}</td>
                                <td>${item.location}</td>
                                <td>${item.shortage_qty.toFixed(2)}</td>
                             </tr>`;
        });

        tableContent += `</tbody></table>`;
        modalBody.append(tableContent);
    } else {
        modalBody.append('<p>No shortage materials found.</p>');
    }

    $('#stock_transfer_request_modal').modal('show');
}
$(document).on("click", "#transferReq", function() {
    let shortageItems = filterShortageMaterials();
    populateStockTransferModal(shortageItems);
});


const createLocationMap = (locations) => {
    const locationMap = {};
    locations.forEach(location => {
        locationMap[location.name] = location.id;
    });
    return locationMap;
};
function fetchLocationQty(row, item_id, pp_no, parent_id=null,isFirst=null) {
    locationQty = parseFloat($(row).find(".item-location-qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").text());
    var requiredQtySelector = ".bom_required_qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']";
    if (parent_id != null) {
        requiredQtySelector += "[data-parent_id='" + parent_id + "']";
    }
    var requiredQty = parseFloat($(row).find(requiredQtySelector).text());
    const issued_qty = parseFloat($(row).find('.issued_qty').text()) || 0;
    var already_alloted_qty = $(row).find(`.already_alloted_qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).find('.allocated-qty').length ? parseFloat($(row).find(`.already_alloted_qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).find('.allocated-qty').text()) || 0 : parseFloat($(row).find(`.already_alloted_qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).text()) || 0;
    var shortageQty = (locationQty - (requiredQty-already_alloted_qty)) < 0 ? Math.abs(locationQty - (requiredQty-already_alloted_qty)).toFixed(2) : 0;
    $(row).find(".location-wise-shortage[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").text(shortageQty);

    $(row).find(".item-location-qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").text(locationQty.toFixed(2));
    if(isFirst){
        if (already_alloted_qty > 0) {
            let parent_id = $(row).find(".bom_required_qty").data('parent_id');
            if (parent_id) {
                let parentRow = $('tr[data-id="' + parent_id + '"]');
                parentRow.find('td i.fa-minus').addClass('disabled');
            } else {
                let item_id = $(row).data('item_id');
                $(row).find('td i.fa-plus').addClass('disabled-field');
            }
        }
        else{
            if (parent_id) {
                let parentRow = $('tr[data-id="' + parent_id + '"]');
                parentRow.find('td i.fa-minus').removeClass('disabled');
            } else {
                let item_id = $(row).data('item_id');
                $(row).find('td i.fa-plus').removeClass('disabled-field');
            }
        }
    }

    if (locationQty > 0 && already_alloted_qty != requiredQty ) {
        if (locationQty > (requiredQty - already_alloted_qty)) {
            $(row).find(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").val((requiredQty - already_alloted_qty).toFixed(2));
        } else {
                $(row).find(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").val(locationQty.toFixed(2));
        }
        $(row).find(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").prop('disabled', false);
    } else {
        $(row).find(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").val(0.00);
        $(row).find(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").prop('disabled', true);
    }
    QTipInit();
}

    async function triggerCreateStockTransfer(pp_no, pp_id, transfers){
        const button = $(`.stock-transfer-btn[data-pp-id="${pp_id}"]`);
        button.addClass('disabled-field')

      try{
        var enterprise_id = $("#enterprise_id").val();
        var created_by = $('#login_user_id').val();
        var validationPassed = true
        var totalShortage = 0
        var totalRequested = 0
        var stockZeroFlag = false
        var transfer_id = ''
        var transfer_no = null
        var status = null;

        var transferDetails = [];
        var groupedItems = {};
        var expandedMaterials = {};
        var transfer_list = JSON.parse(decodeURIComponent(transfers))
        var filteredTransferList = transfer_list.filter(transferItem => {
            return !(transferItem['status'] === 4 || transferItem['status'] === 3);
        })
        var transferNumbers = filteredTransferList.map(transferItem => transferItem['transfer_no']);

            $("#report-table tbody tr").each(function(){
                var row = $(this)
                if (row.find('td.disabled-field').length > 0) {
                    validationPassed = false
                    return;
                }
                var item_id = row.data('id');
                var parent_id = row.find(".bom_required_qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").data('parent_id');
                var item_name = row.find('.bom-name').text()
                var quantity = parseFloat(row.find(".location-wise-shortage[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").text());
                var required_qty = row.find(".bom_required_qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").data('default-value') || 0;
                var requested_qty = 0;
                if (status !== 1 || status !==2) {
                    requested_qty = row.find(".already_alloted_qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").data('requested_value') || 0;
                }
                var allowed_qty = Math.abs(quantity - requested_qty) || 0
                var all_location_stock = row.find(".item-available-qty").data('stock')
                var from_location_id = "";
                var from_location_name = "";
                var unit = row.find(".item-unit[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").text();
                var to_location_id = row.find(".item-location-qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").data('location_id');
                var rate = row.find(".bom_required_qty[data-bom_header='" + pp_no + "'][data-item_id='" + item_id + "']").data('rate_value');
                var is_faulty = 0;
                totalShortage += quantity
                totalRequested += requested_qty

                if(!allowed_qty || (quantity <= 0) || (allowed_qty <= 0)  || (requested_qty == required_qty)){
                    validationPassed = false;
                    return
                }
                if(quantity == 0){
                    quantityZeroFlag = true;
                }else if (quantity > allowed_qty) {
                    quantity = allowed_qty;
                }

                if (all_location_stock) {
                    var filteredData = all_location_stock
                        .filter(stock => stock.id === item_id)
                        .filter(stock => stock.location_id !== to_location_id)
                        .filter(stock => stock.Closing > 0);
                    if (filteredData.length > 0) {
                        var sortedData = filteredData.sort((a, b) => {
                            var diffA = Math.abs(a.Closing - quantity);
                            var diffB = Math.abs(b.Closing - quantity);
                            return diffA - diffB;
                        })
                        var remainingQty = quantity;
                        if(status === 2 && status === 1){
                            remainingQty = quantity - requested_qty
                        }
                        var selected_location = sortedData[0];
                        var transferQty = Math.min(selected_location.Closing, remainingQty)
                        var key = `${item_id}_${selected_location.location_id}_${to_location_id}`;
                        if (!groupedItems[key]) {
                            groupedItems[key] = {
                                item_id: item_id,
                                enterprise_id: enterprise_id,
                                transfer_id: transfer_id,
                                pp_id: pp_id,
                                parent_id: parent_id,
                                is_faulty: is_faulty,
                                quantity: 0,
                                rate: rate,
                                unit: unit,
                                to_location_id: to_location_id,
                                from_location_id: selected_location.location_id,
                                from_location_name: selected_location.location_name
                            };
                        }
                        groupedItems[key].quantity = transferQty;
                        remainingQty -= transferQty;
                    }else{
                        stockZeroFlag = true
                    }
                }
            });
        transferDetails = Object.values(groupedItems)
        if(totalShortage == 0 && transferDetails.length == 0){
            swal("", "There is no shortage to transfer", "error")
        }
        if(stockZeroFlag && totalShortage > 0){
            validationPassed = false
            swal("", "There is no available stock in other locations", "error")
        }else if (transferDetails.length == 0 && totalShortage > 0){
            swal("", `Stock transfer already raised. Refer transfer no: ${transferNumbers}`)
        }

        if(transferDetails.length > 0){
             validationPassed = true
        }
        if(validationPassed){
            var groupedTransfers = {};
            transferDetails.forEach(transfer => {
                 if(transfer.from_location_id && transfer.to_location_id){
                     var key = `${transfer.from_location_id}-${transfer.to_location_id}`;
                        if (!groupedTransfers[key]) {
                            groupedTransfers[key] = [];
                        }
                        groupedTransfers[key].push(transfer);
                 }
            });
            if(groupedTransfers){
                    const responses = await Promise.all(Object.keys(groupedTransfers).map(async (key) => {
                        var transferGroup = groupedTransfers[key];
                        var fromLocation = transferGroup[0].from_location_id;
                        var fromLocationName = transferGroup[0].from_location_name;
                        var toLocation = transferGroup[0].to_location_id;
                        var pp_id = transferGroup[0].pp_id;

                        var materials = transferGroup.map(transfer => ({
                            item_id: transfer.item_id,
                            parent_id:transfer.parent_id,
                            quantity: transfer.quantity,
                            rate: transfer.rate,
                            unit:transfer.unit,
                            is_faulty:transfer.is_faulty,
                        }));

                        var total_qty = materials.reduce((sum, item) => sum + item.quantity, 0);
                        var total_value = materials.reduce((sum, item) => sum + (item.quantity * item.rate), 0);
                        var remarks = `Stock transfer request was raised against the ${pp_no}`

                    var transferData = {
                        "enterprise_id": enterprise_id,
                        "from_location": fromLocation,
                        "from_location_name": fromLocationName,
                        "to_location": toLocation,
                        "status": 1,
                        "pp_id":pp_id,
                        "total_qty":total_qty,
                        "total_value":total_value,
                        "transfer_details": JSON.stringify(materials),
                        "last_modified_by": created_by,
                        "created_by":created_by,
                        "remarks":remarks,
                        "id":transfer_id ,
                    };

                        try {
                            const response = await $.ajax({
                                url: "/erp/stores/json/stock_transfer/",
                                type: "post",
                                dataType: "json",
                                data:transferData,
                            });
                            return {
                                success: response.response_code === 200,
                                data: response,
                                action: (filteredTransferList.length > 0) ? "updated" : "created",
                            }
                        }catch(error) {
                            console.log("Error in AJAX request:", error);
                            return { success: false, action:"failed"}
                        }
                    }));
                    const successfulResponse = responses.filter(res => res.success);
                    const failedResponse = responses.filter(res => !res.success);

                    const updatedTransfers = successfulResponse.filter(res => res.action === "updated").map(res => res.data.data.code);
                    const createdTransfers = successfulResponse.filter(res => res.action === "created").map(res => res.data.data.code);

                    let message = '';
                    if (updatedTransfers.length > 0) {
                        message += `Stock transfer request updated. Refer Transfer number: ${updatedTransfers.join(', ')}.\n`
                    }
                    if (createdTransfers.length > 0) {
                        message += `Stock transfer request created. Refer Transfer number: ${createdTransfers.join(', ')}.\n`;
                    }
                    if (failedResponse.length > 0) {
                        message += `${failedResponse.length} transfer requests failed.`;
                    }

                    swal({
                    title: "",
                    text: message,
                    type: successfulResponse.length > 0 ? "success" : "error"
                    }, function () {
                        localStorage.setItem("triggerGenerateShortageReportPP", "true");
                        $('#loading').show();
                        window.location.reload();
                    });
            }
        }else if(totalShortage === totalRequested && transferDetails.length == 0 && transferNumbers.length > 0){
            swal("", `Stock transfer requested completely. Refer transfer no: ${transferNumbers}`, "info")
        }
      }catch (error){
        console.error("Error in stock transfer:", error);
        swal("", "An unexpected error occurred. Please try again.", "error");
      }finally{
            button.removeClass('disabled-field')
     }
    }

$(document).ready(function() {
    if (localStorage.getItem("triggerGenerateShortageReportPP") == "true") {
        $("#loadingmessage_changelog_listing_ie").show();
        generateShortageReportPP();
        localStorage.removeItem("triggerGenerateShortageReportPP");
    }
});
function checkDuplicate(inputElement, selected_pp_nos) {
    const $changedRow = $(inputElement).closest('tr');
    const itemId = $changedRow.data('id');
    const pp_no = $(inputElement).data('bom_header');
    $('#report-table tbody tr').each(function () {
        $(this).css('background-color', '');
        $('#report-table tbody .custom-background').removeClass('custom-background');
    });
    let pp_no_list = selected_pp_nos.split('[:]');
    if (pp_no_list.includes(pp_no)) {
        let duplicateCount = 0;
        $('#report-table tbody tr').each(function () {
            const rowItemId = $(this).data('id');
            let rowHasMatch = false;
            $(this).find('.bom_alloted_material').each(function () {
                const rowPpNo = $(this).data('bom_header');
                if (rowItemId == itemId && rowPpNo == pp_no) {
                    rowHasMatch = true;
                    return false;
                }
            });
            if (rowHasMatch) {
                duplicateCount++;
            }
        });
        if (duplicateCount > 1) {
            $('#report-table tbody tr').each(function () {
                const rowItemId = $(this).data('id');
                let rowHasMatch = false;
                $(this).find('.bom_alloted_material').each(function () {
                    const rowPpNo = $(this).data('bom_header');
                    if (rowItemId == itemId && rowPpNo == pp_no) {
                        rowHasMatch = true;
                        return false;
                    }
                });
                if (rowHasMatch) {
                    $(this).addClass('custom-background');
                    $(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").addClass('custom-background');
                    $(".bom_required_qty[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").addClass('custom-background');
                    $(".location-wise-shortage[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").addClass('custom-background');
                    $(".already_alloted_qty[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").addClass('custom-background');
                    $(".issued_qty[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").addClass('custom-background');
                    $(".bom_alloted_material[data-bom_header='" + pp_no + "'][data-item_id='" + itemId + "']").closest('td').addClass('custom-background');
                }
            });
        }

    }else {
        swal("", `Stock transfer already raised. Refer transfer_no ${transfer_no}`, "");
    }
}
function initializeLocationQty(pp_no,isFirst=null) {
    const processedItems = {};
    $('#report-table tbody tr').each(function () {
        const $currentRow = $(this);
        const item_id = $currentRow.data('id');
        const key = `${item_id}`;
        if (!processedItems[key]) {
            processedItems[key] = { firstRow: null, ppData: {}, totalRowShortageQty: 0, totalRowRequiredQty: 0,};
        }
        if (!processedItems[key].ppData[pp_no]) {
            processedItems[key].ppData[pp_no] = {
                totalRequiredQty: 0,
                totalShortageQty: 0,
                totalDeallocated: 0,
                totalAlreadyAllotted: 0,
                totalIssued: 0,
            };
        }
        processedItems[key].totalRowShortageQty += parseFloat($currentRow.find(`.item-shortage-qty`).data('default-value') || 0);
        processedItems[key].totalRowRequiredQty += parseFloat($currentRow.find(`.item-required-qty`).data('default-value') || 0);
        const ppData = processedItems[key].ppData[pp_no];
        ppData.totalRequiredQty += parseFloat($currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).data('default-value') || 0);
        let shortageQty = $currentRow.find(`.bom_shortage_material[data-bom_header='${pp_no}']`).data('default-value');
        ppData.totalShortageQty += (shortageQty === '-' || shortageQty === '') ? 0 : parseFloat(shortageQty) || 0;
        ppData.totalAlreadyAllotted += parseFloat($currentRow.find(`.already_alloted_qty[data-bom_header='${pp_no}']`).data('default-value') || 0);
        ppData.totalDeallocated += parseFloat($currentRow.find(`.deallocated_material[data-bom_header='${pp_no}']`).val() || 0);
        ppData.totalIssued += parseFloat($currentRow.find(`.issued_qty[data-bom_header='${pp_no}']`).data('default-value') || 0);
        if (!processedItems[key].firstRow && $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).length > 0) {
            processedItems[key].firstRow = $currentRow;
        }
    });
    $('#report-table tbody tr').each(function () {
        const $currentRow = $(this);
        const item_id = $currentRow.data('id');
        const key = `${item_id}`;

        if (processedItems[key]) {
            const ppDataMap = processedItems[key].ppData;
            const firstRow = processedItems[key].firstRow;

            if ($currentRow.is(firstRow)) {
                const ppData = ppDataMap[pp_no];
                $currentRow.find(`.item-shortage-qty`).text(processedItems[key].totalRowShortageQty.toFixed(2));
                $currentRow.find(`.item-required-qty`).text(processedItems[key].totalRowRequiredQty.toFixed(2));
                $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).text(ppData.totalRequiredQty.toFixed(2));
                $currentRow.find(`.location-wise-shortage[data-bom_header='${pp_no}']`).text(
                    Math.max(0, (ppData.totalRequiredQty - ppData.totalAlreadyAllotted) - parseFloat($currentRow.find(`.item-location-qty[data-bom_header='${pp_no}']`).text() || 0)).toFixed(2)
                );
                $currentRow.find(`.already_alloted_qty[data-bom_header='${pp_no}'] .allocated-qty`).text(ppData.totalAlreadyAllotted.toFixed(2));
                $currentRow.find(`.deallocated_material[data-bom_header='${pp_no}']`).val(ppData.totalDeallocated.toFixed(2));
                $currentRow.find(`.issued_qty[data-bom_header='${pp_no}']`).text(ppData.totalIssued.toFixed(2));
                if($currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).text() != $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).data('default-value')){
                    $currentRow
                        .find(`.bom_alloted_material[data-bom_header='${pp_no}'][data-item_id='${item_id}']`)
                        .val(() => {
                            const locationQty = parseFloat($currentRow.find(`.item-location-qty[data-bom_header='${pp_no}']`).text() || 0);
                            const remainingQty = ppData.totalRequiredQty - ppData.totalAlreadyAllotted;
                            const allotQty = locationQty > remainingQty ? remainingQty : locationQty;
                            return allotQty <= 0 ? 0 : allotQty.toFixed(2);
                        })
                        .prop('disabled', () => {
                            const locationQty = parseFloat($currentRow.find(`.item-location-qty[data-bom_header='${pp_no}']`).text() || 0);
                            const remainingQty = ppData.totalRequiredQty - ppData.totalAlreadyAllotted;
                            return locationQty > remainingQty ? remainingQty < 0 : locationQty < 0;
                    });
                }
            } else {
                $currentRow.find(`.item-shortage-qty`).text(0);
                $currentRow.find(`.item-required-qty`).text(0);
                $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).text(0);
                $currentRow.find(`.location-wise-shortage[data-bom_header='${pp_no}']`).text(0);
                $currentRow.find(`.already_alloted_qty[data-bom_header='${pp_no}']`).text(0);
                $currentRow.find(`.issued_qty[data-bom_header='${pp_no}']`).text(0);
                $currentRow.find(`.bom_alloted_material[data-bom_header='${pp_no}']`).val(0).prop('disabled', true);
            }
        }
        var totalShortageQtyUpdated = 0;
        $(this).find(".location-wise-shortage").each(function() {
            var value = parseFloat($(this).text()) || 0;
            totalShortageQtyUpdated += value;
        });
        $(this).find(".item-shortage-qty").text(totalShortageQtyUpdated.toFixed(2));
        var totalRequiredQtyUpdated = 0;
        $(this).find(".bom_required_qty").each(function() {
            var value = parseFloat($(this).text()) || 0;
            totalRequiredQtyUpdated += value;
        });
        $(this).find(".item-required-qty").text(totalRequiredQtyUpdated.toFixed(2));
        let parent_id = $(this).find(".bom_required_qty").data('parent_id');
        if(isFirst){
            if ($(this).find(".bom_required_qty").text() == 0) {
                const rowId = $(this).data('id')
                let matchingRow = $('tr[data-id="' + rowId + '"]').not($(this));
                if (matchingRow.length > 0) {
                    if(parseFloat(matchingRow.find(".allocated-qty").text()) > 0) {
                        if (parent_id) {
                            let parentRow = $('tr[data-id="' + parent_id + '"]');
                            parentRow.find('td i.fa-minus').addClass('disabled-field');
                        } else {
                            let item_id = $(this).data('item_id');
                            $(this).find('td i.fa-plus').addClass('disabled-field');
                        }
                    }
                    else{
                        if (parent_id) {
                            let parentRow = $('tr[data-id="' + parent_id + '"]');
                            parentRow.find('td i.fa-minus').removeClass('disabled-field');
                        } else {
                            let item_id = $(this).data('item_id');
                            $(this).find('td i.fa-plus').removeClass('disabled-field');
                        }
                    }
                }
            }
        }
    });
}



function saveDeAllotedQty(pp_no, item_id, button) {
    $(button).prop('disabled',true)
    let isValid = true;
    let errorMsg = '';
    let is_expanded = 0;
    let allocatedMaterials = [];
    const $row = $(button).closest('tr');

    const $deallocatedInput = $row.find('.deallocated_material').filter(function () {
        return $(this).data('bom_header') === pp_no;
    });

    const inputValue = parseFloat($deallocatedInput.val()) || 0;
    const already_alloted_qty = parseFloat($row.find(".already_alloted_qty[data-bom_header='" + pp_no + "']").data('default-value')) || 0;
    const requiredQty = parseFloat($row.find(".bom_required_qty[data-bom_header='" + pp_no + "']").data('default-value')) || 0;
    const shortageQty = parseFloat($row.find(".location-wise-shortage[data-bom_header='" + pp_no + "']").data('default-value')) || 0;
    const locationQty = parseFloat($row.find(".item-location-qty[data-bom_header='" + pp_no + "']").text()) || 0;
    const pp_id = parseFloat($row.find(".item-location-qty[data-bom_header='" + pp_no + "']").data('pp_id')) || null;
    const location_id = parseFloat($row.find(".item-location-qty[data-bom_header='" + pp_no + "']").data('location_id')) || null;
    const issued_qty = parseFloat($row.find(".issued_qty[data-bom_header='" + pp_no + "']").data('default-value')) || 0;
    const parentIdData = $row.find(".bom_required_qty[data-bom_header='" + pp_no + "']").data('parent_id') || null;
    let allMatchingRowsValid = true;
        $('#report-table tbody tr').each(function () {
            const $thisRow = $(this);
            const currentParentId = $thisRow.find(".bom_required_qty[data-bom_header='" + pp_no + "']").data('parent_id');
            if (currentParentId === parentIdData) {
                if (!$thisRow.is($row)) {
                    const currentAllotedQty = parseFloat($thisRow.find(".already_alloted_qty[data-bom_header='" + pp_no + "']").data('default-value')) || 0;
                    if (currentAllotedQty !== 0) {
                        allMatchingRowsValid = false;
                        return false;
                    }
                }
            }
        });
        if(parentIdData){
            if (allMatchingRowsValid && inputValue === already_alloted_qty) {
                is_expanded = 1;
            } else {
                is_expanded = 0;
            }
        }else{
            is_expanded = 0;
        }
    if (inputValue == 0){
        isValid = false;
        errorMsg = `Deallocated quantity cannot be 0.\n`;
    }
    else if (inputValue > already_alloted_qty || (already_alloted_qty - issued_qty) < inputValue) {
        isValid = false;
        errorMsg = `Deallocated quantity cannot exceed already allocated quantity or issued quantity.\n`;
        $deallocatedInput.addClass('error-border');
    } else {
        $deallocatedInput.removeClass('error-border');
    }

    if (!isValid) {
        swal("Error", errorMsg, "error");
        $(button).prop('disabled',false)
        return;
    }

    const allocatedQty =  inputValue;
    allocatedMaterials.push({
        item_id: item_id,
        parent_id: parentIdData,
        pp_no: pp_no,
        allocated_qty: allocatedQty,
        required_qty: requiredQty,
        pp_id: pp_id,
        location_id: location_id,
        is_expanded : is_expanded
    });
    $.ajax({
        url: "/erp/production/json/allocated_material_quantity/",
        type: "POST",
        dataType: "json",
        data: {
            "allocated_materials": JSON.stringify(allocatedMaterials),
            "enterprise_id": $('#enterprise_id').val(),
            "status" : 0
        },
        success: function () {
            swal({ title: "", text: "Materials have been deallocated successfully.", type: "success" }, function () {
                localStorage.setItem("triggerGenerateShortageReportPP", "true");
                $('#loading').show();
                window.location.reload();
            });
        },
        error: function (xhr) {
            console.error(xhr.status + ": " + xhr.responseText);
            swal("Error", "Failed to deallocate materials. Please try again.", "error");
        }
    });
}

function QTipInit() {
    $('.item-available-qty').qtip({
        content: {
            text: "<p>All location-wise stock<br/> will be displayed</p>",
            title: ''
        },
        position: {
            my: 'bottom center',
            at: 'top center',
            adjust: {
                x: 0,
                y: 25
            }
        }
    });
}

function removeDuplicate(pp_no) {
    const processedItems = {};

    $('#report-table tbody tr').each(function () {
        const $currentRow = $(this);
        const item_id = $currentRow.data('id');
        $currentRow.find('.item-location-qty').each(function () {
            const key = `${item_id}_${pp_no}`;

            if (!processedItems[key]) {
                processedItems[key] = true;

                $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).text(function () {
                    return $(this).attr('data-default-value') || $(this).text();
                });

                $currentRow.find(`.deallocated_material[data-bom_header='${pp_no}']`).text(function () {
                    return $(this).attr('data-default-value') || $(this).text();
                });

                $currentRow.find(`.location-wise-shortage[data-bom_header='${pp_no}']`).text(function () {
                    const shortageValue = $currentRow.find(`.bom_shortage_material[data-bom_header='${pp_no}']`).text() || '0';
                    return shortageValue;
                });

                $currentRow.find(`.already_alloted_qty[data-bom_header='${pp_no}'] .allocated-qty`).text(function () {
                    return $(this).attr('data-default-value') || $(this).text();
                });

                $currentRow.find(`.issued_qty[data-bom_header='${pp_no}']`).text(function () {
                    return $(this).attr('data-default-value') || $(this).text();
                });
                if($currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).text() != $currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}']`).data('default-value')){
                    $currentRow.find(`.bom_alloted_material[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).val(function () {
                        const itemLocationQty = parseFloat($currentRow.find(`.item-location-qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).text() || 0);
                        const bomRequiredQty = parseFloat($currentRow.find(`.bom_required_qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).text() || 0);
                        const alreadyAllotedQty = parseFloat($currentRow.find(`.already_alloted_qty[data-bom_header='${pp_no}'][data-item_id='${item_id}']`).text() || 0);
                        let calculatedValue = itemLocationQty > (bomRequiredQty - alreadyAllotedQty)
                            ? (bomRequiredQty - alreadyAllotedQty).toFixed(2)
                            : itemLocationQty.toFixed(2);
                        if (parseFloat(calculatedValue) <= 0) {
                            calculatedValue = "0";
                            $(this).prop('disabled', true);
                        } else {
                            $(this).prop('disabled', false);
                        }

                        return calculatedValue;
                    });
                }
            }
        });
    });
    initializeLocationQty(pp_no);
}
