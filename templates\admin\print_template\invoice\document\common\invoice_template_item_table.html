<table border=1 bordercolor="#dcdcdc" class="table item_table elegant_table{% if item_res.include_row_separator %} row-seperator{% endif %}{% if item_res.include_column_separator %} column-seperator{% endif %} item_table_1" style="width: 100% !important;">
	<thead>
		<tr class="header_shading">
			{% if item_res.include_sno %}
				<th class="text-center td_sno td_sno_text" rowspan="2" style="width: {{item_res.sno_width}}%">{{ item_res.sno_label }}</th>
			{% endif %}
				<th class="text-center td_description td_description_text" rowspan="2" style="width: {{item_res.itemdetails_width}}%">{{ item_res.itemdetails_label }}</th>
			{% if source.type != "Issue" and item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
				<th class="text-center pdf_item_hsn_code_txt td_hsn_code" rowspan="2" style="width: {{item_res.hsnsac_width}}%">{{ item_res.hsnsac_label }}</th>
			{% endif %}
			{% if item_res.include_quantity %}
				<th class="text-center td_qty td_qty_text" rowspan="2" style="width: {{item_res.quantity_width}}%">{{ item_res.quantity_label }}</th>
			{% endif %}
			{% if item_res.include_units and not item_res.units_in_quantity_column %}
				<th class="text-center td_uom td_uom_text" rowspan="2" style="width: {{item_res.units_width}}%">{{ item_res.units_label }}</th>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_unit_price %}
				<th class="text-center td_price td_price_text" rowspan="2" style="width: {{item_res.unit_price_width}}%">{{ item_res.unit_price_label }}<br>{{ source.currency }}</th>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_discount %}
				<th class="text-center td_disc td_disc_text" rowspan="2" style="width: {{item_res.discount_width}}%">{{ item_res.discount_label }}<br>%</th>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_taxable_amount %}
				<th class="text-center td_tax td_tax_text" rowspan="2" style="width: {{item_res.taxable_amount_width}}%">{{ item_res.taxable_amount_label }}</th>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_tax %}
				{% if item_res.show_tax_for_dc %}
					{% if item_res.tax_type == 1 %}
						{% if item_res.include_taxrate and item_res.include_taxamount %}
							<th class="text-center td_cgst tax_rate_column" colspan="2" style="width: 12%; ">CGST</th>
							<th class="text-center td_sgst tax_rate_column" colspan="2" style="width: 12%; ">SGST</th>
							<th class="text-center td_igst tax_rate_column" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
						{% elif item_res.include_taxrate or item_res.include_taxamount %}
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">CGST</th>
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">SGST</th>
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">IGST</th>
						{% endif %}
					{% elif item_res.tax_type == 4 %}
						{% if item_res.include_taxamount %}
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">CGST</th>
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">SGST</th>
							<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">IGST</th>
						{% endif %}
					{% endif %}
				{% else %}
					{% if source.type != "DC" and source.type != "JDC" %}
						{% if item_res.tax_type == 1 %}
							{% if item_res.include_taxrate and item_res.include_taxamount %}
								<th class="text-center td_cgst tax_rate_column" colspan="2" style="width: 12%; ">CGST</th>
								<th class="text-center td_sgst tax_rate_column" colspan="2" style="width: 12%; ">SGST</th>
								<th class="text-center td_igst tax_rate_column" colspan="2" style="width: 12%;  border-right-color: #ccc;">IGST</th>
							{% elif item_res.include_taxrate or item_res.include_taxamount %}
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">CGST</th>
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">SGST</th>
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {% if item_res.include_taxrate %} {{item_res.taxrate_width}}% {% else %} {{item_res.taxamount_width}}% {% endif %}">IGST</th>
							{% endif %}
						{% elif item_res.tax_type == 4 %}
							{% if item_res.include_taxamount %}
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">CGST</th>
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">SGST</th>
								<th class="text-center td_tax_rate tax_one_column" rowspan="2" style="width: {{item_res.taxamount_width}}%">IGST</th>
							{% endif %}
						{% endif %}
					{% endif %}
				{% endif %}
			{% endif %}
		</tr>
		{% if source.type != "Issue" and item_res.include_tax %}
			{% if item_res.show_tax_for_dc %}
				{% if item_res.tax_type == 1 %}
					{% if item_res.include_taxrate and item_res.include_taxamount %}
						<tr class="tax_rate_column header_shading tr_second_row">
							<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
							<th class="text-center td_cgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
							<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
							<th class="text-center td_sgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
							<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
							<th class="text-center td_igst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
						</tr>
					{% endif %}
				{% endif %}
			{% else %}
				{% if source.type != "DC" and source.type != "JDC" %}
					{% if item_res.tax_type == 1 %}
						{% if item_res.include_taxrate and item_res.include_taxamount %}
							<tr class="tax_rate_column header_shading tr_second_row">
								<th class="text-center td_cgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
								<th class="text-center td_cgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
								<th class="text-center td_sgst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
								<th class="text-center td_sgst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
								<th class="text-center td_igst_rate td_tax_rate td_gst_rate" style="width: {{item_res.taxrate_width}}%">%</th>
								<th class="text-center td_igst_amt td_tax_amt td_gst_amt"style="width: {{item_res.taxamount_width}}%">{{ source.currency.code }}</th>
							</tr>
						{% endif %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}
	</thead>
	<tbody>
		{% for inv_material in invoice_materials %}
			<tr>
				{% if item_res.include_sno %}
					<td class="text-center td_sno" style="width: {{item_res.sno_width}}%">{{forloop.counter}}.</td>
				{% endif %}
				<td style="width: {{item_res.itemdetails_width}}%">
					<span class="pdf_item_name">
						<span class="pdf_item_name_txt"></span>
							{% if item_res.name_label != "" %} {{ item_res.name_label }} {% endif %}
				            {% if inv_material.material.item_name %} {{ inv_material.material.item_name }} {% elif inv_material.material.item.name %} {{ inv_material.material.item.name }} {% else %} Material_{{forloop.counter}} {% endif %}
						<br>
					</span>
					{% if item_res.include_itemcode and inv_material.material.item.drawing_no %}
						<span class="pdf_item_drawing_number"><i>{% if item_res.itemcode_label != "" %} {{ item_res.itemcode_label }} {% endif %}</i>  {{ inv_material.material.item.drawing_no }}<br></span>
					{% endif %}

<!--					{% if item_res.include_make and item_res.include_partno %}-->
<!--						{% if inv_material.material.make and inv_material.material.make_id and inv_material.material.make_id != 1 %}-->
<!--							<span class="pdf_item_make">[-->
<!--								<i class="pdf_item_make_txt">{% if item_res.make_label != "" %}{{ item_res.make_label }}{% endif %}</i>-->
<!--							                            {{ inv_material.material.make }}-->
<!--							</span>-->
<!--							{% for make in inv_material.material.item.makes %}-->
<!--								{% if make.make_id = inv_material.material.make_id %}-->
<!--									{% if make.part_no %}-->
<!--										- -->
<!--										<span class="pdf_item_part">-->
<!--											<i class="pdf_item_part_txt">{% if item_res.partno_label != "" %} {{ item_res.partno_label }} {% endif %}</i>-->
<!--										</span>-->
<!--										<span class="pdf_item_make_part_value">{{ make.part_no }} ]<br></span>-->
<!--									{% else %}-->
<!--										 ]<br>-->
<!--									{% endif %}-->
<!--								{% endif %}-->
<!--							{% endfor %}-->
<!--						{% endif %}-->
<!--					{% elif item_res.include_make or item_res.include_partno %}-->
<!--						{% if item_res.include_make %}-->
<!--							{% if inv_material.material.make and inv_material.material.make_id and inv_material.material.make_id != 1 %}-->
<!--								<span class="pdf_item_make">[-->
<!--									<i class="pdf_item_make_txt">{% if item_res.make_label != "" %} {{ item_res.make_label }} {% endif %}</i>-->
<!--								                           {{ inv_material.material.make }}]<br>-->
<!--								</span>-->
<!--							{% endif %}-->
<!--						{% else %}-->
<!--							{% for make in inv_material.material.item.makes %}-->
<!--								{% if make.make_id = inv_material.material.make_id %}-->
<!--									{% if make.part_no %}-->
<!--										<span class="pdf_item_part">[-->
<!--											<i class="pdf_item_part_txt">{% if item_res.partno_label != "" %} {{ item_res.partno_label }} {% endif %}</i>-->
<!--										</span>-->
<!--										<span class="pdf_item_make_part_value">{{ make.part_no }}]<br></span>-->
<!--									{% endif %}-->
<!--								{% endif %}-->
<!--							{% endfor %}-->
<!--						{% endif %}-->
<!--					{% endif %}-->
					{% if inv_material.material.is_faulty == 1 %}
						<span>[Faulty]<br></span>
					{% endif %}
					{% if item_res.include_oano and inv_material.consolidated_oa_no %}
						<span class="pdf_item_oa"><i class="pdf_item_oa_txt">{% if item_res.oano_label != "" %} {{ item_res.oano_label }} {% endif %}</i> {{ inv_material.consolidated_oa_no }}<br></span>
					{% endif %}
					{% if item_res.include_dc_no and inv_material.consolidated_dc_details %}
						{% if item_res.include_dc_date or item_res.include_dc_qty %}
							{{inv_material.consolidated_dc_details.values.length}}
							<i class="pdf_item_dc_no_txt">{% if item_res.dc_no_label != "" %} {{ item_res.dc_no_label }}{% if item_res.data_separator == "<br />" %}{% autoescape off %} {{ item_res.data_separator }} {% endautoescape %}{% endif %} {% endif %}</i>
							<span class="pdf_item_dc_no">
								{% for dc_details in inv_material.consolidated_dc_details.values %}
									{{dc_details.code}} {% if item_res.include_dc_date %} ({{dc_details.approved_on}}) {% endif %} {% if item_res.include_dc_qty %} - {% endif %} {% if item_res.include_dc_qty %} {{dc_details.qty}} {% endif %} {% if item_res.include_dc_date or item_res.include_dc_qty %} {% if not forloop.last %}{% autoescape off %} {{ item_res.data_separator }} {% endautoescape %}{% endif %}{% endif %}
								{% endfor %}
							</span>
						{% else %}
							<span class="pdf_item_dc_no">
								<i class="pdf_item_dc_no_txt">{% if item_res.dc_no_label != "" %} {{ item_res.dc_no_label }} {% if item_res.data_separator == "<br />" %}{% autoescape off %} {{ item_res.data_separator }} {% endautoescape %}{% endif %}{% endif %}</i>
								{% for dc_details in inv_material.consolidated_dc_details.values %}
									{{dc_details.code}} {% if not forloop.last %}{% autoescape off %} {{ item_res.data_separator }} {% endautoescape %}{% endif %}
								{% endfor %}
							</span>
						{% endif %}
					<br />
					{% endif %}
					{% if inv_material.consolidated_grn_details %}
						<i class="pdf_item_grn_no_txt">GRN No:</i>
						<div style="margin-left: 25px;">
							{% for grn_details in inv_material.consolidated_grn_details.values %}
								{{grn_details.code}}  {{grn_details.qty}}  <br>
							{% endfor %}
						</div>
					{% endif %}
					{% if item_res.include_hsnsac and item_res.hsnsac_part_of_itemdetails and inv_material.material.hsn_code %}
						<span class="pdf_item_hsn_code"><i class="pdf_item_hsn_code_txt">{% if item_res.hsnsac_label != "" %} {{ item_res.hsnsac_label }} {% endif %}</i> {{ inv_material.material.hsn_code }}<br></span>
					{% endif %}
					{% if source.hasAssessRateTax %}
						<span class="pdf_item_hsn_code"><i>Disc. Rate:</i>{{ source.currency }} {{ inv_material.consolidated_taxable_value|floatformat:2 }} / <i>Ass. Rate:</i>{{ source.currency }} {{ inv_material.consolidated_taxable_value|floatformat:2 }}<br></span>
					{% endif %}
					{% if item_res.include_description and inv_material.material.item.description %}
						<span class="pdf_item_desc"><span class="pdf_item_desc_txt">{% if item_res.description_label != "" %} {{ item_res.description_label }} {% endif %}</span>{{ inv_material.material.item.description }}<br></span>
					{% endif %}
					{% if item_res.include_remarks and inv_material.consolidated_remarks %}
						<span class="pdf_item_remarks">(<i class="pdf_item_remarks_txt">{% if item_res.remarks_label != "" %} {{ item_res.remarks_label }} {% endif %}</i><i>{{ inv_material.consolidated_remarks|linebreaksbr }}</i>)<br></span>
					{% endif %}
					{% if item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 3 %}
								{% if inv_material.cgst_rate != 0.00 or inv_material.sgst_rate != 0.00 or inv_material.igst_rate != 0.00 %}
									<span class="tax_in_description">
										[{% if inv_material.cgst_rate != 0.00 %}
		                                    CGST @ {{ inv_material.cgst_rate|floatformat:1 }}: {{ inv_material.consolidated_cgst_value|floatformat:2 }}
		                                        {% if inv_material.sgst_rate != 0.00 or inv_material.igst_rate != 0.00 %} - {% endif %}
			                             {% endif %}
										 {% if inv_material.sgst_rate != 0.00 %}
		                                    SGST @ {{ inv_material.sgst_rate|floatformat:1 }}: {{ inv_material.consolidated_sgst_value|floatformat:2 }}
												{% if inv_material.igst_rate != 0.00 %} - {% endif %}
										 {% endif %}
										 {% if inv_material.igst_rate != 0.00 %}
		                                    IGST @ {{ inv_material.igst_rate|floatformat:1 }}: {{ inv_material.consolidated_igst_value|floatformat:2 }}
										 {% endif %}]
									</span>
								{% endif %}
							{% endif %}
						{% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
								{% if item_res.tax_type == 3 %}
									{% if inv_material.cgst_rate != 0.00 or inv_material.sgst_rate != 0.00 or inv_material.igst_rate != 0.00 %}
										<span class="tax_in_description">
											[{% if inv_material.cgst_rate != 0.00 %}
			                                    CGST @ {{ inv_material.cgst_rate|floatformat:1 }}: {{ inv_material.consolidated_cgst_value|floatformat:2 }}
			                                        {% if inv_material.sgst_rate != 0.00 or inv_material.igst_rate != 0.00 %} - {% endif %}
				                             {% endif %}
											 {% if inv_material.sgst_rate != 0.00 %}
			                                    SGST @ {{ inv_material.sgst_rate|floatformat:1 }}: {{ inv_material.consolidated_sgst_value|floatformat:2 }}
													{% if inv_material.igst_rate != 0.00 %} - {% endif %}
											 {% endif %}
											 {% if inv_material.igst_rate != 0.00 %}
			                                    IGST @ {{ inv_material.igst_rate|floatformat:1 }}: {{ inv_material.consolidated_igst_value|floatformat:2 }}
											 {% endif %}]
										</span>
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
				</td>
				{% if source.type != "Issue" and item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
					<td class="text-left td_hsn_code" style="width: {{item_res.hsnsac_width}}%">{{ inv_material.material.hsn_code }}</td>
				{% endif %}
				{% if item_res.include_quantity %}
					<td class="text-right td_qty" style="width: {{item_res.quantity_width}}%">{{ inv_material.consolidated_quantity|floatformat:2 }} {{inv_material.alternate_unit_id}}
						{% if item_res.include_units and item_res.units_in_quantity_column %}
							{% if inv_material.material.alternate_unit_id %}
								<span class="pdf_unit_in_price"> ({{ inv_material.material.alternate_unit.unit.unit_name }})<br />
									{% if item_res.include_primary_qty %}
										<span class="pdf_primary_unit_value" style="font-size: {{item_res.font_size|add:'-2'}}pt">({{ inv_material.primary_unit_value|floatformat:2 }}</span>
										{% if inv_material.material.item.unit.unit_name %}
											<span class="pdf_primary_unit" style="font-size: {{item_res.font_size|add:'-2'}}pt"> {{ inv_material.material.item.unit.unit_name }})</span>
										{% else %}
											{% if inv_material.material.unit.unit_name %}
												<span class="pdf_primary_unit" style="font-size: {{item_res.font_size|add:'-2'}}pt"> {{ inv_material.material.unit.unit_name }})</span>
											{% endif %}
										{% endif %}
									{% endif %}
								</span>
							{% else %}
								{% if inv_material.material.item.unit.unit_name %}
									<span class="pdf_unit_in_price"> ({{ inv_material.material.item.unit.unit_name }})</span>
								{% else %}
									{% if inv_material.material.unit.unit_name %}
										<span class="pdf_unit_in_price"> ({{ inv_material.material.unit.unit_name }})</span>
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
						{% if item_res.include_units and not item_res.units_in_quantity_column %}
							{% if inv_material.material.alternate_unit_id %}
								{% if item_res.include_primary_qty %}
									<br /><span class="pdf_primary_unit_value" style="font-size: {{item_res.font_size|add:'-2'}}pt">({{ inv_material.primary_unit_value|floatformat:2 }})</span>
								{% endif %}
							{% endif %}
						{% endif %}
					</td>

				{% endif %}
				{% if item_res.include_units and not item_res.units_in_quantity_column %}
					{% if inv_material.material.alternate_unit_id %}
							<td class="pdf_unit_in_price">{{ inv_material.material.alternate_unit.unit.unit_name }}<br />
								{% if item_res.include_primary_qty %}
									<span class="pdf_primary_unit" style="font-size: {{item_res.font_size|add:'-2'}}pt">({{ inv_material.material.item.unit.unit_name }})</span>
								{% endif %}
							</td>
					{% else %}
						{% if inv_material.material.item.unit.unit_name %}
							<td class="text-left td_uom" style="width: {{item_res.units_width}}%"> {{inv_material.alternate_unit_id}} {{ inv_material.material.item.unit.unit_name }}</td>
						{% else %}
							<td class="text-left td_uom" style="width: {{item_res.units_width}}%">{{ inv_material.material.unit.unit_name }}</td>
						{% endif %}
					{% endif %}
				{% endif %}
				{% if source.type != "Issue" and item_res.include_unit_price %}
					{% if inv_material.material.alternate_unit_id %}
						<td class="text-right td_price" style="width: {{item_res.unit_price_width}}%">{{ inv_material.material.rate | multiply:inv_material.material.alternate_unit.scale_factor | floatformat:2 }}</td>
					{%else%}
						<td class="text-right td_price" style="width: {{item_res.unit_price_width}}%">{{ inv_material.material.rate|floatformat:2 }}</td>
					{% endif %}
				{% endif %}
				{% if source.type != "Issue" and item_res.include_discount %}
					<td class="text-right td_disc" style="width: {{item_res.discount_width}}%">{{ inv_material.material.discount|floatformat:2 }}</td>
				{% endif %}
				{% if source.type != "Issue" and item_res.include_taxable_amount %}
					<td class="text-right td_tax" style="width: {{item_res.taxable_amount_width}}%">{{ inv_material.consolidated_taxable_value|floatformat:2 }}</td>
				{% endif %}
				{% if source.type != "Issue" and item_res.include_tax %}
					{% if item_res.show_tax_for_dc %}
						{% if item_res.tax_type == 1 %}
							{% if item_res.include_taxrate %}
								<td class="text-right td_cgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
									{% if item_res.include_taxamount %} {{ inv_material.cgst_rate|floatformat:2 }} {% else %} {{ inv_material.cgst_rate|floatformat:2 }}% {% endif %}</td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-right td_cgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_cgst_value|floatformat:2 }}</td>
							{% endif %}
							{% if item_res.include_taxrate %}
								<td class="text-right td_sgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
									{% if item_res.include_taxamount %} {{ inv_material.sgst_rate|floatformat:2 }} {% else %} {{ inv_material.sgst_rate|floatformat:2 }}% {% endif %}</td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-right td_sgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_sgst_value|floatformat:2 }}</td>
							{% endif %}
							{% if item_res.include_taxrate %}
								<td class="text-right td_igst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
									{% if item_res.include_taxamount %} {{ inv_material.igst_rate|floatformat:2 }} {% else %} {{ inv_material.igst_rate|floatformat:2 }}% {% endif %}</td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-right td_igst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_igst_value|floatformat:2 }}</td>
							{% endif %}
						{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
							<td class="text-right tax_one_column tax_one_column_csgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_cgst_value|floatformat:2 }}<br><small>@ {{ inv_material.cgst_rate|floatformat:2 }}%</small></td>
							<td class="text-right tax_one_column tax_one_column_ssgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_sgst_value|floatformat:2 }}<br><small>@ {{ inv_material.sgst_rate|floatformat:2 }}%</small></td>
							<td class="text-right tax_one_column tax_one_column_isgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_igst_value|floatformat:2 }}<br><small>@ {{ inv_material.igst_rate|floatformat:2 }}%</small></td>
						{% endif %}
					{% else %}
						{% if source.type != "DC" and source.type != "JDC" %}
							{% if item_res.tax_type == 1 %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_cgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_material.cgst_rate|floatformat:2 }} {% else %} {{ inv_material.cgst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_cgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_cgst_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_sgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_material.sgst_rate|floatformat:2 }} {% else %} {{ inv_material.sgst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_sgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_sgst_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_igst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_material.igst_rate|floatformat:2 }} {% else %} {{ inv_material.igst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_igst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_igst_value|floatformat:2 }}</td>
								{% endif %}
							{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
								<td class="text-right tax_one_column tax_one_column_csgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_cgst_value|floatformat:2 }}<br><small>@ {{ inv_material.cgst_rate|floatformat:2 }}%</small></td>
								<td class="text-right tax_one_column tax_one_column_ssgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_sgst_value|floatformat:2 }}<br><small>@ {{ inv_material.sgst_rate|floatformat:2 }}%</small></td>
								<td class="text-right tax_one_column tax_one_column_isgt" style="width: {{item_res.taxamount_width}}%">{{ inv_material.consolidated_igst_value|floatformat:2 }}<br><small>@ {{ inv_material.igst_rate|floatformat:2 }}%</small></td>
							{% endif %}
						{% endif %}
					{% endif %}
				{% endif %}
			</tr>
		{% endfor %}
		<tr class="item_empty_row {% if summary_res.include_hsn_summary or irn_details %} hide {% endif %}" >
			{% if item_res.include_sno %}
				<td class="text-center td_sno"></td>
			{% endif %}
			<td class="text-center pdf_item_name"></td>
			{% if source.type != "Issue" and item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
				<td class="text-center td_hsn_code"></td>

			{% endif %}
			{% if item_res.include_quantity %}
				<td class="text-center td_qty"></td>
			{% endif %}
			{% if item_res.include_units and not item_res.units_in_quantity_column %}
				<td class="text-center td_uom"></td>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_unit_price %}
				<td class="text-center td_price"></td>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_discount %}
				<td class="text-center td_disc"></td>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_taxable_amount %}
				<td class="text-center td_tax"></td>
			{% endif %}
			{% if source.type != "Issue" and item_res.include_tax %}
				{% if item_res.show_tax_for_dc %}
					{% if item_res.tax_type == 1 %}
						{% if item_res.include_taxrate %}
							<td class="text-center td_cgst tax_rate_column td_gst_rate"></td>
						{% endif %}
						{% if item_res.include_taxamount %}
							<td class="text-center td_cgst tax_rate_column td_gst_amt"></td>
						{% endif %}
						{% if item_res.include_taxrate %}
							<td class="text-center td_sgst tax_rate_column td_gst_rate"></td>
						{% endif %}
						{% if item_res.include_taxamount %}
							<td class="text-center td_sgst tax_rate_column td_gst_amt"></td>
						{% endif %}
						{% if item_res.include_taxrate %}
							<td class="text-center td_igst tax_rate_column td_gst_rate"></td>
						{% endif %}
						{% if item_res.include_taxamount %}
							<td class="text-center td_igst tax_rate_column td_gst_amt"></td>
						{% endif %}
					{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
						<td class="text-center tax_one_column tax_one_column_csgt "></td>
						<td class="text-center tax_one_column tax_one_column_ssgt "></td>
						<td class="text-center tax_one_column tax_one_column_isgt "></td>
					{% endif %}
			    {% else %}
					{% if source.type != "DC" and source.type != "JDC" %}
			            {% if item_res.tax_type == 1 %}
							{% if item_res.include_taxrate %}
								<td class="text-center td_cgst tax_rate_column td_gst_rate"></td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-center td_cgst tax_rate_column td_gst_amt"></td>
							{% endif %}
							{% if item_res.include_taxrate %}
								<td class="text-center td_sgst tax_rate_column td_gst_rate"></td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-center td_sgst tax_rate_column td_gst_amt"></td>
							{% endif %}
							{% if item_res.include_taxrate %}
								<td class="text-center td_igst tax_rate_column td_gst_rate"></td>
							{% endif %}
							{% if item_res.include_taxamount %}
								<td class="text-center td_igst tax_rate_column td_gst_amt"></td>
							{% endif %}
						{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
							<td class="text-center tax_one_column tax_one_column_csgt "></td>
							<td class="text-center tax_one_column tax_one_column_ssgt "></td>
							<td class="text-center tax_one_column tax_one_column_isgt "></td>
						{% endif %}
					{% endif %}
			    {% endif %}
			{% endif %}
		</tr>
		{% if source.type != "Issue" %}
			{% for inv_charge in source.charges %}
				<tr class="for_summary_enabled">
					{% if item_res.include_sno %}
						<td class="text-center td_sno" style="width: {{item_res.sno_width}}%"></td>
					{% endif %}
					<td class="text-right" style="width: {{item_res.itemdetails_width}}%">
						<span class="pdf_item_name">{{ inv_charge.item_name }}<br></span>
						{% if item_res.include_hsnsac and item_res.hsnsac_part_of_itemdetails and inv_charge.hsn_code %}
							<span class="pdf_item_hsn_code"><i>{% if item_res.hsnsac_label != "" %} {{ item_res.hsnsac_label }} {% endif %}</i> {{ inv_charge.hsn_code }}<br></span>
						{% endif %}
						{% if source.hasAssessRateTax %}
							<span class="pdf_item_hsn_code"><i>Disc. Rate:</i>{{ source.currency }} {{ inv_charge.getTotalCharge|floatformat:2 }} / <i>Ass. Rate:</i>{{ source.currency }} {{ inv_charge.getTotalCharge|floatformat:2 }}<br></span>
						{% endif %}
						{% if item_res.include_tax %}
							{% if item_res.show_tax_for_dc %}
								{% if item_res.tax_type == 3 %}
									{% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
										<span class="tax_in_description">[
										                                 {% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 %}
									                                        CGST @ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}
																				{% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
											                             {% endif %}
																		 {% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 %}
									                                        SGST @ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}
																				{% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
																		 {% endif %}
																		 {% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
										                                    IGST @ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}
																		 {% endif %}
																		 ]</span>
									{% endif %}
								{% endif %}
							{% else %}
								{% if source.type != "DC" and source.type != "JDC" %}
									{% if item_res.tax_type == 3 %}
										{% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
											<span class="tax_in_description">[
											                                 {% if inv_charge.getInvoiceChargeTaxValue.cgst_rate != 0.00 %}
										                                        CGST @ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}
																					{% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 or inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
												                             {% endif %}
																			 {% if inv_charge.getInvoiceChargeTaxValue.sgst_rate != 0.00 %}
										                                        SGST @ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}
																					{% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %} - {% endif %}
																			 {% endif %}
																			 {% if inv_charge.getInvoiceChargeTaxValue.igst_rate != 0.00 %}
											                                    IGST @ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:1 }}: {{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}
																			 {% endif %}
																			 ]</span>
										{% endif %}
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
					</td>
					{% if item_res.include_hsnsac and not item_res.hsnsac_part_of_itemdetails %}
						<td class="text-left td_hsn_code" style="width: {{item_res.hsnsac_width}}%">{{ inv_charge.hsn_code }}</td>
					{% endif %}
					{% if item_res.include_quantity %}
						<td class="text-right td_qty" style="width: {{item_res.quantity_width}}%; color: transparent;">
							0.00
						</td>

					{% endif %}
					{% if item_res.include_units and not item_res.units_in_quantity_column %}
						{% if inv_material.material.item.unit.unit_name %}
							<td class="text-center td_uom" style="width: {{item_res.units_width}}%"></td>
						{% else %}
							<td class="text-center td_uom" style="width: {{item_res.units_width}}%"></td>
						{% endif %}
					{% endif %}
					{% if item_res.include_unit_price %}
						<td class="text-right td_price" style="width: {{item_res.unit_price_width}}%">{{ inv_charge.rate|floatformat:2 }}</td>
					{% endif %}
					{% if item_res.include_discount %}
						<td class="text-right td_disc" style="width: {{item_res.discount_width}}%">{{ inv_charge.discount|floatformat:2 }}</td>
					{% endif %}
					{% if item_res.include_taxable_amount %}
						<td class="text-right td_tax" style="width: {{item_res.taxable_amount_width}}%">{{ inv_charge.getTotalCharge|floatformat:2 }}</td>
					{% endif %}
					{% if item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 1 %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_cgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_cgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_sgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_sgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="text-right td_igst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
										{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}% {% endif %}</td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="text-right td_igst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}</td>
								{% endif %}
							{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
								<td class="text-right tax_one_column tax_one_column_csgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}%</small></td>
								<td class="text-right tax_one_column tax_one_column_ssgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}%</small></td>
								<td class="text-right tax_one_column tax_one_column_isgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}%</small></td>
							{% endif %}
						{% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
								{% if item_res.tax_type == 1 %}
									{% if item_res.include_taxrate %}
										<td class="text-right td_cgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
											{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}% {% endif %}</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-right td_cgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="text-right td_sgst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
											{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}% {% endif %}</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-right td_sgst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="text-right td_igst tax_rate_column td_gst_rate" style="width: {{item_res.taxrate_width}}%">
											{% if item_res.include_taxamount %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }} {% else %} {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}% {% endif %}</td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="text-right td_igst tax_rate_column td_gst_amt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}</td>
									{% endif %}
								{% elif item_res.tax_type == 4 and item_res.include_taxamount %}
									<td class="text-right tax_one_column tax_one_column_csgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.cgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.cgst_rate|floatformat:2 }}%</small></td>
									<td class="text-right tax_one_column tax_one_column_ssgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.sgst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.sgst_rate|floatformat:2 }}%</small></td>
									<td class="text-right tax_one_column tax_one_column_isgt" style="width: {{item_res.taxamount_width}}%">{{ inv_charge.getInvoiceChargeTaxValue.igst_value|floatformat:2 }}<br><small>@ {{ inv_charge.getInvoiceChargeTaxValue.igst_rate|floatformat:2 }}%</small></td>
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
				</tr>
			{% endfor %}
		{% endif %}
	</tbody>
	<tfoot>
		{% if summary_res.include_total %}
			{% if source.type != "Issue" and summary_res.include_subtotal %}
				<tr class="total_section sub_total_section">
					<td colspan="{{item_column_span.span_total_column}}" class="text-right total_section_1"><b>Total</b></td>
					{% if item_res.include_quantity and summary_res.include_qty_total %}
						<td class="text-right total_section_2">{{ source.getConsolidatedQuantity|floatformat:2 }}</td>
					{% endif %}
					{% if source.type != "Issue" %}
						<td colspan="{{item_column_span.span_total_summary}}" class="text-right total_section_3">{{ source.getTotalTaxableValue|floatformat:2 }}</td>
					{% endif %}
					{% if source.type != "Issue" and item_res.include_tax %}
						{% if item_res.show_tax_for_dc %}
							{% if item_res.tax_type == 1 %}
								{% if item_res.include_taxrate %}
									<td class="tax_rate_column td_gst_rate"></td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="tax_rate_column text-right td_gst_amt">{{ cgst_total_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="tax_rate_column td_gst_rate"></td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="tax_rate_column text-right td_gst_amt">{{ sgst_total_value|floatformat:2 }}</td>
								{% endif %}
								{% if item_res.include_taxrate %}
									<td class="tax_rate_column td_gst_rate"></td>
								{% endif %}
								{% if item_res.include_taxamount %}
									<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}</td>
								{% endif %}
							{% elif item_res.tax_type == 4 %}
								{% if item_res.include_taxamount %}
									<td class="text-right tax_one_column tax_one_column_csgt_total">{{ cgst_total_value|floatformat:2 }}<br></td>
									<td class="text-right tax_one_column tax_one_column_ssgt_total">{{ sgst_total_value|floatformat:2 }}<br></td>
									<td class="text-right tax_one_column tax_one_column_isgt_total" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}<br></td>
								{% endif %}
							{% endif %}
						{% else %}
							{% if source.type != "DC" and source.type != "JDC" %}
								{% if item_res.tax_type == 1 %}
									{% if item_res.include_taxrate %}
										<td class="tax_rate_column td_gst_rate"></td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="tax_rate_column text-right td_gst_amt">{{ cgst_total_value|floatformat:2 }}</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="tax_rate_column td_gst_rate"></td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="tax_rate_column text-right td_gst_amt">{{ sgst_total_value|floatformat:2 }}</td>
									{% endif %}
									{% if item_res.include_taxrate %}
										<td class="tax_rate_column td_gst_rate"></td>
									{% endif %}
									{% if item_res.include_taxamount %}
										<td class="tax_rate_column text-right td_gst_amt" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}</td>
									{% endif %}
								{% elif item_res.tax_type == 4 %}
									{% if item_res.include_taxamount %}
										<td class="text-right tax_one_column tax_one_column_csgt_total">{{ cgst_total_value|floatformat:2 }}<br></td>
										<td class="text-right tax_one_column tax_one_column_ssgt_total">{{ sgst_total_value|floatformat:2 }}<br></td>
										<td class="text-right tax_one_column tax_one_column_isgt_total" style="border-right-color: #ccc;">{{ igst_total_value|floatformat:2 }}<br></td>
									{% endif %}
								{% endif %}
							{% endif %}
						{% endif %}
					{% endif %}
				</tr>
			{% elif item_res.include_quantity and summary_res.include_qty_total %}
				<tr class="total_section sub_total_section">
					<td colspan="{{item_column_span.span_total_column}}" class="text-right total_section_1"><b>Total</b></td>
					<td class="text-right total_section_2">{{ source.getConsolidatedQuantity|floatformat:2 }}</td>
					{% if item_res.include_units and not item_res.units_in_quantity_column %}
						<td></td>
					{% endif %}
				</tr>
			{% endif %}
		{% endif %}
		{% if source.type != "Issue" %}
			{% for inv_tax in sorted_taxes %}
				{% for key, value in tax_values.items %}
					{% if key == inv_tax.tax_code %}
						<tr class="other_tax_column total_section for_summary_enabled" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">{% if inv_tax.tax.assess_rate > 0 %} <i>Accessable Value - {{ source.getTotalTaxableValue|floatformat:2 }}</i><br>{% endif %}{{ inv_tax.tax.name }} @ {{ inv_tax.tax.net_rate }} %</td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value }}</td>
						</tr>
					{% endif %}
				{% endfor %}
			{% endfor %}
		{% endif %}
		{% if source.type != "Issue" and item_res.include_tax %}
			{% if item_res.show_tax_for_dc %}
				{% if item_res.tax_type == 2 %}
					{% for key, value in tax_summary.cgst_summary.items %}
						<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">CGST @ {{ key|floatformat:2 }} %</td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
						</tr>
					{% endfor %}
					{% for key, value in tax_summary.sgst_summary.items %}
						<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">SGST @ {{ key|floatformat:2 }} %</td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
						</tr>
					{% endfor %}
					{% for key, value in tax_summary.igst_summary.items %}
						<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
							<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">IGST @ {{ key|floatformat:2 }} %</td>
							<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
						</tr>
					{% endfor %}
				{% endif %}
			{% else %}
				{% if source.type != "DC" and source.type != "JDC" %}
					{% if item_res.tax_type == 2 %}
						{% for key, value in tax_summary.cgst_summary.items %}
							<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
								<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">CGST @ {{ key|floatformat:2 }} %</td>
								<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
							</tr>
						{% endfor %}
						{% for key, value in tax_summary.sgst_summary.items %}
							<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
								<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">SGST @ {{ key|floatformat:2 }} %</td>
								<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
							</tr>
						{% endfor %}
						{% for key, value in tax_summary.igst_summary.items %}
							<tr class="consolidated_tax total_section for_summary_enabled" style="width: 100%">
								<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">IGST @ {{ key|floatformat:2 }} %</td>
								<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ value|floatformat:2 }}</td>
							</tr>
						{% endfor %}
					{% endif %}
				{% endif %}
			{% endif %}
		{% endif %}
		{% if source.round_off != 0 %}
			<tr class="total_section for_summary_enabled" style="width: 100%">
				<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1">Round-off<span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
				<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ source.round_off }}</td>
			</tr>
		{% endif %}
		{% if source.type != "Issue" and summary_res.include_total %}
				<tr class="total_section for_summary_enabled" style="width: 100%">
					<td colspan="{{item_column_span.total_text_column_count}}" class="text-right total_section_1"><b>Grand Total</b><span class="total_inclusice_tax hide"> (inclusive tax)</span></td>
					<td colspan="{{item_column_span.total_value_column_count}}" class="text-right total_section_3">{{ source.currency.code }} <b>{{ source.grand_total }}</b></td>
				</tr>
		{% endif %}
		{% if source.type != "Issue" and summary_res.include_total_in_words %}
			<tr class="tr_total_in_words show_total_in_words total_in_words for_summary_enabled" style="width: 100%">
				<td colspan="{{item_column_span.span_all_column}}" class="full-length-td"><b>Total Value ({{ source.currency.code }}): {{ total_in_words|upper }} </b></td>
			</tr>
		{% endif %}
		{% if source.type != "Issue" and template_id != 2 and header_res.include_splinstructions and source.special_instruction %}
		    <tr>
				<td colspan="{{item_column_span.span_all_column}}" class="full-length-td tr_special_instruction for_single_page">
					<b class="tr_special_instruction_txt">{{ header_res.splinstructions_label }}</b>
					{{ source.special_instruction }}
				</td>
		    </tr>
			{% endif %}
		{% if source.type != "Issue" and source.is_courier == 1 %}
		<tr class="tr_total_in_words show_total_in_words total_in_words for_summary_enabled" style="width: 100%">
			<td colspan="{{item_column_span.span_all_column}}" class="full-length-td">
				<b class="tr_special_instruction_txt">No of consignments/Packages: {{ source.no_of_consignment }}</b>
				<b class="tr_special_instruction_txt">Weight in Kg: {{ source.weight }}</b>
			</td>
		</tr>
		{% endif %}
	</tfoot>
	</table>