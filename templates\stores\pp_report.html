{% extends "stores/sidebar.html" %}
{% block ppreport %}
{% if access_level.view %}
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/custom.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/chosen.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/bootstrap-multiselect.css?v={{ current_version }}">
<script type="text/javascript" src="/site_media/js/bootstrap-datepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/bootstrap-multiselect.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/chosen.jquery.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/jquery.easyResponsiveTabs.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/daterangepicker.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/pp_report.js?v={{ current_version }}"></script>
<script type="text/javascript" src="/site_media/js/load_material.js?v={{ current_version }}"></script>

<style>
        li.stock_report_menu a {
            outline: none;
            background-color: #e6983c !important;
        }
        .dataTables_scrollBody {
            overflow: auto hidden !important;
        }

        .dataTables_scrollBody table{
            margin-bottom: 20px;
        }
    </style>
<div class="right-content-container download-icon-onTop">
	<div class="page-title-container">
		<span class="page-title">{{template_title}}</span>
	</div>
	<div class="container-fluid">
		<div class="row">
			<div class="col-lg-12">
				<div class="content_bg">
					<ul class="resp-tabs-list hor_1"></ul>
					<div class="resp-tabs-container hor_1">
						<div class="row">
							<div class="add_table">
								<div class="col-lg-12 add_table_content">
									<div class="filter-components">
										<div class="filter-components-container">
											<div class="dropdown">
												<button class="btn dropdown-toggle btn-filter" type="button" data-toggle="dropdown" data-tooltip="tooltip" title="Modify&nbsp;filter">
													<i class="fa fa-filter"></i>
												</button>
												<span class="dropdown-menu arrow_box arrow_box_filter">
                                                        <div class="col-sm-12 form-group" >
                                                            <label>Date Range</label>
                                                            <div id="reportrange" class="report-range form-control">
                                                                <i class="glyphicon glyphicon-calendar"></i>&nbsp;
                                                                <span></span> <b class="caret"></b>
                                                                <input type="hidden" class="fromdate" id="fromdate" name="fromdate" value="{{since}}"/>
                                                                <input type="hidden" class="todate" id="todate" name="todate" value="{{till}}"/>
                                                            </div>
                                                        </div>
                                                        <div class="col-sm-12 form-group pendingdet">
                                                            <label>Material Name</label>
                                                            <select class="form-control chosen-select" name="select" id="id-material">
                                                            </select>
                                                        </div>
													<div style="margin-left:20px;">
														 <input checked="checked" id="id-is_detailed" name="billable" type="checkbox">
														<label style="margin-left:5px;margin-bottom:20px">Detailed View</label>
													  </div>
                                                       <div class="filter-footer">
                                                            <input type="button" class="btn btn-save" value="Apply" id="id-search_isr"/>
                                                        </div>
                                                    </span>
											</div>
											<span class='filtered-condition filtered-date'>Date: <b></b></span>
											<span class='filtered-condition filtered-material'>Material Name: <b></b></span>
										</div>
									</div>
									<div class="col-lg-12 search_result_table">
										<div class="csv_export_button">
											<a role="button" class="btn btn-add-new pull-right export_csv" onclick="GeneralExportTableToCSV.apply(this, [$('#pp_report'), 'PP_Report.csv']);" data-tooltip="tooltip" data-placement="bottom" title="Download&nbsp;Internal&nbsp;Stock&nbsp;Flow as CSV"><i class="fa fa-download" aria-hidden="true"></i></a>
										</div>
										<table class="table table-bordered custom-table table-striped grn_tbl" id="pp_report" style="width: 100%;">
											<thead class="th-vertical-center">
											<tr class="exclude_export">
												<th style="min-width: 15px;">S.NO</th>
												<th style="min-width: 45px;">DATE</th>
												<th style="min-width: 60px;">RECEIPT NO</th>
												<th style="min-width: 45px;">PROJECT</th>
												<th style="min-width: 150px;">RECEIVED FROM</th>
												<th style="min-width: 150px;">MATERIAL</th>
												<th style="min-width: 40px;">QUANTITY </th>
												<th style="min-width: 30px;">UNIT</th>

											</tr>
											<!-- This <tr> is used for csv download. Don't delete -->
											<tr>
												<th hidden="hidden">>S.NO</th>
												<th hidden="hidden">>DATE</th>
												<th hidden="hidden">>RECEIPT NO</th>
												<th hidden="hidden">>PROJECT</th>
												<th hidden="hidden">>RECEIVED FROM</th>
												<th hidden="hidden">>MATERIAL</th>
												<th hidden="hidden">QUANTITY </th>
												<th hidden="hidden">UNIT</th>
											</tr>
											</thead>
											<tbody id="isr_tbl_tbody">

											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

        $(window).load(function(){
            updateFilterText();
        });

        function updateFilterText() {
            $(".filtered-date b").text($("#reportrange").find("span").text());
            $(".filtered-party b").text($("#id-party_id option:selected").text());
            $(".filtered-material b").text($("#id-material option:selected").text());
        }
        var oTable;
        var oSettings;

        $(document).ready(function () {
            $(".nav-pills li").removeClass("active");
            $("#li_pp_report").addClass("active");
            TableHeaderFixed(tableId="pp_report","pp_report_filter");
            onPageLoad();
        });

    </script>
{% else %}
<div class="text-center" style="margin-top: 100px;">
	<h3>You don't have adequate permission to access this module.</h3>
	<h4>Please Contact your Administrator.</h4>
</div>
{% endif %}
{% endblock %}