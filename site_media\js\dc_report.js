var onPageLoad = function() {
    $("#dcreportview").click(function () {
        searchDCReport();
    });
}


function loadAllMaterials(itemId, makeId, isFaulty) {
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend)
    searchDCReport();
}

function searchDCReport() {
    $('#loading').show();
    $('#tablesorter').DataTable().clear();
    $('#tablesorter').DataTable().destroy();

    approved_only = $('#id_approved_only:checkbox:checked').length>0;
    var material = $('#id-material').val().split("[::]");
    var [itemId, makeId, isFaulty] = $('#id-material').val().split("[::]");
    var returnable = $('#id_returnable:checkbox:checked').length>0;
    var non_returnable= $('#id_non_returnable:checkbox:checked').length>0;
    var searchCriteria = {
        from_date: $('.fromdate').val(), to_date: $('.todate').val(),
        supplier: $('#cmbsupplier').val(),
        item_id: itemId, make_id: makeId, is_faulty: isFaulty,
        approved_only: approved_only,returnable : returnable, non_returnable: non_returnable
    };
    var isReturable = "";
    $("#DueDetailsTBody, .cs_tfoot").html("");
    $.ajax({
        url: '/erp/stores/json/dcreport/',
        type: "POST",
        dataType: "json",
        data: searchCriteria,
        success: function (response) {
            $("#loading").hide();
            row = ""; row1 = ""; var qty = 0;var total_value = 0.0;var unit = 0;var rate = 0;
            if(!returnable) {
                isReturable = "hide exclude_export";
                $("#tablesorter th:gt(10), #tablesorter tbody td:gt(10), #tablesorter tfoot td:gt(10)").addClass("hide exclude_export");
            }
            else {
                $("#tablesorter th:gt(10), #tablesorter tbody td:gt(10), #tablesorter tfoot td:gt(10)").removeClass("hide exclude_export");
            }
            if(response.value.length != 0) {
                $.each(response.value, function (index, value) {
                    if(value["date"] !="" && value["date"] !="undefined" && value["date"] != null) {
                        var setdateFormat = value["date"].split('-');
                        setdateFormat = setdateFormat[1]+"/"+setdateFormat[2]+"/"+setdateFormat[0];
                    }
                    else {
                        var setdateFormat="";
                    }
                    var dcDate = "";
                    if(setdateFormat != "") {
                        dcDate = moment(setdateFormat).format('MMM D, YYYY');
                    }
                    if(value["return_due_on"] !="" && value["return_due_on"] !=null) {
                        var setreturndateFormat = value["return_due_on"].split('-');
                        setreturndateFormat = setreturndateFormat[1]+"/"+setreturndateFormat[2]+"/"+setreturndateFormat[0];
                        setreturndateFormat = moment(setreturndateFormat).format('MMM D, YYYY')
                    }
                    else {
                        var setreturndateFormat="-NA-";
                    }
                    if(value["inward_date"] !="" && value["inward_date"] !=null) {
                        var setinwarddateFormat = value["inward_date"].split('-');
                        setinwarddateFormat = setinwarddateFormat[1]+"/"+setinwarddateFormat[2]+"/"+setinwarddateFormat[0];
                        setinwarddateFormat = "(" + moment(setinwarddateFormat).format('MMM D, YYYY') + ")"
                    }
                    else {
                        var setinwarddateFormat="";
                    }
                    var deliveryStatusColor;

                    
                    if(value["delivery_status"].toLowerCase() == "on time" &&  value["pending_qty"] > 0) {
                        deliveryStatusColor = "on-time"
                    }
                    else if(value["delivery_status"].toLowerCase() == "delayed" &&  value["pending_qty"] != value["quantity"]) {
                        deliveryStatusColor = "draft"
                    }
                    else if(value["delivery_status"].toLowerCase() == "delayed" &&  value["pending_qty"] == value["quantity"]) {
                        deliveryStatusColor = "rejected"
                    }
                    else if(value["delivery_status"].toLowerCase() == "on time" &&  value["pending_qty"] == 0) {
                        deliveryStatusColor = "approved"
                    }
                    var pending_qty_value = value["pending_qty"];
                    if(isNaN(pending_qty_value)) pending_qty_value = "";

                    row += `<tr>
                                <td class="text-center">${(index + 1)}</td>
                                <td class="text-center">${value["code"] }</td>
                                <td class="text-center">${dcDate}</td>
                                <td class="text-center">${value["dc_type"]}</td>
                                <td class="text-center">${value["ref"]}</td>
                                <td class="text-left">${value["customer"]}</td>
                                <td class="text-left">${value["instrument"]}</td>
                                <td class="text-right">${value["quantity"]}</td>
                                <td class="text-center">${value["unit"]}</td>
                                <td class="text-right">${value["rate"]}</td>
                                <td class="text-right">${(parseInt(value["quantity"]) * parseFloat(value["rate"])).toFixed(2)}</td>
                                <td class="text-left ${isReturable}">${setreturndateFormat}</td>
                                <td class="text-left ${isReturable}">${value.grn_code.replace(/,/g, ',<br />')}</td>
                                <td class="text-right ${isReturable}" data-order=${pending_qty_value}><span class=${deliveryStatusColor}>${value["pending_qty"]}</span></td>
                                <td class="text-center ${isReturable}"><span class=${deliveryStatusColor}>${value["delivery_status"]} <BR/> ${setinwarddateFormat}</span></td>
                                <td class="text-right ${isReturable}">${value["delivery_overdue"]}</td>
                            </tr>`;
                        qty += parseInt(value["quantity"]);
                        total_value += (parseInt(value["quantity"]) * parseFloat(value["rate"]));
                        rate += parseInt(value["rate"]);
                });
            
                var row1 = `<tr>
                                <td class="text-center"></td>
                                <td></td>
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                                <td class="text-center"></td>
                                <td class="text-right"><b>Grand Total</b></td>
                                <td class="text-right"><b>${qty}</b></td>
                                <td class="text-center"></td>
                                <td class="text-right"><b>${rate}</b></td>
                                <td class="text-right"><b>${total_value.toFixed(2)}</b></td>
                                <td class="text-left ${isReturable}"></td>
                                <td class="text-left ${isReturable}"></td>
                                <td class="text-center ${isReturable}"></td>
                                <td class="text-center ${isReturable}"></td>
                                <td class="text-center ${isReturable}"></td>
                            </tr>`;

                $('#DueDetailsTBody').html(row);
                $('.cs_tfoot').html(row1);
            }
            TableHeaderFixed();
            updateFilterText();
            closeFilterOption();
        },
        error: function (xhr, errmsg, err) {
            $("#loading").hide();
            console.log(xhr.status + ": " + xhr.responseText);
        }
    });
}