table.calculator_table {
    position: fixed;
    bottom: 0;
    right: 0px;
    border: solid 3px #fff;
    border-top: none;
    box-shadow: 0px 0px 10px #333;
    z-index: 10012;
}

.calculator_table td{
	border: solid 2px #fff;
	text-align: center;
}

.calc-num-pad {
	background: #F0F0F0;
    /*box-shadow: 2px 3px 6px #212121;*/
}

.calc-num-pad button {
	background: 0 0;
    color: #A2A2A2;
    width: 40px;
    height: 40px;
    border-radius: 100px;
    border: none;
    font-size: 22px;
    font-weight: 100;
    box-shadow: 0 0 0 transparent;
    outline: 0;
    font-family: verdana;
    cursor: pointer;
}

.calc-num-pad button:hover {
	background: #FFF;
	border-radius: 50px
}

.calc-display-box {
	background: #004195;
    padding: 0;
    min-height: 160px;
    overflow: hidden;
    cursor: move;
}

.calc-display-text {
    margin-top: 50px;
    background: 0 0;
    border: none;
    color: #FFF;
    font-size: 40px;
    font-weight: 100;
    padding-right: 20px;
    float: right;
    display: inline-block;
    overflow: hidden;
    width: 270px;
    height: 60px;
    outline: none;
}

.calc_close {
    font-size: 16px;
    position: absolute;
    right: 7px;
    margin-top: 7px;
    color: #FFF;
    cursor: pointer;
}

.calc_minimize {
    font-size: 16px;
    position: absolute;
    right: 33px;
    margin-top: 11px;
    color: #FFF;
    cursor: pointer;
}

.calc_invalid {
    position: absolute;
    float: left;
    text-align: left;
    left: 8px;
    top: 8px;
    color: #dd4b39;
    font-size: 13px;
    display: none;
}