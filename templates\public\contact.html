<html lang="en">
<head>
  <title>xserp</title>
  <meta charset="utf-8">
  <link rel="icon" href="/site_media/images/xs-logo-with-border.png" type="image/png">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="stylesheet" type="text/css" href="/site_media/css/bootstrap.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/erp_default.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/JSCustomValidator.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/sweetalert.css?v={{ current_version }}">
  <link rel="stylesheet" type="text/css" href="/site_media/css/roboto-font.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login.css?v={{ current_version }}" >
  <link rel="stylesheet" type="text/css" href="/site_media/css/login-menu.css?v={{ current_version }}" >
	<link rel="stylesheet" type="text/css" href="/site_media/css/font-awesome.min.css?v={{ current_version }}" >

  <script type="text/javascript" src="/site_media/js/jquery-3.1.1.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/bootstrap.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/jquery-ui.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/JSCustomValidator.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/sweetalert.min.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/field-validation.js?v={{ current_version }}"></script>
  <script type="text/javascript" src="/site_media/js/cookies.js?v={{ current_version }}"></script>
  <script async src="https://www.googletagmanager.com/gtag/js?id=AW-722929303"></script>
  <script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());
	  gtag('config', 'AW-722929303');
  </script>
  <style type="text/css">
    @media only screen and (max-height: 700px) {
    .contact-header {
        font-size: 32px;
        margin-top: 20px;
        margin-bottom: 20px;
      }

      .xserp-contact {
        margin-bottom: 30px !important;
      }
    }
  </style>
</head>

<body>
    {% include "public/theme-header.html" %}
    <!-- main menu -->
    
    <div class="main__theme__wrap contact-main-container">
    	<div class="container login-testimonial-container" style="max-width: 700px;">
		  	<div class="col-sm-12 contact-container">
			    <div class="col-sm-12 xserp-contact" style="margin-bottom: 45px;">
			    	<div class="xserp-heading">
	                	<div class="contact-header text-center">
	            			We will reach back to you shortly.
	          			</div>
	             	</div>
          			<form name="thisForm" method="post" action="" onsubmit="return validateform()">
          				{% csrf_token %}
                  		<div class="form-group wrap-input floating-label">
                     		<input type="text" class="form-control floating-input" name="user_name" id="user_name" placeholder=" " maxLength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');">
                     		<label>Name *</label>
                  		</div>
                  		<div class="form-group wrap-input floating-label">
                     		<input type="text" class="form-control floating-input" name="user_contact" id="user_contact" placeholder=" " maxlength="15" onblur="return validateStringOnBlur(this, event, 'ph_number');" onkeypress="return validateStringOnKeyPress(this,event, 'ph_number');">
                     		<label>Contact Number *</label>
                  		</div>
                  		<div class="form-group wrap-input floating-label">
                     		<input type="text" class="form-control floating-input" name="user_email" id="user_email" placeholder=" " maxlength="150" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');">
                     		<label>Email Address *</label>
                  		</div>
                  		<div class="form-group wrap-input floating-label">
                     		<select class="form-control floating-input" name="user_timing" id="user_timing" placeholder=" ">
                     			<option value="9 am - 6 pm">9 am - 6 pm</option>
                     			<option value="9 am - 9 pm">9 am - 9 pm</option>
                     		</select>
                     		<label>Best time to call *</label>
                  		</div>
                  		<div class="form-group wrap-input floating-label">
                     		<textarea type="text" class="form-control floating-input" name="user_subject" id="user_subject" placeholder=" " maxlength="300" onblur="return validateStringOnBlur(this, event, 'alphaSpecialChar');" onkeypress="return validateStringOnKeyPress(this,event, 'alphaSpecialChar');"></textarea>
                     		<label>Subject</label>
                  		</div>
                  		<div class="form-group" style="margin-top: 40px;">
                         	<button class="btn btn-lg btn-primary btn-block" id="send_mail_form" type="button">Send</button>
                      	</div>
                   	</form>
           	 	</div>
		  	 </div> 
		  </div>
    </div>
    <!-- footer -->
    {% include "public/theme-footer.html" %}
    <script type="text/javascript" src="/site_media/js/jquery.js.download"></script>
    <script type="text/javascript" src="/site_media/js/login-menu.js?v={{ current_version }}"></script>
    <script type="text/javascript">
	    $(document).ready(function(){

		});

		function validateform() {
      $("#send_mail_form").addClass("btn-theme-processing").text("Processing...");
		    $(".error-border").removeClass('error-border');
		    $(".custom-error-message").remove();
		    var ControlCollections = [{
		            controltype: 'textbox',
		            controlid: 'user_name',
		            isrequired: true,
		            errormsg: 'Name is required.'
		        },
		        {
		            controltype: 'textbox',
		            controlid: 'user_contact',
		            isrequired: true,
		            errormsg: 'Contact Number is required.'
		        },
		        {
		            controltype: 'textbox',
		            controlid: 'user_email',
		            isrequired: true,
		            errormsg: 'Email Address is required.',
		            isemail: true,
					emailerrormsg: 'Invalid Email Address.'
		        }
		    ];
		    var result = JSCustomValidator.JSvalidate(ControlCollections);
		    return result;
		}

		$("#send_mail_form").click(function()  {
			if (validateform()) {
				var name = $("#user_name").val();
				var contact_no = $("#user_contact").val();
				var email = $("#user_email").val();
				var best_time_to_call = $("#user_timing").val();
				var subject = "[Reach-out]" + " " + $("#user_subject").val();
				$.ajax({
					url: "/erp/public/send_contact_us_mail/",
					type: "post",
					data: {
						name: name, contact_no: contact_no, user_email: email, user_subject: $("#user_subject").val(), best_time_to_call: best_time_to_call, subject: subject
					},
					success: function(response) {
						if (response.response_message === "Success") {
              $("#send_mail_form").removeClass("btn-theme-processing").text("Send");
							swal({
								title: "Mail Status",
								text: response.custom_message,
								type: "success"
							},
							function() {
								location.reload();
						});
						} else {
							swal({title: "", text: response.custom_message, type: "error"});
              $("#send_mail_form").removeClass("btn-theme-processing").text("Send");
						}
					},
					error: function(xhr, errmsg, err) {
						console.log(xhr.status + ": " + xhr.responseText);
					}
				});
			}
      else {
        $("#send_mail_form").removeClass("btn-theme-processing").text("Send");
      }
		});

      $(document).ready(function(){
          var pageHeight = (window.innerHeight-65);
          var contactContainerHeight = $(".contact-container").height();
          console.log(pageHeight, contactContainerHeight)
          var margin = 100;
          if(pageHeight > contactContainerHeight) {
            margin = (pageHeight - contactContainerHeight) / 2;
          }
          if(pageHeight <= 600) {
            margin = 35;
          }
          if(margin <= 35) {
            margin = 35;
          }
          //$(".contact-main-container").css({minHeight: (pageHeight)+"px"});
          $(".contact-main-container").css({marginTop: (margin+65)+"px"});
          $(".contact-main-container").css({marginBottom: (margin)+"px"});
      });
	</script>

</body>

</html>