"""
"""
from django.conf.urls import patterns, url

from erp.admin import json_api, pgi_views
from erp.admin.views import manageUser, editUser, saveUser, deleteUser, manageEnterprise, saveEnterpriseProfile, \
	checkUser, bulkImport, tallyXMLImport, tallyXMLImportSave, getImportMessages, unprocessXMLFiles, \
	unprocessXMLFilesDelete, manageInvoiceTemplate, saveInvoiceTemplate, previewInvoiceTemplate, saveExpense, \
	savePurchase, savePreference, getEnterpriseProfileLogList, getEnterpriseProfileLogData, \
	sendEnterpriseRegisteredMail, \
	erase_enterprise_data, emailRequestFromUser, saveSETemplateConfig, validateInvoiceNumberFormat, \
	getInvoiceTemplateLogList, getInvoiceTemplateLogData, validateExtensionRequest, gstAuthentication, \
	managePurchaseTemplate, previewPurchaseTemplate, savePurchaseTemplate, getPurchaseTemplateLogList, \
	getPurchaseTemplateLogData, manageSalesEstimateTemplate, saveSalesEstimationTemplate, \
	previewSalesEstimationTemplate, manageOATemplate, saveOrderAcknowledgementTemplate, previewOrderAcknowledgementTemplate
from erp.masters.json_api import getNumberOfMaterialWithNegativeStock
from erp.views import send_contact_us_mail

__author__ = 'kalaivanan'

urlpatterns = patterns(
	'',
	url('user/$', manageUser),
	url('user/#table_tab2$', manageUser),
	url('user/edit/$', editUser),
	url('user/edit/#table_tab2$', editUser),
	url('user/save/$', saveUser),
	url('user/save/#table_tab2$', saveUser),
	url('user/delete/$', deleteUser),
	url('user/check/$', checkUser),

	# Json api urls
	url('json/allUsers/$', json_api.allUsers),
	url('json/userDetails/$', json_api.userDetails),
	url('json/deleteUser/$', json_api.deleteUser),
	url('json/updateUser/$', json_api.updateUser),
	url('json/update_project_permission/$', json_api.user_project_mapping),

	url('enterprise/$', manageEnterprise),
	url('enterprise/saveProfile/$', saveEnterpriseProfile),
	url('enterprise/savePreference/$', savePreference),
	url('enterprise/saveExpense/$', saveExpense),
	url('enterprise/savePurchase/$', savePurchase),
	url('enterprise/getloglist/$', getEnterpriseProfileLogList),
	url('enterprise/getlogdata/$', getEnterpriseProfileLogData),
	url('invoice_template/getloglist/$', getInvoiceTemplateLogList),
	url('invoice_template/getlogdata/$', getInvoiceTemplateLogData),
	url('json/bulkImport/$', bulkImport),
	url('json/tallyxmlImport/$', tallyXMLImport),
	url('json/tallyxmlMessage/$', getImportMessages),
	url('json/tallyxmlImportSave/$', tallyXMLImportSave),
	url('json/unprocessXMLFiles/$', unprocessXMLFiles),
	url('json/generate_checksum/$', pgi_views.pgiGenerateHash),
	url('json/get_merchants/$', pgi_views.pgiMerchantDetails),
	url('json/update_transaction/$', pgi_views.pgiUpdateTransaction),
	url('json/unprocessXMLFilesDelete/$', unprocessXMLFilesDelete),
	url('json/count_of_negative_stock_materials/$', getNumberOfMaterialWithNegativeStock),
	url('json/gst_authentication', gstAuthentication),
	url('invoice_template/$', manageInvoiceTemplate),
	url('sales_estimate_template/$', manageSalesEstimateTemplate),
	url('oa_template/$', manageOATemplate),
	url('invoice_template/save/$', saveInvoiceTemplate),
	url('invoice_template/preview/$', previewInvoiceTemplate),
	url('send_contact_us_mail/$', send_contact_us_mail),
	url('send_enterprise_registered_mail/$', sendEnterpriseRegisteredMail),
	url('erase_enterprise_data/$', erase_enterprise_data),
	url('mail_request/$', emailRequestFromUser),
	url('validate_extension_request/$', validateExtensionRequest),
	url('enterprise/saveSETemplateConfig/$', saveSETemplateConfig),
	url('json/validate_invoice_number_format/$', validateInvoiceNumberFormat),
	url('pay/success/$', pgi_views.pgiResponseHandler),
	url('pay/failure/$', pgi_views.pgiResponseHandler),
	url('redirectResult/$', pgi_views.pgiResultHandler),
	url('pay/initiateSubscriptionPayment/$', pgi_views.initiateSubscriptionPayment),
	url('po_template/$', managePurchaseTemplate),
	url('po_template/save/$', savePurchaseTemplate),
	url('po_template/preview/$', previewPurchaseTemplate),
	url('se_template/save/$', saveSalesEstimationTemplate),
	url('se_template/preview/$', previewSalesEstimationTemplate),
	url('oa_template/save/$', saveOrderAcknowledgementTemplate),
	url('oa_template/preview/$', previewOrderAcknowledgementTemplate),
	url('purchase_template/getloglist/$', getPurchaseTemplateLogList),
	url('purchase_template/getlogdata/$', getPurchaseTemplateLogData),
	url('json/pgi_invoice_creation/$', pgi_views.pgiInvoiceCreation),
	url('json/user_location_map/$', json_api.user_location_mapping),
)
