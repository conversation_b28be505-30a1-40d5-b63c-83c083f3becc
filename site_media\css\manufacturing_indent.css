#mi_list {
    table-layout: fixed;
    width: 100%;
}

table.dataTable thead tr.mi-add-row th {
    font-weight: normal;
    letter-spacing: 0;
    font-size: 12px;
}
table.dataTable thead tr.mi-add-row th .form-control,
table.dataTable thead tr.mi-add-row th .chosen-single,
table.dataTable thead tr.mi-add-row th .chosen-container {
    font-size: 12px;
    background: #fff;
}

table.dataTable thead tr.mi-add-row th .chosen-container {
    background: transparent;
}

#mi_list td,
#mi_list tr.mi-add-row th {
    border-right: none;
    border-left: none;
}

.qty_update_button {
    position: absolute;
    margin-top: -34px;
   margin-left: 7px;
}

.ui-autocomplete {
	Z-index: 10021 !important;
}

#mi_list .custom-error-message {
    position: relative;
    text-align: left;
    text-transform: initial;
}

#mi_list .mi-add-row th {
    text-align: left;
}

.edit_material-removal-icon {
	float: right;
    background: #555;
    padding: 7px 15px;
    margin-top: -33px;
    border-radius: 0 4px 4px 0;
    display: block;
    color: #fff;
    cursor: pointer;
}

#material_required[readonly] {
    background: #eee;
}

.supplementary_qty {
    margin-top: -34px;
    margin-right: 46px;
    background: #EEE;
    padding: 8px;
    height: 34px;
    max-width: 100px;
    min-width: 100px;
    border: solid 1px #CCC;
    line-height: 12px;
}

.qty-content .quantity {
    width: calc(100% - 45px);
    border-radius: 4px 0 0 4px;
}

.qty-content .quantity.oa-enabled {
    width: calc(100% - 145px);
    border-radius: 4px 0 0 4px;
}

@media only screen and (max-width: 1500px) {
    .supplementary_qty {
        max-width: 75px;
        min-width: 75px;
        font-size: 0.8vw;
        padding: 8px 4px;
    }

    .qty-content .quantity.oa-enabled {
        width: calc(100% - 120px);
        font-size: 0.8vw;
        padding: 4px;
    }
}


.mi_not_applicable {
	background: rgba(32, 155, 225, 0.1);
    border: solid 1px rgba(32, 155, 225, 0.4);
    padding: 4px 12px;
    border-radius: 50px;
    cursor: pointer;
    margin-right: 8px;
    display: inline-block;
    margin-bottom: 5px;
}

.mi_not_applicable:hover {
	background:  rgba(32, 155, 225, 0.2);
}

#edit_manufacturing_qty {
    text-align: right;
    width: calc(100% - 44px);
}
#manufacturing_qty {
    text-align: right;
    width: calc(100% - 44px);
}