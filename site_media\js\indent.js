$(function () {
    $('#cmdshow').click(function () {
        if ($('#materialrequired').val()=="") {
            swal("","Please Select Material","warning");
            return;
        }
        var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');

        if (newQtyField.value == "" || newQtyField.value =="0.00") {
            swal("","Please Enter Qty","warning");
            return;
        } else {

            $("#loadingmessage_changelog_listing_ie").show();
            showCatalogues();

        }
    });

    $('#add_new_ind_material').click(function () {
		refreshSessionPerNActions(10); // Dummy service call to keep the session alive
		var newQtyField = document.getElementById('id_ind_material-__prefix__-quantity');
        if ($('#material_id').val()!="" && newQtyField.value != "" && newQtyField.value >0) {
            var materialtable = document.getElementById("indentMaterial");
            var materialrowCount = materialtable.rows.length;

            for (j = 1; j <= materialrowCount-1; j++) {
                var match = false;
                if ($('#id_ind_material-' + parseInt(j - 1) + '-item_id').val() == $('#id_ind_material-__prefix__-item_id').val() && $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val() == $('#id_ind_material-__prefix__-make_label').val())  {
                    match = true;
                }

                if (match == true )  {
                    if ($("#id_ind_material-__prefix__-alternate_units:visible").length >= 1){
                        swal("", "Material already Exists!.", "error");
                        return;
                    }else{
                        if (document.getElementById('id_ind_material-' + parseInt(j - 1) + '-DELETE').checked){
                            var matched_row = document.getElementById('ind_material-' + parseInt(j - 1));
                            document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value = document.getElementById('id_ind_material-__prefix__-quantity').value;
                            document.getElementById('id_ind_material-' + parseInt(j - 1) + '-DELETE').checked = false;
                            if ($("#id_indent-indent_no").val()!=""){
                                document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantityLabel').innerHTML = document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value;
                            }
                            matched_row.style.display = '';
                        }
                        else {
                            var existingQty  = parseFloat(document.getElementById('id_ind_material-__prefix__-quantity').value);
                            swal({
                                title: "Material already Exists!",
                                text: "Do you still want to add its Quantity?",
                                type: "warning",
                                showCancelButton: true,
                                confirmButtonColor: "#209be1",
                                confirmButtonText: "Yes, do it!",
                                closeOnConfirm: true,
                                closeOnCancel: true
                            },
                            function(isConfirm){
                                if (isConfirm) {
                                    document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value = (parseFloat($('#id_ind_material-' + parseInt(j - 1) + '-quantity').val()) + existingQty).toFixed(3);
                                    if ($("#id_indent-indent_no").val()!=""){
                                        document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantityLabel').innerHTML = document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value;
                                    }
                                }
                            });
                        }
                        document.getElementById('id_ind_material-__prefix__-drawing_no').value = '';
                        document.getElementById('id_ind_material-__prefix__-is_service').value = '';
                        document.getElementById('id_ind_material-__prefix__-item_id').value = '';
                        document.getElementById('id_ind_material-__prefix__-description').value = '';
                        document.getElementById('id_ind_material-__prefix__-quantity').value = '';
                        document.getElementById('id_ind_material-__prefix__-units').value = 'NOs';
                        $('#materialrequired').val('');
                        $('#material_id_hidden').val('');
                        $('#materialr_id').val('');
                        return;
                    }
                }
            }

            if (!match) {
//              generateFormsetFormRow('ind_material');
                if($("#id_ind_material-__prefix__-is_service").val() == 1) {
                    generateFormsetFormRowAppend('ind_material', ".item-for-service");
                }
                else {
                    generateFormsetFormRowAppend('ind_material', ".item-for-goods");
                }
                var index = parseInt(parseInt($('#id_ind_material-TOTAL_FORMS').val()) - 1);
                var mat_label = document.getElementById('id_ind_material-' + index + '-materialLabel');
                var s_no = document.getElementById("id_ind_material-" + index + "-s_no");

                indent_copyFromEmptyForm('ind_material', index, 'indent-indent_no', 'indent_no');
                mat_label.innerHTML = $('#id_ind_material-' + index + '-description').val();
                s_no.innerHTML = index + 1;
            }
            $('#materialrequired').val('');
            $('#material_id_hidden').val('');
            $('#material_id').val('');
            $("#cmdshow").hide()
			setTimeout(function(){},250);
            $('#ind_unit_display').html('&nbsp;');
			$(".custom-error-message").remove();
            validateTextInputs();
            indentMaterialTableSerialNumberInit();
            $(".material-removal-icon").trigger("click");
        }
        else {
            if ($('#material_id_hidden').val() == "" && $('#materialrequired').val() != ""){
                var materialName = $("#materialrequired").val();
                var materialUnit = $('#id_ind_material-__prefix__-all_units').val()
                addNewMaterial(materialName, materialUnit);
            }
        }
    });

    $('#chknonstockable').change(function() {
        if (!$(this).is(':checked')) {
            $('#nonstockable').hide();
            $('#stockable').show();
            document.getElementById('id_indent-is_stockable').checked = false;
        }else{
            $('#nonstockable').show();
            $('#stockable').hide();
            document.getElementById('id_indent-is_stockable').checked = true;
        }
    });

    $('#catAdd').click(function () {
        addIndent();
    });

    $('#catCancel').click(function () {
        $("#catalogueModal").modal('hide');
    });

    $("#save_indent_button").click(function(){
        $("#id_indent-purpose").removeAttr("disabled");
        $(".error-border").removeClass('error-border');
        $(".custom-error-message").remove();

        var ControlCollections = [
            {
                controltype: 'textbox',
                controlid: 'id_indent-purpose',
                isrequired: true,
                errormsg: 'Purpose is required.'
            },
            {
                controltype: 'textbox',
                controlid: 'id_indent-expected_date',
                isrequired: true,
                errormsg: 'Date is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_indent-indent_type',
                isrequired: true,
                errormsg: 'Indent Type is required.'
            },
            {
                controltype: 'dropdown',
                controlid: 'id_indent-project_code',
                isrequired: true,
                errormsg: 'Project is required.'
            }
        ];
        var result = JSCustomValidator.JSvalidate(ControlCollections);
        if(result){
            var item_count = parseFloat($("#id_ind_material-TOTAL_FORMS").val());
            var item_list =0

            for(i=0;i<item_count;i++){
                if(!document.getElementById("id_ind_material-"+i+"-DELETE").checked){
                    item_list = item_list+1;
                }
            }

            if(item_list == 0) {
                swal('','Please add atleast one material in Indent','error');
            }
            else {
                if (checkIndentMaterialQty()== true && result == true){
                    $("#edit_indent_form").submit();
					ga('send', 'event', 'Indent', $("id_indent-indent_no") == "" ? 'Create': 'Update', $('#enterprise_label').val(), 1);
                    $("#loading").show();
                    $("#save_indent_button").val('Please wait...').addClass('btn-processing');
                }
            }
        }
        else {
            $("html, body").animate({ scrollTop: 0 }, "fast");
        }
    });

    $("#short_close").click(function(){
        short_close();
    });

    $("#load_received_qty").click(function(){
       if($("#load_received_qty").is(":checked")) {
            load_received_qty();
            $(".table_received_qty").removeClass('hide')
       }
       else{
             $(".table_received_qty").addClass('hide')
       }
    });

    function load_received_qty(){
        if ($("#id_indent-indent_no").val() != "") {
            var materialTable = document.getElementById("indentMaterial");
            var materialRowCount = materialTable.rows.length;
            $.ajax({
                url: "/erp/stores/json/indent/load_received_qty/",
                type: "post",
                datatype: "json",
                data: {indent_no: $('#id_indent-indent_no').val()},
                success: function (response) {
                    for(i=0; i < response.length; i++){
                        material = response[i]
                        for(j=1; j < materialRowCount-1; j++){
                            rowClass = $('#id_ind_material-' + parseInt(j - 1) + '-item_id').val() + "_" + $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val()
                            rowMaterialItemId =  $('#id_ind_material-' + parseInt(j - 1) + '-item_id').val()
                            rowMaterialMakeId =  $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val()
                            if(material["item_id"] == rowMaterialItemId && material["make_id"] == rowMaterialMakeId ){
                                $("#indentMaterial").find("."+rowClass).closest("tr").find(".table_received_qty").text(material["grn_qty"])
                            }
                        }
                    }
                }
            });
        }
    }

    $("#load_stock_qty").click(function(){
        if($("#load_stock_qty").is(":checked")) {
            load_stock_qty();
            $(".table_stock_in_hand").removeClass('hide')
        }
        else{
            $(".table_stock_in_hand").addClass('hide')
        }
    });

    function load_stock_qty(){
        if ($("#id_indent-indent_no").val() != "") {
            var materialTable = document.getElementById("indentMaterial");
            var materialRowCount = materialTable.rows.length;
            $.ajax({
                url: "/erp/stores/json/indent/load_stock_qty/",
                type: "post",
                datatype: "json",
                data: {indent_no: $('#id_indent-indent_no').val()},
                success: function (response) {
                    for(i=0; i < response.length; i++){
                        material = response[i]
                        for(j=1; j < materialRowCount-1; j++){
                            rowClass = $('#id_ind_material-' + parseInt(j - 1) + '-item_id').val() + "_" + $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val()
                            rowMaterialItemId =  $('#id_ind_material-' + parseInt(j - 1) + '-item_id').val()
                            rowMaterialMakeId =  $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val()
                            if(material["item_id"] == rowMaterialItemId && material["make_id"] == rowMaterialMakeId ){
                                $("#indentMaterial").find("."+rowClass).closest("tr").find(".table_stock_in_hand").text(material["stock_qty"])
                            }
                        }
                    }
                }
            });
        }
    }

    function short_close(){
        var initialFormCount = parseInt(document.getElementById('id_ind_material-INITIAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            var qty = document.getElementById('id_ind_material-' + i + '-quantity');
            var po_qty = document.getElementById("id_ind_material-" + i + "-po_qty");
            qty.value = po_qty.value;
        }
    }

    function checkIndentMaterialQty(){
        check =true
        var message="\n"
        var initialFormCount = parseInt(document.getElementById('id_ind_material-INITIAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            var qty = document.getElementById('id_ind_material-' + i + '-quantity');
            var po_qty = document.getElementById("id_ind_material-" + i + "-po_qty");
            var description = document.getElementById("id_ind_material-" + i + "-description");
            if (parseFloat(qty.value) < parseFloat(po_qty.value)){
                check = false;
                message += description.value + "\n";
            }
        }
        if (check==false){
            swal('','Requested Quantity cannot be less than PO Quantity!'+ message ,'error');
            return false;
        }
        return true;
    }



    $('#cmdtcketback').click(function () {
        hidepage1();
    });
    $('#cmdcancel').click(function () {
        hidepage1();
    });
    $('#cmdadd').click(function () {
        addupdate(1);
    });
});

$.extend($.expr[":"], {
    "contains-ci": function (elem, i, match, array) {
        return (elem.textContent || elem.innerText || $(elem).text() || "").toLowerCase().indexOf((match[3] || "").toLowerCase()) >= 0;
    }
});

$(document).ready(function () {
    var url = window.location.href;
    url = url.substring(7)
    window.urlpath = url.substring(0, url.indexOf('/'));
    PoQuantityPriceUpdate();
	action_count = 0;

    materialListBlurEvent('materialrequired');

    $('#id_expectedDate').datepicker('setDate', '+0');

    transformToDummyForm('ind_material');
    transformToDummyForm('tag');
    create_delete_tag_button();
    populateUnitOptions("",'id_ind_material-__prefix__-all_units');
    loadMaterial("onload");
    $('#indMaterialForms').ready(function () {
        var initialFormCount = parseInt(document.getElementById('id_ind_material-INITIAL_FORMS').value);
        for (i = 0; i < initialFormCount; i++) {
            var matLabel = document.getElementById('id_ind_material-' + i + '-materialLabel');
            var s_no = document.getElementById("id_ind_material-" + i + "-s_no");
            matLabel.innerHTML = $('#id_ind_material-' + i + '-description').val();
            s_no.innerHTML = "" + parseInt(i+1) + ".";
            var makeLabel = document.getElementById('id_ind_material-' + i + '-makeLabel');
            makeLabel.innerHTML = $('#id_ind_material-' + i + '-make').val();
        }
    });
	FromToDateValidation();
    updateProjectChosen($("#selected_project_code").val(), "id_indent-project_code");
	$(".chosen-select").chosen();
	$("#cmdshow").hide();

    $('.modal').on('hidden.bs.modal', function(e) {
        $(this).find('.error-border').removeClass("error-border");
        $(".duplicate_drawing").hide();
        $(this).find('.custom-error-message').remove();
        $("#ul-tagit-display").find(".li-tagit-display").find('.delete_tag').click();
    });

      $('#poListmodal').on('hidden.bs.modal', function(e) {
        $("#po_list > tbody").html("");
    });

	$('#edit_catalogue_form').submit(function() {
        $.ajax({
            data: $(this).serialize(),
            type: $(this).attr('method'),
            url: $(this).attr('action'),
            success: function(response) {
                var item_name = ""
                $('#loading').hide();
                $("#material_id_hidden").val(response['item_id']);
                $("#materialrequired").val(response['name']).attr("readonly","readonly");
                $("#materialrequired").closest("div").find(".material-removal-icon").removeClass("hide");
                $("#material_id").val(response['item_id']);
                $("#id_ind_material-__prefix__-drawing_no").val(response['drawing_no']);
                $("#id_ind_material-__prefix__-is_service").val(response['is_service']);
                item_name = response['name']
                if(response['drawing_no'] !="" && response['drawing_no'] != null) {
                    item_name +=  " - "  + response['drawing_no']
                }
                if(response['is_service'] == 1){
                    item_name += `<span class="service-item-flag"></span>`;
                }
                $("#id_ind_material-__prefix__-description").val(item_name);
                $("#id_ind_material-__prefix__-enterprise_id").val(response['enterprise_id']);
                $("#id_ind_material-__prefix__-item_id").val(response['item_id']);
                $('#id_ind_material-__prefix__-alternate_units').html("");
                if($('#is_multiple_units').val()=="True" ){
                    if (response['alternate_unit_count'] > 0){
                        loadAlternateUnits(response['item_id'], response['unit_name'], '#id_ind_material-__prefix__-alternate_units');
                        $(".alternate_unit_select_box").removeClass("hide");
                    }
                    else {
                        $(".alternate_unit_select_box").addClass("hide");
                    }
                }
                $("#id_ind_material-__prefix__-units").val(response['unit_name']) ;
                $("#ind_unit_display").text(response['unit_name']);
                //loadMakeList();
                if(!$('#add_material_modal').hasClass('in')) {
                    $("#add_new_ind_material").trigger("click");
                }
                $('#add_material_modal').modal('hide')
                setTimeout(function(){
                    $("#id_ind_material-__prefix__-quantity").focus();
                },250);

            }
        });
        return false;
    });
    if($('#most_used_project_code').val()){
        $('#id_indent-project_code').val($('#most_used_project_code').val()).trigger("chosen:updated");
    }

//        constructPO();
        $('#supplier').on('change', function() {
          var that = $(this);
          that.closest('.temp-bom').attr('data-supplier',that.val());
            var party_id= this.value
            $.ajax({
            url: "/erp/purchase/json/po/load_party_currency/",
            type: "post",
            datatype: "json",
            data: {party_id:party_id},
            success: function(response) {
                that.closest('.temp-bom').attr('data-currency',response);
                if (response !=""){

                }
            }
        })
        });
        var spl_ins = 'Gst extra'; //special instructions will be changed in future based on some extra selections;
        purchase_order_dict = {
                                "project_code":$("#po_project_code").val(), "indent_no":$("#po_indent_no").val(), "purpose": $("#po_purpose").val(),
                                "closing_remarks":spl_ins,"stock_type": 1,"po_type":"", "supplier_id":"",
                                "quotation_ref_no":"", "quotation_date":"", "payment":"",
                                "transport":"","packing":"","delivery":"",
                                "total":"","tax1type":1,"tax1":0,"tax2":0,
                                "indent_type":$("#po_intent_type").val(),"currency_code":"",
                                "pay_against":"","pay_in_days":"","pay_through":"","quick_po_type":1
                              };
        $( ".txt_po_quantity" ).keyup();
});


function PoQuantityPriceUpdate() {
    $(".txt_po_quantity, .txt_po_unitrate").on('keyup blur', function () {
        var po_qty = $(this).closest('tr').find('.indent_po_qty input').val();
        var po_price = $(this).closest('tr').find('.td_po_unit_price input').val();
        $(this).closest('tr').find('.txt_po_price').val(Number(po_qty * po_price).toFixed(2));
    });
}

function constructPO(){
    var tags =getTagsDetails();
    po_material_json = [];
    var i = 0;
    var taxes = []
    var material_taxes = ['0','0','0']
    purchase_order_json_list = []
    var spqValidationCheck = true;
    $(".btn-spq").remove();
    $(".append_supplier_table").find('.temp-bom').each(function(){
        var curSupplierId = $(this).data('supplier');
        var curSupplierCurrency = $(this).data('currency');
        var isQtyValue = 0;
        var shipping_name = ''
        var shipping_address = ''
        console.log('curSupplierId', curSupplierId);
        if (curSupplierId){
                $.ajax({
                    async: false,
                    url: "/erp/purchase/json/purchaseLatestUsedSupplierDetails/",
                    type: "post",
                    datatype:"json",
                    data: {party_id: curSupplierId},
                    success: function(response){
                        party_details = response['party_details']
                        shipping_address = party_details['shipping_address']
                        shipping_name = party_details['shipping_name']
                    }
                });

        }
        console.log('shipping_name', shipping_name)
        console.log('shipping_address', shipping_address)
        $(this).find(".type_order_container").each(function(){
            if($(this).find(".is_po_checked").is(":checked")) {
                $(this).find('.temp-bom-row').each(function(){
                    isQtyValue += Number($(this).find(".txt_po_quantity").val());
                })
                if(isQtyValue <= 0) {
                    $(this).find(".is_po_checked").prop("checked", false)
                }
            }
        });
        $(this).find(".type_order_container").each(function(){
            if($(this).find(".is_po_checked").is(":checked")) {
                if($(this).find('.temp-bom-row').not(".disabled_material").length > 0  && isQtyValue > 0) {
                    var supplierName = $(this).closest(".temp-bom").prev("ul").find(".supplier_name_tab").text();
                    var spl_ins = $(this).find('table tfoot [name="po-instruction"]').val();
                    var po_remarks = $(this).find('table tfoot [name="po-remarks"]').val();
                    var txtpayment = $(this).find('table tfoot [name="txtpayment"]').val();
                    var no_of_days = ($(this).find('table tfoot [name="no_of_days"]').val() != "") ? $(this).find('table tfoot [name="no_of_days"]').val(): 0;
                    var pay_type = $(this).find('table tfoot [name="pay_type"] :selected').val();
                    var pay_mode = $(this).find('table tfoot [name="pay_mode"] :selected').val();
                    var delivery_due_date = moment($(this).find('.delivery-due-date').val()).format('YYYY-MM-DD');
                    purchase_order_dict = {
                                            "project_code":$("#po_project_code").val(), "indent_no":$("#po_indent_no").val(), "purpose": $("#po_purpose").val(),
                                            "closing_remarks":spl_ins,"stock_type": 0,"po_type":$(this).find('.is_po_checked').val(), "supplier_id":curSupplierId,
                                            "quotation_ref_no":"", "quotation_date":moment().format('YYYY-MM-DD'), "payment":"",
                                            "transport":0,"packing":0,"delivery": delivery_due_date, "tax1type":1,"tax1":0,"tax2":0,
                                            "purchase_account_id":$("#po_intent_type").val(),"currency_code":curSupplierCurrency,
                                            "pay_against":0,"pay_in_days":0,"pay_through":0 , "tags":tags, 'taxes':taxes, "bucket_name":supplierName,"quick_po_type":1 ,"po_remarks":po_remarks,
                                            'payment': txtpayment, 'pay_in_days': no_of_days, 'pay_against': pay_type, 'pay_through': pay_mode, "is_blanket_po": 0,
                                            "valid_since":"", "valid_till":"", "round_off": 0, "shipping_name": shipping_name, "shipping_address": shipping_address
                                          };
                    var total_material_price = 0 ;
                    $(this).find('.temp-bom-row').not(".disabled_material").each(function(){
                        var mat_make_id = $(this).find('.indent_material_make_id').text();
                        var mat_no = $(this).find('.indent_material_no').text();
                        if(mat_no.indexOf("__non_stock__") > 0) mat_no = "";
                        var item_id = $(this).find('.indent_material_item_id').text();
                        if(mat_no.indexOf("__non_stock__") > 0) item_id = "";
                        var mat_name = $(this).find('.indent_material_name').text();
                        var mat_unit_price = $(this).find('.indent_unit_price input').val();
                        var mat_po_qty = $(this).find('.indent_po_qty input').val();
                        var mat_unit = $(this).find('.indent_material_unit_id').text();
                        var mat_tot_price = $(this).find('.indent_total_price input').val();
                        var mat_indent_qty = $(this).find('.indent_indent_qty').text();
                        var mat_prev_po_qty = $(this).find('.indent_prev_po_qty').text();
                        var alternate_unit_id = $(this).find('.indent_material_alternate_unit_id').text();
                        var scale_factor = $(this).find('.indent_material_scale_factor').text();
                        var spq = $(this).find('.indent_material_spq').text();
                        var moq = $(this).find('.indent_material_moq').text();
                        if(!$(this).find('.indent_po_qty input').hasClass("spq-validated")) {
                            if($(this).find(".indent_material_alternate_unit_id").text().trim() != 0) {
                                alternateUnitValue = $(this).find(".indent_material_scale_factor").text().trim();
                                unit = $(this).find(".indent_unit").text().trim();
                                spq = spq / alternateUnitValue;
                            }
                            var checkMod = (mat_po_qty / spq).toFixed(3);
                            var ceilValue = spq_round_ceil(spq, mat_po_qty);
                            var floorValue = spq_round_floor(spq, mat_po_qty);
                            var thumbTooltip = `Use as is: ${mat_po_qty}`;
                            var upTooltip = `SPQ is ${spq}. Round up to SPQ as ${ceilValue}`;
                            var downTooltip = `SPQ is ${spq}. Round down to SPQ as ${floorValue}`;
                            var moqTooltip = `MOQ is ${moq}`;
                            if (!Number.isInteger(Number(checkMod)) && spq > 0){
                                spqValidationCheck = false;
                                var editRow = `<span onclick='spqSetRoundValue(this, ${floorValue}, ${ceilValue})' ><i class="fa fa-arrow-circle-down btn-spq" data-tooltip="tooltip" title="${downTooltip}" aria-hidden="true"></i></span>`;
                                if($("#is_purchase_approve_permission").val() == "True") {
                                    editRow += `<span onclick='spqSetDefault(this, ${mat_po_qty})'><i class="fa fa-thumbs-o-up btn-spq" data-tooltip="tooltip" title="${thumbTooltip}" aria-hidden="true"></i></span>`;
                                }
                                editRow += `<span onclick='spqSetRoundValue(this, ${ceilValue}, ${ceilValue})' ><i class="fa fa-arrow-circle-up btn-spq" data-tooltip="tooltip" title="${upTooltip}" aria-hidden="true"></i></span>`;
                                if(Number(ceilValue) < Number(moq) ) {
                                    editRow += `<span onclick='spqSetRoundValue(this, ${moq}, ${moq})' ><i class="fa fa-level-up btn-spq" data-tooltip="tooltip" title="${moqTooltip}" aria-hidden="true"></i></span>`;
                                }
                                $(this).find('.indent_po_qty input').addClass("error-border");
                                $(this).find('.indent_po_qty').append(editRow);
                                TooltipInit();
                            }
                            else if(Number(mat_po_qty) < Number(moq)) {
                                spqValidationCheck = false;
                                var editRow = `<span onclick='spqSetDefault(this, ${mat_po_qty})'><i class="fa fa-thumbs-o-up btn-spq" data-tooltip="tooltip" title="${thumbTooltip}" aria-hidden="true"></i></span>`;
                                editRow += `<span onclick='spqSetRoundValue(this, ${moq}, ${moq})' ><i class="fa fa-level-up btn-spq" data-tooltip="tooltip" title="${moqTooltip}" aria-hidden="true"></i></span>`;
                                $(this).find('.indent_po_qty input').addClass("error-border");
                                $(this).find('.indent_po_qty').append(editRow);
                                TooltipInit();
                            }
                        }
                        total_material_price =  parseFloat(total_material_price) +  (mat_po_qty * parseFloat(mat_unit_price) )
                        var deliveryDueJson = [];
                        var deliveryDueObj = {
                            delivery_date: delivery_due_date,
                            delivery_qty: mat_po_qty
                        };
                        deliveryDueJson.push(deliveryDueObj);
                        var obj = {

                            'item_id':item_id,
                            'drawing_no':mat_no,
                            'drawing_name': mat_name,
                            'approved_price': mat_unit_price,
                            'rate': mat_unit_price,
                            'discount': 0,
                            'quantity': mat_po_qty,
                            'unit_id' :mat_unit,
                            'item_make':mat_make_id,
                            'total_price': mat_tot_price,
                            'request_qty': mat_indent_qty,
                            'raised_qty': mat_prev_po_qty,
                            'balance_qty': parseInt(mat_indent_qty)-parseInt(mat_prev_po_qty),
                            'material_taxes':material_taxes,
                            'alternate_unit_id': alternate_unit_id,
                            'scale_factor': scale_factor,
                            'delivery_schedules': JSON.stringify(deliveryDueJson)
                        };
                        po_material_json.push(obj);
                        purchase_order_dict['po_Materials']
                    });
                    purchase_order_dict = Object.assign({"materials":po_material_json}, purchase_order_dict)
                    purchase_order_dict = Object.assign({"total":(total_material_price).toFixed(2)}, purchase_order_dict)

                    po_material_json = []
                    purchase_order_json_list.push(purchase_order_dict);
                }
            }
        });
    });
    if(spqValidationCheck) {
        var token = getCookie('csrftoken');

        if(purchase_order_json_list.length > 0){
            var response_messages =[]
            var item_count = 0;
            $.each( purchase_order_json_list, function( index, item ){
                item_count=purchase_order_json_list.length;
                var length= 0;
                var temp = 0;
                console.log("item:", JSON.stringify(item))
                $.ajax({
                    async: false,
                    global: false,
                    url: "/erp/purchase/json/po/save/",
                    type: "POST",
                    dataType: "json",
                    data: { 'csrfmiddlewaretoken': token,'purchase_order_dict':JSON.stringify(item) } ,
                    success: function (json) {
                        response = json['message']
                        draft_no = json['draft_no']
                        response_messages.push("\n" + draft_no+"-"+response+"  for " + item['bucket_name']+ "\n")
                    },
                    error: function (xhr, errmsg, err) {
                        console.log(xhr.status + ": " + xhr.responseText);
                    }
                });

            });

            swal({
                title: "",
                text: response_messages,
                type: "success",
                allowEscapeKey: false
            }, function(){
                $("#loading").show();
                $( "#edit_indent_click_button" ).click();
            });
            setTimeout(function(){
                $("#loading").hide();
            },50);
            ga('send', 'event', 'Purchase Order', "Create via Indent buckets", $('#enterprise_label').val(), item_count);
        }
        else{
            $("#loading").hide();
            swal("",'Please select atleast one supplier or add quantity to atleast one material to generate P.O. ','warning');
        }
    }
    else {
        swal("","One or more Order Quantity constraints has been violated for one or more Items.<br />Choose an appropriate Quantity for each of them","warning");
        $("#loading").hide();
    }

}

function spqSetDefault(current, qty){
    $(current).closest("td").find("input").addClass("spq-validated").removeClass("error-border");
    $(current).closest("td").find(".btn-spq").hide();
    setTimeout(function(){
        $(current).closest("td").find(".btn-spq").remove();
    },100);
}

function spqSetRoundValue(current, qty, maxQty){
    $(current).closest("td").find("input").addClass("spq-validated").removeClass("error-border").val(qty.toFixed(3));
    $(current).closest("td").find(".btn-spq").hide();
    setTimeout(function(){
        $(current).closest("td").find(".btn-spq").remove();
    },100);
}


function closeCatalogues() {
    //document.getElementById("catcss").style.display = "none";
    $("#catalogueModal").modal('hide');
}

function addIndent() {
    var table = document.getElementById("cattable");
    var rowCount = table.rows.length;
    var input_obj = document.getElementsByName('catmaterial');
    var input_obj_item_id = document.getElementsByName('catmaterial_item_id');
    var input_obj_is_service = document.getElementsByName('catmaterial_is_service');

    var match = false;
    for (i = 1; i < rowCount; i++) {
        match = false;
        if($(table.rows[i]).data('toggle') == 'close') {
            if (parseFloat(input_obj[i - 1].value) > 0) {
                var materialtable = document.getElementById("indentMaterial");
                var materialrowCount = materialtable.rows.length;
                for (j = 1; j < materialrowCount; j++) {
                    if ($(input_obj[i-1]).closest('td').prev('td').find('option:selected').text() !="") {
                        if ( $('#id_ind_material-' + parseInt(j - 1) + '-make_id').val() == $(input_obj[i-1]).closest('td').prev('td').find('option:selected').val() && $('#id_ind_material-' + parseInt(j - 1) + '-item_id').val() == $(table.rows[i]).find('input[name="catmaterial_item_id"]').val() && !document.getElementById("id_ind_material-"+parseInt(j - 1)+"-DELETE").checked) {
                            match = true;
                            document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_ind_material-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                            if ($("#id_indent-indent_no").val()!=""){
                                var qty_label = document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantityLabel');
                                $('#id_ind_material-' + parseInt(j - 1) + '-quantityLabel').html(parseFloat($('#id_ind_material-' + parseInt(j - 1) + '-quantity').val()));
                            }
                        }
                    }
                    else {
                        if ($('#id_ind_material-' + parseInt(j - 1) + '-item_id').val() == $(table.rows[i]).find('input[name="catmaterial_item_id"]').val()) {
                            match = true;
                            document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantity').value = parseFloat($('#id_ind_material-' + parseInt(j - 1) + '-quantity').val()) + parseFloat(input_obj[i - 1].value);
                            if ($("#id_indent-indent_no").val()!=""){
                                var qty_label = document.getElementById('id_ind_material-' + parseInt(j - 1) + '-quantityLabel');
                                $('#id_ind_material-' + parseInt(j - 1) + '-quantityLabel').html(parseFloat($('#id_ind_material-' + parseInt(j - 1) + '-quantity').val()));
                            }
                        }
                    }
                }
                if (match == false) {
                    document.getElementById("id_ind_material-__prefix__-drawing_no").value = trim($(table.rows[i].cells[8]).text());
                    document.getElementById("id_ind_material-__prefix__-item_id").value = parseFloat(input_obj_item_id[i - 1].value);
                    document.getElementById("id_ind_material-__prefix__-quantity").value = parseFloat(input_obj[i - 1].value);
                    document.getElementById('id_ind_material-__prefix__-description').value = $(table.rows[i].cells[0]).text();
                    document.getElementById("id_ind_material-__prefix__-units").value = $(table.rows[i].cells[6]).text();
                    document.getElementById("id_ind_material-__prefix__-is_service").value = $(table.rows[i]).find("input[name='catmaterial_is_service']").val();
//                    generateFormsetFormRow('ind_material');
                    if($("#id_ind_material-__prefix__-is_service").val() == 1) {
                        generateFormsetFormRowAppend('ind_material', ".item-for-service");
                    }
                    else {
                        generateFormsetFormRowAppend('ind_material', ".item-for-goods");
                    }
                    var index = parseInt(parseInt($('#id_ind_material-TOTAL_FORMS').val()) - 1);
                    var mat_label = document.getElementById('id_ind_material-' + index + '-materialLabel');
                    var s_no = document.getElementById("id_ind_material-" + index + "-s_no");

                    var new_form_fk_field = document.getElementById('id_ind_material-' + index + '-indent_no');
                    var new_form_drawing_no = document.getElementById('id_ind_material-' + index + '-drawing_no');
                    var new_form_is_service = document.getElementById('id_ind_material-' + index + '-is_service');
                    var new_form_item_id = document.getElementById('id_ind_material-' + index + '-item_id');
                    var new_form_enterprise_id = document.getElementById('id_ind_material-' + index + '-enterprise_id');
                    var new_form_quantity = document.getElementById('id_ind_material-' + index + '-quantity');
                    var new_form_units = document.getElementById('id_ind_material-' + index + '-units_label');
                    var new_form_description = document.getElementById('id_ind_material-' + index + '-description');

                    new_form_fk_field.value = document.getElementById('id_indent-indent_no').value;
                    new_form_drawing_no.value = document.getElementById('id_ind_material-__prefix__-drawing_no').value;
                    new_form_item_id.value = document.getElementById('id_ind_material-__prefix__-item_id').value;
                    new_form_enterprise_id.value = document.getElementById('id_ind_material-__prefix__-enterprise_id').value;
                    new_form_quantity.value = document.getElementById('id_ind_material-__prefix__-quantity').value;
                    new_form_units.innerHTML = document.getElementById('id_ind_material-__prefix__-units').value;
                    new_form_description.value = document.getElementById('id_ind_material-__prefix__-description').value;
                    new_form_is_service.value = document.getElementById('id_ind_material-__prefix__-is_service').value;

                     var item_name = $(table.rows[i].cells[0]).text()

                    var new_form_make_id = document.getElementById('id_ind_material-' + index + '-make_id');
                    var makeLabel = document.getElementById('id_ind_material-' + index + '-makeLabel');
                    makeLabel.innerHTML = $(input_obj[i-1]).closest('td').prev('td').find('option:selected').text();
//                    new_form_make_id.value = $(input_obj[i-1]).closest('td').prev('td').find('option:selected').val();
                    new_form_make_id.value = 1
//                    if ($(input_obj[i-1]).closest('td').prev('td').find('option:selected').val()!= 1){
//                        item_name = item_name + "[" + $(input_obj[i-1]).closest('td').prev('td').find('option:selected').text() + "]"
//                    }
                    var make_name = document.getElementsByName("make_name");
                    if( make_name[i-1].value != '')
                    {
                    item_name = item_name + " [" + make_name[i-1].value + "]"
                    }
                    if($("#id_ind_material-__prefix__-is_service").val() == 1) {
                        item_name += '<span class="service-item-flag"></span>';
                    }

                    mat_label.innerHTML = item_name;
                    s_no.innerHTML = index + 1+".";
                }
	        }
        }
    }
    $("#catalogueModal").modal('hide');
    $('#materialrequired, #id_ind_material-__prefix__-quantity').val('');
	$("#ind_unit_display").html('&nbsp;');
    $('#material_id_hidden').val('');
    $('#materialr_id').val('');
    $("#cmdshow").hide();
    $(".material-removal-icon").click();
    indentMaterialTableSerialNumberInit();
	setTimeout(function(){
//		$('#materialrequired').focus();
	},250);
}


function deleteCatRow(currentRow) {
    try {
        if (window.confirm('Do you want delete this row?')) {
            var table = document.getElementById("cattable");
            var rowCount = table.rows.length;
            for (var i = 0; i < rowCount; i++) {
                var row = table.rows[i];
                if (row == currentRow.parentNode.parentNode) {
                    if (rowCount <= 1) {
                        alert("Cannot delete all the rows.");
                        break;
                    }
                    table.deleteRow(i);
                    rowCount--;
                    i--;
                }
            }
        }
    } catch (e) {
        alert(e);
    }
}

function indent_copyFromEmptyForm(form_prefix, form_idx, fk_field_name, new_form_fk_field_name) {
    var new_form_fk_field = document.getElementById('id_' + form_prefix + '-' + form_idx + '-' + new_form_fk_field_name);
    var new_form_drawing_no = document.getElementById('id_' + form_prefix + '-' + form_idx + '-drawing_no');
    var new_form_is_service = document.getElementById('id_' + form_prefix + '-' + form_idx + '-is_service');
    var new_form_item_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-item_id');
    var new_form_enterprise_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-enterprise_id');
    var new_form_quantity = document.getElementById('id_' + form_prefix + '-' + form_idx + '-quantity');
    var new_form_units = document.getElementById('id_' + form_prefix + '-' + form_idx + '-units_label');
    var new_form_description = document.getElementById('id_' + form_prefix + '-' + form_idx + '-description');
    var new_form_alternate_unit_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-alternate_unit_id');
    var new_form_scale_factor = document.getElementById('id_' + form_prefix + '-' + form_idx + '-scale_factor');


    new_form_fk_field.value = document.getElementById('id_' + fk_field_name).value;
    new_form_drawing_no.value = document.getElementById('id_' + form_prefix + '-__prefix__-drawing_no').value;
    new_form_is_service.value = document.getElementById('id_' + form_prefix + '-__prefix__-is_service').value;
    new_form_item_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value;
    new_form_enterprise_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-enterprise_id').value;
    new_form_quantity.value = document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value;
    if (document.getElementById('id_' + form_prefix + '-__prefix__-alternate_units').value == 0 ) {
        new_form_units.innerHTML = document.getElementById('id_' + form_prefix + '-__prefix__-units').value;
    } else{
        new_form_units.innerHTML = $('#id_ind_material-__prefix__-alternate_units option:selected').text();
        new_form_alternate_unit_id.value = $('#id_ind_material-__prefix__-alternate_units option:selected').val();
        new_form_scale_factor.value = $('#id_ind_material-__prefix__-alternate_units option:selected').attr("data-val");
    }
    var item_name = document.getElementById('id_' + form_prefix + '-__prefix__-description').value
    var new_form_make_id = document.getElementById('id_' + form_prefix + '-' + form_idx + '-make_id');
    new_form_make_id.value = document.getElementById('id_' + form_prefix + '-__prefix__-make_label').value;
    new_form_description.value = item_name;

    document.getElementById('id_' + form_prefix + '-__prefix__-drawing_no').value = 'None';
    document.getElementById('id_' + form_prefix + '-__prefix__-item_id').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-quantity').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-description').value = '';
    document.getElementById('id_' + form_prefix + '-__prefix__-is_service').value = '';

}


function deleteIndMaterial(catMatFormId) {
    swal({
        title: "Are you sure?",
        text: "Do you want to delete this Material",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#209be1",
        confirmButtonText: "Yes, delete it!",
        closeOnConfirm: true
    },
    function(){
        if ($("#id_indent-indent_no").val() != "") {
            var indent_no = document.getElementById('id_' + catMatFormId + '-indent_no').value;
            var item_id = document.getElementById('id_' + catMatFormId + '-item_id').value;
            var make_id = document.getElementById('id_' + catMatFormId + '-make_id').value;
            $.ajax({
                url: "/erp/stores/json/indent/checkpomaterial/",
                type: "post",
                datatype: "json",
                data: {indent_no: indent_no, item_id: item_id, make_id: make_id},
                success: function (response) {
                    if (response[0][0]==0){
                        var deleteFlag = document.getElementById('id_' + catMatFormId + '-DELETE');
                            var deleteRow = document.getElementById(catMatFormId);
                            deleteFlag.checked = true;
                            deleteRow.style.display = 'none';
                    }
                    else {
                        setTimeout(function(){
                            swal("WARNING", "Some POs have been raised against this Material. Please delete those POs before deleting this Material.", "warning");
                        },500);
                    }
                }
            });
        }
        else {
            var deleteFlag = document.getElementById('id_' + catMatFormId + '-DELETE');
            var deleteRow = document.getElementById(catMatFormId);
            deleteFlag.checked = true;
            deleteRow.style.display = 'none';
        }
    });
}


function changeImage(imgId, imgSrc) {
    var img = document.getElementById(imgId);
    img.src = imgSrc;
}

function matLabelLoad(catMatFormId) {
    var matLabel = document.getElementById('id_' + catMatFormId + '-materialLabel');
    matLabel.innerHTML = $('#id_' + catMatFormId + '-material option:selected').text();
}

function loadMaterial(loadType = "") {
    var dataToSend = {
        'type': "indents",
        'module': 'indents',
        'particulars':'indent_material'
    }
    var local_material_list  = JSON.parse(localStorage.getItem('goods_service_master_data'));
    if(loadType == "onchange" && local_material_list) {
        var material_choices = local_material_list[0].material;
    }
    else {
        var material_choices = generateMaterialAsAutoComplete(dataToSend);
    }
    var frequently_material_choices = generateFrequentlyMaterialAsAutoComplete(dataToSend);

    $("#materialrequired").materialAutocomplete({
        source: frequently_material_choices.concat(material_choices),
        minLength:1,
        response: function( event, ui ) {
            ui.content.unshift({value:"add_new_item", label:"+ ADD NEW MATERIAL ", category: ""});
        },
        open: function(event, ui) {
            $("ul.ui-autocomplete.ui-menu a:contains('+ ADD NEW MATERIAL')").addClass("add_new_material_link");
        },
        select: function (event, ui) {
            event.preventDefault();
            if(ui.item.value != "add_new_item") {
                var itemName = ui.item.label.replace("<span class='service-item-flag'></span>", '');
                $("#material_id_hidden").val(ui.item.id);
                $("#materialrequired").val(itemName).attr("readonly", true)
                $("#material_id").val(ui.item.id);
                $("#cat_code").val(ui.item.id);
                $('#id_ind_material-__prefix__-alternate_units').html("");
                if($('#is_multiple_units').val()=="True" ){
                    if (ui.item.alt_uom > 0){
                        loadAlternateUnits(ui.item.id, ui.item.unit, '#id_ind_material-__prefix__-alternate_units');
                        $(".alternate_unit_select_box").removeClass("hide");
                    }
                    else {
                        $(".alternate_unit_select_box").addClass("hide");
                    }
                }
                $("#id_ind_material-__prefix__-drawing_no").val(ui.item.id);
                $("#id_ind_material-__prefix__-is_service").val(ui.item.is_service);
                $("#id_ind_material-__prefix__-item_id").val(ui.item.id);
                $("#id_ind_material-__prefix__-description").val(ui.item.label);
                $("#id_ind_material-__prefix__-enterprise_id").val($("#id_indent-enterprise_id").val());
                $("#id_ind_material-__prefix__-units").val(ui.item.unit) ;
                $("#id_ind_material-__prefix__-make_label").val(ui.item.mid);
                $("#ind_unit_display").text(ui.item.unit);
                if (parseInt(ui.item.bom) > 0 ){
                    $("#cmdshow").show()
                }else{
                    $("#cmdshow").hide()
                }
                $(".material-removal-icon").removeClass('hide')
                setTimeout(function(){
                    $("#id_ind_material-__prefix__-quantity").focus();
                },250);
            }
            else {
                openMaterialAddModal();
            }
        }
    }).on('focus', function() { $(this).keydown(); });
}


$('#saveCatalogueButton').submit(function(e) {
    e.preventDefault();
    $('#add_material_modal').modal('toggle');
    return false;
});

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}

$('.nav-pills li').removeClass('active');
$('#li_indent').addClass('active');


function CheckNo(sender){
    if(!isNaN(sender.value)){
        if(sender.value == "" )
            sender.value = 0;
    }else{
          sender.value = 0;
    }
}

function getTagsDetails() {
    var selectedTag =[];
    $("#tab2 #ul-tagit-display").find(".li-tagit-display").each(function(){
        selectedTag.push($(this).text().trim());
    });
    var tags;
    $.ajax({
        async: false,
        global: false,
        url: "/erp/stores/json/indent/getTagDetails/",
        type: "post",
        datatype: "json",
        data: {'tag_list[]': selectedTag},
        success: function (response){
            tags = response;
        }
    });
    return tags;
}



function showPoList(selectedItem) {
    var supplier_main_list = [];
    $("#indent_bucket_view").find(".parent-supplier-container").each(function(){
        if($(this).attr("id") != "no-supplier") {
            supplier_main_list.push($(this).attr("id"));
        }
    });
    var thisVal = selectedItem.id;
    var supplier_Id = $("#"+thisVal).data('po-list-id');
    $("#poListmodal").find('h4.modal-title').text(selectedItem.parentNode.parentElement.firstElementChild.textContent);
    $('#poListmodal-title').text(selectedItem.text);
    $('#po_indent_no').val();
    $("#poListmodal").modal('show');
    $.ajax({
        url: "/erp/stores/json/indent/loadPoList",
        type: "POST",
        dataType: "json",
        data: {'indent_no': $('#po_indent_no').val(),'supplier_id': supplier_Id,'available_supplier_list[]': supplier_main_list},
        success: function (response) {
            $("#po_list tbody").html("");
            if(response.length > 0) {
                $.each(response, function (i, item) {
                    var csrf_token = document.getElementsByName('csrfmiddlewaretoken')[0].value;
                    var item_name=""
                    if (item.make != "-NA-") {
                        item_name  = item.item_code + " [" + item.make + "]"
                    }
                    else {
                        item_name  = item.item_code
                    }

                    po_no = '' + item[2];
                    if(po_no == '0'){
                        po_code = 'NA';
                    }
                    else {
                        while(po_no.length < 7){
                            po_no = '0' + po_no;
                        }
                        if (item[13]==0){
                            po_code = item[10] + "/PO/" + po_no;
                        }
                        else if(item[13]==1){
                            po_code = item[10] + "/JO/" + po_no;
                        }
                    }
                    indent_no =  item[12] + "/IND/" + item[11];

                    if (item[8]==0){
                        status="Draft";
                    }
                    else if (item[8]==1){
                        status="Reviewed";
                    }
                    else if (item[8]==2){
                        status="Approved";
                    }
                    else {
                        status="Rejected";
                    }
                    var pdf_generation_form = "<form id='pdf_generation' method='POST' action='/erp/purchase/po/pdf/'>"+
                        "<input type=\'hidden\' name=\'csrfmiddlewaretoken\' value=\'" +csrf_token+ "\' />"+
                        "<input name='po_id' value='"+ item[0] +"' hidden/>" +status+
                        "<input type='submit' hidden id='generate_pdf_"+ item[0] + "' /></form>";
                        var dDate = item[1];
                        dDate = moment(dDate, "DD.MM.YYYY").format("MMM D, YYYY");
                        if(dDate.toLowerCase().trim() == "invalid date") { dDate = '-'; }
                        var pDate = item[3];
                        pDate = moment(pDate, "DD.MM.YYYY").format("MMM D, YYYY");
                        if(pDate.toLowerCase().trim() == "invalid date") { pDate = '-'; }

                    var row = "<tr align='center'><td align='center'>"+(i+1)+".</td><td>" +
                        "<a id='po_draft_no' role='button' onclick='javascript:redirectPO("+item[0]+","+item[8]+")'>"+item[0]+ "</td><td hidden='hidden'>" +
                        "<a href='/erp/purchase/po_list/'>"+item[0]+"</td><td>" +
                        dDate + "</td><td class='text-left'>" + po_code + "</td><td >" +
                        pDate + "</td><td class='text-left'>" +
                        item[4] + "</td><td class='text-right'>" +
                        item[5] + "</td><td class='text-left'>" +
                        item[6] + "</td><td class='text-left'>" +
                        indent_no + "</td><td hidden='hidden' class='exclude_export'>" +
                        status + "</td><td>" +
                        pdf_generation_form + "</td>"+"</td></tr>";
                    $('#po_list').append(row).addClass('tbl');
                });
            }
            else {
                $('#poListmodal').modal('hide');
                swal("","There is no P.O. generated against this party","warning");
            }
        },
        error: function (xhr, errmsg, err) {
            console.log(xhr.status + ": " + xhr.responseText);
            $("#poListmodal").modal('hide');
            $('#loading').hide();
        }
    });
}

function setCookie(draft_no, status) {
    var draft_no = draft_no;
    document.cookie = "po_draft_no_indent="+draft_no + "; path=/;";
    document.cookie = "po_draft_no_status="+status + "; path=/;";
}
function redirectPO(draft_no, status){
    setCookie(draft_no, status);
    window.open('/erp/purchase/po/', '_blank');
}

function showCatalogues() {
    var catqty = $("#id_ind_material-__prefix__-quantity").val();
    $.ajax({
        url: "/erp/stores/json/catalogue_materials/",
        type: "post",
        datatype: "json",
        data: {'cat_code': $("#cat_code").val(), "alternate_unit_id":$('#id_ind_material-__prefix__-alternate_units').val()},
        success: function (response) {
            if (response.response_code != 400) {
                $("#cattable").find("tr:gt(0)").remove();
                $("#catButton").find('.modal-footer').remove();
                $.each(response, function (i, item) {
                    if(typeof(Storage) !== "undefined") {
                        if (sessionStorage.clickcount) {
                            sessionStorage.clickcount = Number(sessionStorage.clickcount) + 1;
                        } else {
                            sessionStorage.clickcount = 1;
                        }
                    }
                    var childArrow = "";
                    var makes = "-NA-";
                    var drawing_no = "";
                    var description = "";
                    var is_service_value = item.is_service==true?1:0;
                    var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+is_service_value+' >';
                    var itemTypeFlag = "";
                    if(item.is_service == true){
                        itemTypeFlag = `<span class="service-item-flag"></span>`;
                    }
                    if(!item.material_type && item.is_service != true) {
                        itemTypeFlag = `<span class="non_stock-flag"></span>`;
                    }
                    var item_name = item.name;
                    if (item.make_name !="" && item.make_name !=null) {
                        item_name += " [" + item.make_name +"]";
                    }
                    if(item.hasChildren) {
                        childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+item.item_id+ "_" + sessionStorage.clickcount +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;' >"+item_name+" "+itemTypeFlag+"</a></i>";
                    } else { childArrow = item_name+" "+itemTypeFlag }

                    if (item.makes.length > 0) {
                        makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                        for (j = 0; j <= item.makes.length - 1 ; j++) {
                            makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                        }
                        makes += "</select>";
                    }
                    description = item.name;
                    if (item.drawing_no !="" && item.drawing_no!=null) {
                        drawing_no = item.drawing_no;
                        description += " - " + item.drawing_no;
                    }
                    var is_service_value = item.is_service==true?1:0;
                    // var row = "<tr data-toggle='close' data-padding='0' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                    //     description +"</td><td class='text-left bom-sno'>" +
                    //     (i+1) + "</td><td>"+childArrow+"</td><td>" +
                    //     drawing_no + "</td><td>" +
                    //     makes + "</td><td>" +
                    //     "<input type='text' name ='catmaterial' value='" + (item.quantity * catqty).toFixed(2) + "' class='form-control setUnitValue text-right' id='txtcatqty' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' /></td><td>" +
                    //     item.unit_name + "</td><td class='text-center'>" +
                    //     "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></a>" + "</td><td hidden=hidden>" +
                    //     drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" +
                    //     item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td></tr>";
                     var row = "<tr data-toggle='close' data-padding='0' data-parent='"+item.item_id+ "_" + sessionStorage.clickcount + "' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                        description +"</td><td class='text-left bom-sno'>" +
                        (i+1) + "</td><td>"+childArrow+"</td><td>" +
                        drawing_no + "</td><td hidden='hidden'>" +
                        makes + "</td><td>" +
                        "<input type='text' name ='catmaterial' value='" + (item.quantity * catqty).toFixed(2) + "' class='form-control setUnitValue text-right' id='txtcatqty' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' /></td><td>" +
                        item.unit_name + "</td><td class='text-center'>" +
                        "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></a>" + "</td><td hidden=hidden>" +
                        drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" +
                        item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden=hidden><input type='text' name ='make_name' value='" + item.make_name + "' ></td></tr>";
                    $('#cattable').append(row).addClass('tbl');
                });
                var row = "<div class='modal-footer'><span class='pull-left nonStock-text'>  <span class='nonStock-text non_stock-flag'></span> - NON-STOCKABLE</span><span class='pull-left nonStock-text'>  <span class='nonStock-text service-item-flag'></span> - SERVICE</span><input type='button' onclick='addIndent()' class='btn btn-save' id='catAdd' value='Add' /><a onclick='closeCatalogues()' class='btn btn-cancel' id='catCancel'>Close</a></div>"
                $('#catButton').append(row).addClass('tbl');
                $("#catalogueModal").modal('show');
                $("#loadingmessage_changelog_listing_ie").hide();
            } else {
                // TODO why should I login again here
                swal(response.response_message, "Kindly <a href='/erp/login/'>Login</a> again.", "warning");
                document.location.href="/erp/login/";
                return;
            }
            $(".setUnitValue").blur(function(){
                var setID = $(this).closest('tr').attr('id');
                var setValue = $(this).val();
                $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                    var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                    $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                });
            });
        }
    });
}

function appendMaterial(cat_code, dataChild, dataP) {
    var currentCatChild = $("#"+cat_code + "_" + dataChild).attr('id');
    var current_bom_sno = $("#"+cat_code + "_" + dataChild).find(".bom-sno").text().trim();
    var constructedRow = '';
    if($("#"+currentCatChild).attr('data-toggle') == 'open') {
        var curPadding = $("#"+currentCatChild).attr('data-padding');
        $("#"+currentCatChild).nextAll('tr').each(function(){
            if(Number($(this).attr('data-padding')) > Number(curPadding)) {
                $(this).remove();
            }
            else {
                return false;
            }
        });
        $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-minus').addClass('fa-plus');
        $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','close');
    }
    else {
        $("#cattable #"+cat_code + "_" + dataChild).find('.fa.fa-for-arrow').removeClass('fa-plus').addClass('fa-minus');
        $("#cattable #"+cat_code + "_" + dataChild).attr('data-toggle','open');
        $.ajax({
            url: "/erp/stores/json/catalogue_materials/",
            type: "post",
            dataType: "json",
            data: {'cat_code': cat_code},
            success: function (response) {
                var catqty = $("#id_ind_material-__prefix__-quantity").val();
                $.each(response, function (i, item) {
                if(typeof(Storage) !== "undefined") {
                    if (sessionStorage.clickcount) {
                        sessionStorage.clickcount = Number(sessionStorage.clickcount)+1;
                    } else {
                        sessionStorage.clickcount = 1;
                    }
                }
                var childArrow ="";
                var makes = "-NA-";
                var dataParent = dataP;
                var dataPadding = Number($("#"+cat_code + "_" + dataChild).data('padding') + 30);
                var drawing_no = "";
                var isStockableIcon = "";
                var isServiceIcon = "";
                var item_description = item.name;
                var is_service = '<input hidden="hidden" name="catmaterial_is_service" value='+item.is_service+' >';

                if (item.drawing_no !="" && item.drawing_no!=null) {
                    drawing_no = item.drawing_no;
                    item_description += item.drawing_no;
                }
                if (item.make_name != ""){
                    item_description += " [" + item.make_name + "]";
                }

                if(item.is_service == true){
                    isServiceIcon = `<span class="service-item-flag"></span>`;
                }
                if(!item.material_type && !item.is_service){
                    isStockableIcon = `<span class="non_stock-flag"></span>`;
                }
                if(item.hasChildren) {
                    childArrow = "<i class='fa fa-plus fa-for-arrow' role='button' onclick='return appendMaterial(\""+ item.item_id +"\", \""+ sessionStorage.clickcount +"\", \""+ dataParent +"\")'><a style='padding-left: 26px; display: block; margin-top: -12px;'>"+item_description+" "+isServiceIcon+" "+isStockableIcon+"</a></i>";
                } else {
                    childArrow = item_description +" "+isServiceIcon+" "+isStockableIcon;
                }
                if (item.makes.length > 0){
                    makes = "<select class='form-control' name='make' id='txtmake" + i + "'>";
                    for (j = 0; j <= item.makes.length - 1 ; j++) {
                        makes = `${makes}<option value="${item.makes[j].make_id}" data-quantity="${item.makes[j].stock}">${item.makes[j].make_name}</option>`;
                    }
                    makes += "</select>";
                }
                if(item.drawing_no != null){
                    var item_name = item.drawing_no + " - " + item.name
                }else{
                    var item_name = item.name
                }
                var is_service_value = item.is_service==true?1:0;
                // var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+dataParent+"' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" data-child=\""+ cat_code + "_" +dataChild +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                //          item_name + "</td><td class='text-left bom-sno'>" +
                //         current_bom_sno +"."+(i+1)+ "</td><td style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+childArrow+"</span></td><td>" +
                //         drawing_no + "</td><td>" +
                //         makes + "</td><td>" +
                //         "<input type='text' id='txtcatqty' name ='catmaterial' class='form-control text-right setUnitValue' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + item.quantity * catqty + "' data-unitvalue='"+item.quantity+"' /></td><td>" +
                //         item.unit_name + "</td><td class='text-center'>" +
                //         "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>" + "</td><td hidden=hidden>" +
                //         item.drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" + item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td></tr>";
                var row = "<tr data-toggle='close' data-padding='"+dataPadding+"' data-parent='"+dataParent+"' id=\""+ item.item_id + "_" + sessionStorage.clickcount +"\" data-child=\""+ cat_code + "_" +dataChild +"\" bgcolor='#ececec' border='1' align='left' style='font-size:12px; font-weight:normal;'><td hidden=hidden>" +
                        item_name + "</td><td class='text-left bom-sno'>" +
                       current_bom_sno +"."+(i+1)+ "</td><td style='padding-left:"+Number(dataPadding-20)+"px'><span style='padding-left:30px; display: block;' class='tree-view'>"+childArrow+"</span></td><td>" +
                       drawing_no + "</td><td hidden='hidden'>" +
                       makes + "</td><td>" +
                       "<input type='text' id='txtcatqty' name ='catmaterial' class='form-control text-right setUnitValue' maxlength='16' onfocus='setNumberRangeOnFocus(this,12,3)' value='" + item.quantity * catqty + "' data-unitvalue='"+item.quantity+"' /></td><td>" +
                       item.unit_name + "</td><td class='text-center'>" +
                       "<a href='#' onclick='deleteCatRow(this)'><i class='fa fa-trash-o'></i></a>" + "</td><td hidden=hidden>" +
                       item.drawing_no + "</td><td hidden=hidden><input type='text' name ='catmaterial_item_id' value='" + item.item_id + "' ></td><td hidden=hidden><input type='text' name ='catmaterial_is_service' value='" + is_service_value + "' ></td><td hidden=hidden><input type='text' name ='make_name' value='" + item.make_name + "' ></td></tr>";

               
                 constructedRow = constructedRow+row
                });
                $('#cattable #'+cat_code + "_" + dataChild).after(constructedRow);
                $("#"+currentCatChild).find('.setUnitValue').focus();
                setTimeout(function(){
                    $("#"+currentCatChild).find('.setUnitValue').blur();
                },500);

                $(".setUnitValue").blur(function(){
                    var setID = $(this).closest('tr').attr('id');
                    var setValue = $(this).val();
                    $('#cattable').find("tr[data-child=\""+setID+"\"]").each(function(){
                        var setUnitValue = $(this).find('.setUnitValue').data('unitvalue');
                        $(this).find('.setUnitValue').val(Number(setValue * setUnitValue));
                    });
                });
            }
        });
    }
}
function indentMaterialTableSerialNumberInit() {
    $("#indentMaterial tbody.item-for-goods tr:visible, #indentMaterial tbody.item-for-service tr:visible").each(function(i) {
       $(this).find(".s_no").text(i+1+".")
    })
}