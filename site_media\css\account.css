.header-box {
	border-radius: 4px;
	color: #FFF;
	padding: 1px;
}

.header-box .header-box-value {
	color: #FFF;
	padding: 25px 5px;
	text-align: center;
	font-size:26px;
	min-height: 92px;
}

.header-box .header-box-text {
	background: #fefefe;
	color: #347ab8;
	padding: 10px;
	text-align: center;
	border-radius: 0 0 4px 4px;
}

.header-box-1 {
	border: solid 1px #347ab8;
	background: #347ab8;
}

.header-box-1 .header-box-value {
	background: #347ab8;
}

.header-box-1 .header-box-text {
	color: #347ab8;
}

.header-box-2 {
	border: solid 1px #5db75d;
	background: #5db75d;
}

.header-box-2 .header-box-value {
	background: #5db75d;
}

.header-box-2 .header-box-text {
	color: #5db75d;
}

.header-box-3 {
	border: solid 1px #d9544f;
	background: #d9544f;
}

.header-box-3 .header-box-value {
	background: #d9544f;
}

.header-box-3 .header-box-text {
	color: #d9544f;
}

.header-box-4 {
	border: solid 1px #efad4d;
	background: #efad4d;
}

.header-box-4 .header-box-value {
	background: #efad4d;
}

.header-box-4 .header-box-text {
	color: #efad4d;
}

.header-box-5 {
	border: solid 1px #e6693e;
	background: #e6693e;
}

.header-box-5 .header-box-value {
	background: #e6693e;
}

.header-box-5 .header-box-text {
	color: #e6693e;
}


.invoice-box {
	border-radius: 6px;
	border: 1px solid #ccc;
}

.invoice-box.invoice {
	background: rgba(217, 84, 79,0.2);
}

.invoice-box.bills {
	background: rgba(93, 183, 93, 0.15)
}

.invoice-box-content {
	border-bottom: 1px solid #ccc;
    padding: 8px;
}

.box-clickable,
.box-clickable_2 {
	cursor: pointer;
}

.invoice-box-content:last-child {
	border-bottom: 0px solid #ccc;
}

.invoice-box .invoice-box-value {
	font-size: 18px;
	float: right;
	color: #50585c;
}

.invoice-box.bills .invoice-box-text {
	font-size: 14px;
	color: #5db75d;
}

.invoice-box.invoice .invoice-box-text {
	font-size: 14px;
	color: #d9544f;
}

.invoice-box .invoice-box-text.total {
	color: #5cc1bf;
	font-size: 18px;
}

.invoice-box .invoice-box-value.total {
	font-size: 22px;
	margin-top: -4px;
	/*color: #5cc1bf;*/
}

.invoice-box .invoice-box-text.overdue {
	font-size: 18px;
}

.invoice-box .invoice-box-value.overdue {
	font-size: 22px;
	color: #d9544f;
	margin-top: -4px;
}

.accounts-container {
	margin-top: 30px;
}


.accounts-container .account-box table{
	background: rgba(52, 122, 184, 0.2);
	margin-bottom: 0;
}

.account-box .account-header-text {
	font-size: 16px;
	color : #333;
}

.account-box .account-header-value {
	font-size: 20px;
	float: right;
	color : #333;
}

.account-box table th,
.account-box table td {
	border: solid 1px rgba(52, 122, 184, 0.3) !important;
	color : rgb(52, 122, 184);
	font-size: 14px;
}

.account-box table td.text-right,
.account-box table td.text-center {
	color: #50585c;
}

.account-box .account-table-header {
	background:#fff;
}

.account-box .account-table-header th{
	border: none !important;
}

.revenue-container {
	margin-top: 30px;
}

.revenue-container .revenue-box table {
	background: rgba(239, 173, 77,0.2);
	margin-bottom: 0;
}

.revenue-box .revenue-header-text {
	font-size: 16px;
	color : #333;
}

.revenue-box .revenue-header-value {
	font-size: 20px;
	float: right;
	color : #333;
}

.revenue-box table th,
.revenue-box table td {
	border: solid 1px rgba(52, 122, 184, 0.2) !important;
	color : #efad4d;
	font-size: 14px;
}

.revenue-box table td.text-right,
.revenue-box table td.text-center {
	color: #50585c;
}

.revenue-box .revenue-table-header {
	background:#fff;
}

.revenue-box .revenue-table-header th{
	border: none !important;
}

#reportrange .caret {
	float: right;
	margin-top: 8px;
}

.text-red {
	color: #d9544f;
}
