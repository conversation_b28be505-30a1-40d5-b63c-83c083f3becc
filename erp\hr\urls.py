"""
"""
from django.conf.urls import patterns, url

import erp.hr.hr_views
from erp.hr import json_api
from erp.hr.employee_views import manageEmployee, editEmployee, deleteEmployee, saveEmployee, saveDepartment, \
	load_pay_structure_items, load_emp_pay_items, checkEmployeeCode
from erp.hr.hr_views import dashboard, attendance
from erp.hr.pay_views import managePay, savePay, editPay, deletePay, checkPay, isValidPayStructureItem, \
	isValidPayStructureDescription

__author__ = 'saravanan'

urlpatterns = patterns(
	'',
	# HTML views
	url('home/$', dashboard),
	url('attendance/$', attendance),

	url('employee/$', manageEmployee),
	url('employee/save/$', saveEmployee),
	url('employee/edit/$', editEmployee),
	url('employee/delete/$', deleteEmployee),

	url('department/save/$', saveDepartment),

	url('pay/$', managePay),
	url('pay/save/$', savePay),
	url('pay/edit/$', editPay),
	url('pay/delete/$', deletePay),
	url('payslip/$', erp.hr.hr_views.generatePaySlip),

	# Json API
	url('json/employee_import/$', json_api.importEmployees),
	url('json/download_pay_structure/$', json_api.downloadPayStructure),
	url('json/employee/pay_structure_items/$', load_pay_structure_items),
	url('json/employee/employee_pay_items/$', load_emp_pay_items),
	url('json/employee/checkempcode/$', checkEmployeeCode),
	url('json/employee/pay_import/$', json_api.importEmployeePayStructure),

	url('json/pay/check/$', checkPay),
	url('json/pay/available_description/$', isValidPayStructureDescription),
	url('json/pay/validate_item/$', isValidPayStructureItem),

	# Import attendance sheet
	url('json/attendance_import/$', json_api.importAttendanceSheet),
	url('json/attendance_report/$', json_api.getAttendanceReport),
	url('json/payslip_email/$', json_api.emailPaySlip),
)
