from django import forms
from django.forms.formsets import formset_factory
from django.forms.utils import <PERSON><PERSON>r<PERSON><PERSON>, Error<PERSON>ist
import json

from erp import helper, logger
from erp.accounts import PURCHASE_ACCOUNT_GROUP_NAME
from erp.error_messages import LEDGER_NAME_NOT_UNIQUE, UNIT_CONVERSION_RATE_NEEDED, DUPLICATE_DRAWING_NO
from erp.models import Ledger, UserPermission, User, Invoice, AccountGroup, Material, OA, SalesEstimate
from settings import SQLASession
from util.widgets import CheckboxSelectMultipleRow, RadioSelectAsRow

__author__ = 'kalaivanan'


class StyleConstants(object):
	"""
	Style Constants that will be used in Form configurations
	"""
	DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'
	DATE_FORMAT = '%Y-%m-%d'
	PURPOSE_DATE_INPUT = 'purpose_txt_box required_date'
	DATE_INPUT = 'form-control datepicker'

	LOADED_DATE_INPUT = 'form-control datepicker loaded_datepicker hide'
	DATE_INPUT_PARTIAL = 'form-control single-datepicker-view-manual hide'
	SINGLE_DATE_INPUT_HIDDEN = 'form-control daterange-single hide'
	NORMAL_DATE_INPUT_HIDDEN = 'form-control hide'
	FROM_DATE_INPUT_HIDDEN = 'form-control fromdate hide'
	TO_DATE_INPUT_HIDDEN = 'form-control todate hide'
	DESCRIPTION_TEXT_AREA = 'form-control'
	ITEM_REMARKS_TEXT_AREA = 'form-control auto-expandable'
	MOVABLE_MULTISELECT = 'multiselect'
	PURPOSE_TEXT_BOX = 'purpose_txt_box'
	PURPOSE_TEXT_BOX_ALTER = 'purpose_txt_box_alter'
	NON_STOCK_TEXT_BOX = 'non_stock_txt_box'
	QTY_TEXT_BOX = 'qty-txtbox'
	TEXT_BOX = 'form-control'
	TEXT_BOX_CONVERSION_RATE = 'form-control txt_conversion_rate'
	HEADER_TEXT_BOX = 'header-textbox form-control'
	FLOATING_LABEL_TEXTBOX = 'form-control floating-input'
	FLOATING_LABEL_TEXTAREA = 'form-control floating-input'
	FLOATING_LABEL_SELECT = 'form-control floating-select'
	TEXTBOX_AS_TEXT = 'form-control textbox-as-text'
	TEXT_BOX_INVOICE = 'form-control validate_qty'
	TEXT_BOX_ICD_QTY = 'form-control validate-icd-qty'
	TEXT_BOX_ICD_RATE = 'form-control validate-icd-rate'
	TEXT_BOX_HIDDEN = 'form-control hide'
	TEXT_BOX_LABEL_PLAIN = 'text_box_label'
	TEXT_BOX_LABEL = 'form-control text_box_label'
	TEXT_BOX_UNIT_LABEL = 'inline-unit-display'
	TEXT_BOX_PRICE_WIDTH = 'form-control price_width'
	TEXT_BOX_LABEL_RIGHT = 'form-control text_box_label text-right'
	TEXT_BOX_UNIT_DISPLAY = 'input_unit_display'
	TEXT_BOX_FIXED_WIDTH_MEDIUM = 'form-control fixed-width-medium'
	UPPER_TEXT_BOX = 'form-control text-uppercase'
	UNIT_TEXT_BOX = 'form-control unit_box'
	UNIT_SELECT_BOX = 'form-control unit_select_box'
	CALCULATE_TEXT_BOX = 'form-control calculateAuditAmt'
	NEW_CALCULATE_TEXT_BOX = 'form-control newCalculateAuditAmt'
	TEXT_BOX_LEFT = 'form-control text-left'
	TEXT_BOX_LEFT_ICD = 'form-control text-left validate-icd'
	TEXT_BOX_RIGHT = 'form-control text-right'
	TEXT_BOX_CENTER = 'form-control text-center'
	DROP_BOX = 'form-control chosen-select'
	CURRENCY_DROP_BOX = 'form-control chosen-select currency_chosen_select'
	PROJECT_DROP_BOX = 'form-control chosen-select project_select_dropdown'
	LOCATION_DROP_BOX = 'form-control chosen-select location_select_dropdown'
	PARTY_DROP_BOX = 'form-control chosen-select party_select_dropdown'
	TEXT_BOX_SMALL = 'text_box_small'
	TEXT_BOX_12 = 'txtbox12'
	TEXT_BOX_21 = 'txtbox21'
	TEXT_BOX_15 = 'txtbox15'
	TEXT_BOX_14 = 'txtbox14'
	TEXT_BOX_17 = 'txtbox17'
	TEXT_BOX_FRM = "txtbox_frm"
	INV_TXT_BOX = "txtbox12 text_box"
	INV_TXT_BOX_FULL = "invoice_txt_box full_textbox"
	INV_TXT_BOX_LEFT = 'txtbox12 text_box text-left'
	INV_TXT_VAL = 'form-control text-right'
	TOTAL_AMT_TEXT_BOX = 'form-control text-right textbox-total-amt'
	BOOTSTRAP_FORM_CONTROL = 'form-control'
	LABEL_TEXT = 'unit_display unit_display-small pull-right'

	INV_SELECT_BOX = "select_box"
	MULTI_CHECK_BOX = "multi_check"
	STYLED_MULTI_CHECK_BOX = "multi_check styled-checkbox"
	STYLED_CHECK_BOX = "styled-checkbox"


class AttributeKeys(object):
	"""
	Key Constants to map Configurations in Form definitions
	"""
	CLASS = 'class'
	PLACE_HOLDER = 'placeholder'
	HIDDEN = 'hidden'
	VALUE = 'value'
	DISABLED = 'disabled'
	DEFAULT = 'default'
	MAXLENGTH = 'maxlength'
	ONBLUR = 'onblur'
	ONFOCUS = 'onfocus'
	READONLY = 'readonly'
	ONCHANGE = 'onChange'


class AccountGroupForm(forms.Form):
	"""
	AccountGroup UI object - pre-populated with existing AccountGroups as option-choice for Parent
	"""
	id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	name = forms.CharField(
		widget=forms.TextInput(attrs={
			AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '100',
			AttributeKeys.PLACE_HOLDER: 'Provide Account Head name',
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	parent_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
	description = forms.CharField(
		widget=forms.Textarea(attrs={
			AttributeKeys.CLASS: StyleConstants.TEXT_BOX,AttributeKeys.MAXLENGTH: '100',
			AttributeKeys.PLACE_HOLDER: 'Describe the Account Group here',
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	config_flags = forms.MultipleChoiceField(
		widget=CheckboxSelectMultipleRow(), choices=(
			(AccountGroup.ALLOW_SUBGROUP, 'Allow Subgroup creation'),
			(AccountGroup.ALLOW_LEDGER, 'Allow Ledger creation')), required=False)
	billable = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)


class AccountStatementInclusionsForm(forms.Form):
	"""
	Form to be displayed and submitted while generating Account Statements
	"""
	consumables_opening = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	consumables_closing = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	wip_opening = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	wip_closing = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	fg_opening = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	fg_closing = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)
	goods_opening = forms.FloatField(widget=forms.HiddenInput(), required=False, initial=0.0)
	goods_closing = forms.FloatField(widget=forms.HiddenInput(), required=False, initial=0.0)
	period_profit = forms.FloatField(widget=forms.HiddenInput(), required=False, initial=0.0)
	sales_tax = forms.FloatField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,3)'}), required=False, initial=0.0)


class MaterialForm(forms.Form):
	"""
	Form that collects info for the Material model. Field-names are tied with the Model field names.
	"""
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.HEADER_TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Material Name',
		AttributeKeys.MAXLENGTH: 200, 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	drawing_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Drawing Number', AttributeKeys.MAXLENGTH: '20',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphanumeric");', AttributeKeys.ONBLUR: 'constructDrawingNo(); validateStringOnBlur(this, event, "alphanumeric");'}), error_messages={'duplicate': DUPLICATE_DRAWING_NO}, required=False)
	description = forms.CharField(
		widget=forms.Textarea(attrs={"rows": "5", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: 2500,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	material_id = forms.IntegerField(widget=forms.HiddenInput(), initial=None, required=False)
	category_id = forms.IntegerField(widget=forms.HiddenInput(), initial=None, required=False)
	price = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter Price',
		AttributeKeys.MAXLENGTH: '19',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)'}))
	in_use = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	is_stocked = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	is_service = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	sample_size = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter Sample Size', 
		AttributeKeys.MAXLENGTH:'11',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,7,3,true)'},),
		initial=0, decimal_places=3, required=False)
	lot_size = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter Lot Size', 
		AttributeKeys.MAXLENGTH:'11',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,7,3,true)'},),
		initial=0, decimal_places=3, required=False)
	qc_method = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Quality Control Method',
		AttributeKeys.MAXLENGTH: 100, 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	reaction_plan = forms.CharField(widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA,
		AttributeKeys.PLACE_HOLDER:'Enter Reaction plan', AttributeKeys.MAXLENGTH: 450,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	# Field detatched from the Material object to capture the scale for unit change.
	unit_conversion_rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.VALUE: '1.0', 
		AttributeKeys.MAXLENGTH: "13",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)'}),
		required=False, error_messages={'conversion_scale_needed': UNIT_CONVERSION_RATE_NEEDED}, )
	# Place-holder field to collect the Opening-Stock
	opening_stock = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.VALUE: '0.00'}), required=False)
	# Tariff_No
	tariff_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Tariff No',
		AttributeKeys.MAXLENGTH: 20, 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)

	# Redundant field to hold the existing unit value to detect if the field is changed
	current_unit = forms.CharField(widget=forms.HiddenInput(), required=False)

	# Location
	location = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Rack',
		AttributeKeys.MAXLENGTH: 40, 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)

	remarks = forms.CharField(widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA,
		AttributeKeys.PLACE_HOLDER:'Enter Remarks', AttributeKeys.MAXLENGTH: 450,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)

	def is_valid(self):
		"""
		Overrides existing form validation to inculcate custom validation that includes:
		* Unit Conversion
		* Drawing_no duplication

		:return:
		"""
		name = self.data.get('%sname' % ("%s-" % self.prefix if self.prefix else ""))
		material_id = self.data.get('%smaterial_id' % ("%s-" % self.prefix if self.prefix else ""))
		enterprise_id = self.data.get('%senterprise_id' % ("%s-" % self.prefix if self.prefix else ""))
		old_unit = self.data.get("%scurrent_unit" % ("%s-" % self.prefix if self.prefix else ""))
		new_unit = self.data.get("%sunit_id" % ("%s-" % self.prefix if self.prefix else ""))
		conversion_rate = self.data.get("%sunit_conversion_rate" % ("%s-" % self.prefix if self.prefix else ""))

		is_valid = super(MaterialForm, self).is_valid()
		if (old_unit != 'None' and old_unit != new_unit) and (
				conversion_rate is None or ("%s" % conversion_rate).strip() == '' or conversion_rate == 0):
			error = self.fields['unit_conversion_rate'].error_messages['conversion_scale_needed']
			if 'unit_conversion_rate' not in self.errors or not self.errors['unit_conversion_rate']:
				self.errors['unit_conversion_rate'] = ErrorList()
			self.errors['unit_conversion_rate'].append(error)
			logger.warn('Save: Unit conversion rate not provided {enterprise_id: %s, name: %s}' % (enterprise_id, name))
			is_valid = False

		if (not material_id or material_id == '') and (
				name != '' and SQLASession().query(Material.name).filter(
			Material.name == name, Material.enterprise_id == enterprise_id).first()):
			logger.warn('Save: Duplicate material name {enterprise_id: %s, name: %s}' % (enterprise_id, name))
			if 'name' not in self.errors or not self.errors['name']:
				self.errors['name'] = ErrorList()
			self.errors['name'].append(self.fields['name'].error_messages['duplicate'])
			is_valid = False
		return is_valid

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		is_negative_stock_allowed = kwargs.pop('is_negative_stock_allowed', False)
		super(MaterialForm, self).__init__(*args, **kwargs)
		_unit_choices = helper.populateUnit(enterprise_id=enterprise_id)
		self.fields['standard_packing_qty'] = forms.FloatField(widget=forms.TextInput(attrs={
				AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 'autocomplete': 'off',
				AttributeKeys.PLACE_HOLDER: 'Enter Standard Packing Qty',
				AttributeKeys.VALUE: '',
				AttributeKeys.MAXLENGTH: '16',
				AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3); setCurrentValueToData(this)',
				AttributeKeys.ONCHANGE: 'validateMoqFactor(this, 1)'}), required=False)
		self.fields['unit_id'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Units'}),
			choices=_unit_choices)
		if is_negative_stock_allowed is True:
			self.fields['minimum_stock_level'] = forms.FloatField(widget=forms.TextInput(attrs={
				AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 'autocomplete': 'off',
				AttributeKeys.PLACE_HOLDER: 'Enter Minimum Stock Level', 
				AttributeKeys.MAXLENGTH: '16',
				AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3,false,true)'}))
		else:
			self.fields['minimum_stock_level'] = forms.FloatField(widget=forms.TextInput(attrs={
				AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 'autocomplete': 'off',
				AttributeKeys.PLACE_HOLDER: 'Enter Minimum Stock Level', 
				AttributeKeys.MAXLENGTH: '16',
				AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)'}))


class CatalogueMaterialForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the 'CatalogueMaterial' model
	"""
	drawing_no = forms.CharField(widget=forms.HiddenInput(), required=False)
	item_id = forms.CharField(widget=forms.HiddenInput())
	name = forms.CharField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.CharField(widget=forms.HiddenInput())
	quantity = forms.DecimalField(decimal_places=5, widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Quantity', 
		AttributeKeys.MAXLENGTH: '16',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)'}))
	material_select_field = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	units = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	# make_select_field = forms.CharField(widget=forms.TextInput(attrs={
	# 	AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	# make_id = forms.CharField(widget=forms.TextInput(attrs={
	# 	AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	# makes = forms.ChoiceField(widget=forms.Select(attrs={
	# 	AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 'multiple': ''}), required=False)
	is_stock = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	hasChildren = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	is_service = forms.CharField(widget=forms.HiddenInput(), required=False)


class MaterialAlternateUnitForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the 'Alternate Unit' model
	"""

	item_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.CharField(widget=forms.HiddenInput())
	scale_factor = forms.DecimalField(decimal_places=3, widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Scale Factor', 
		AttributeKeys.MAXLENGTH: '14',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,3)'}))
	unit_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXTBOX_AS_TEXT, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	primary_unit_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.LABEL_TEXT, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(MaterialAlternateUnitForm, self).__init__(*args, **kwargs)
		_unit_choices = helper.populateUnit(enterprise_id=enterprise_id)
		self.fields['alternate_unit_id'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Units'}),
			choices=_unit_choices)


class MaterialPartyProfileForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the 'SupplierWiseMaterial' model
	"""
	supp_id = forms.CharField(widget=forms.HiddenInput())
	item_id = forms.CharField(widget=forms.HiddenInput())
	enterprise_id = forms.CharField(widget=forms.HiddenInput())
	alternate_unit_id = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.VALUE: '0'}), required=False)
	unit_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_UNIT_DISPLAY,
		AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	price = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_PRICE_WIDTH,
		AttributeKeys.PLACE_HOLDER: 'Enter Price',
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)',
		AttributeKeys.VALUE: '0.00'}))
	moq = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_PRICE_WIDTH,
		AttributeKeys.PLACE_HOLDER: 'Enter MOQ',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		AttributeKeys.ONCHANGE: 'validateInlineMoq(this)',
		AttributeKeys.VALUE: '0.00'}), required=False)
	lead_time = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Enter Lead Time',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.VALUE: '0'}), required=False)
	supplier_select_field = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	remarks = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Remarks', AttributeKeys.MAXLENGTH: "300"}), required=False)
	effect_since = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.LOADED_DATE_INPUT}, format=StyleConstants.DATE_FORMAT))
	effect_till = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.LOADED_DATE_INPUT}, format=StyleConstants.DATE_FORMAT),
		required=False, initial=None)
	is_primary = forms.BooleanField(widget=forms.CheckboxInput, required=False)
	is_service = forms.BooleanField(widget=forms.CheckboxInput, required=False)
	currency_id = forms.ChoiceField(required=True, choices=helper.populateCurrencyChoices(), widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}))
	currency_select_field = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_UNIT_DISPLAY,
		AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False)
	make_id = forms.IntegerField(widget=forms.HiddenInput, required=False, initial=0)
	make_select_field = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}),
		required=False)
	status = forms.ChoiceField(
		widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DEFAULT: "-1"}),
		choices=helper.populatePriceApprovalChoices(), required=False)
	reject_remarks = forms.CharField(required=False, widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Rejection Remarks', AttributeKeys.MAXLENGTH: "160"}))
	is_mask_price = forms.IntegerField(widget=forms.HiddenInput, required=False, initial=0)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(MaterialPartyProfileForm, self).__init__(*args, **kwargs)
		self.fields['make_choices'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}),
			choices=helper.populateMakes(enterprise_id=enterprise_id), required=False)


class EmployeeForm(forms.Form):
	"""
	VO Object to present the basic Employee details
	"""
	EMPLOYMENT_TYPE = helper.populateEmploymentType()
	EMPLOYEE_STATUS = helper.populateEmployeeStatus()
	EMPLOYEE_GENDER = helper.populateEmployeeGender()
	EMPLOYEE_MARTIAL_STATUS = helper.populateEmployeeMartialStatus()
	ACCOUNT_TYPE = helper.populateAccountType()
	EMPLOYEE_BLOOD_GROUP = helper.populateEmployeeBloodGroup()

	emp_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	emp_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: '10', AttributeKeys.PLACE_HOLDER: 'Enter Code',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	first_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: '50', AttributeKeys.PLACE_HOLDER: 'Enter First Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");' }))
	address1 = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '100',
		AttributeKeys.PLACE_HOLDER: 'Enter Address1',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	address2 = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '100',
		AttributeKeys.PLACE_HOLDER: 'Enter Address2',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	city = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '50',
		AttributeKeys.PLACE_HOLDER: 'Enter City',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	state = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '50',
		AttributeKeys.PLACE_HOLDER: 'Enter State',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	phone_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: '30', AttributeKeys.PLACE_HOLDER: 'Enter Mobile Number',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "ph_number");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "ph_number");'}), required=False)
	email = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '75',
		AttributeKeys.PLACE_HOLDER: 'Enter Email',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	last_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '50',
		AttributeKeys.PLACE_HOLDER: 'Enter Last Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	employment_type = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Employment Type'}),
		choices=EMPLOYMENT_TYPE)
	status = forms.ChoiceField(widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Status'}),
		choices=EMPLOYEE_STATUS)
	designation = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '50',
		AttributeKeys.PLACE_HOLDER: 'Enter Designation',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	date_of_joining = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT),
		required=False)
	date_of_birth = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT),
		required=False)
	gender = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Gender'}),
		choices=EMPLOYEE_GENDER, required=False)
	country = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Country',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	postal_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Enter Postal Code',
		AttributeKeys.MAXLENGTH: '6',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,6,0,true)'}), 
		required=False)
	nationality = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Nationality',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	martial_status = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Status'}),
		choices=EMPLOYEE_MARTIAL_STATUS)
	aadhar_number = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter Aadhaar No.',
		AttributeKeys.MAXLENGTH: '12',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,0,true)'}), 
		required=False)
	pan_number = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.UPPER_TEXT_BOX, AttributeKeys.MAXLENGTH: '12',
		AttributeKeys.PLACE_HOLDER: 'Enter PAN No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphanumeric");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphanumeric");'}), required=False)
	account_number = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Account No', 
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphanumeric");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphanumeric");'}), required=False)
	account_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Account Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	account_type = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Status'}),
		choices=ACCOUNT_TYPE)
	ifsc_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter IFSC Code',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	esi_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter ESI No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	pf_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.UPPER_TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter PF No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	no_of_el = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.VALUE: '0',		
		AttributeKeys.PLACE_HOLDER: 'Enter No. of Days',
		AttributeKeys.MAXLENGTH: '3',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,0)'}), 
		required=False)
	no_of_cl = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.VALUE: '0',
		AttributeKeys.PLACE_HOLDER: 'Enter No. of Days',
		AttributeKeys.MAXLENGTH: '3',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,0)'}), 
		required=False)
	place_of_work = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Place of Work',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	father_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Father Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	mother_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '45',
		AttributeKeys.PLACE_HOLDER: 'Enter Mother Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	contact_number = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '30',
		AttributeKeys.PLACE_HOLDER: 'Enter Contact Number',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "ph_number");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "ph_number");'}), required=False)
	spouse_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '50',
		AttributeKeys.PLACE_HOLDER: 'Enter Spouse Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	blood_group = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Select Blood Group'}),
		choices=EMPLOYEE_BLOOD_GROUP, required=False)

	profile_pic = forms.CharField(widget=forms.HiddenInput(), required=False)
	profile_pic_filename = forms.CharField(widget=forms.HiddenInput(), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(EmployeeForm, self).__init__(*args, **kwargs)
		self.fields['enterprise_id'] = forms.IntegerField(
			widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=enterprise_id)
		self.fields['department_id'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL}),
			choices=helper.populateDepartment(enterprise_id=enterprise_id, with_new_department=True), required=False)
		self.fields['pay_structure_id'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL}),
			choices=helper.populatePayStructure(enterprise_id=enterprise_id), required=False)


class EnterpriseRegistrationForm(forms.Form):
	"""
	VO for enterprise registration details
	"""
	firstname = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: '30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	lastname = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: '30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: '50',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	email = forms.EmailField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: '75',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'validateEmailDuplication(this); return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	password = forms.CharField(widget=forms.PasswordInput(
		attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: '16', 'autocomplete': 'new-password',
			   'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'	},
		render_value=True), required=False)
	mobile = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH: "10",
	'onKeyPress': 'return validateStringOnKeyPress(this,event, "number");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "number");'}), required=False)


class EnterpriseForm(forms.Form):
	"""
	VO for enterprise details
	"""
	id = forms.IntegerField(widget=forms.HiddenInput())
	code = forms.CharField(widget=forms.HiddenInput())
	name = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'50',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	address_1 = forms.CharField(widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTAREA, AttributeKeys.MAXLENGTH: '300', AttributeKeys.PLACE_HOLDER: " ",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	address_2 = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'90',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	city = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'50',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}))
	country_code = forms.CharField(widget=forms.HiddenInput(), required=False)
	state = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'50',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}))
	pin_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'10',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,0,true)'}))
	phone = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "ph_number");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "ph_number");'}))
	fax = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "ph_number");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "ph_number");'}), required=False)
	email = forms.EmailField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'75',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	contact_person = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'50',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	cst_no = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	gst_no = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	tin = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	tan = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	ecc_no = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	cin = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	pan = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'28',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	fy_start_day = forms.ChoiceField(choices=helper.populateFiscalYear(), widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_SELECT, AttributeKeys.PLACE_HOLDER: " "}))
	home_currency_id = forms.ChoiceField(choices=helper.populateCurrencyChoices(), widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_SELECT, AttributeKeys.PLACE_HOLDER: " "}))
	excise_range = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'145',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	excise_division = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'145',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	excise_commissionerate = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'145',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	pla_no = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'40',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	service_tax_no = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: " ", AttributeKeys.MAXLENGTH:'40',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialCharMini");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialCharMini");'}), required=False)
	logo = forms.CharField(widget=forms.HiddenInput(), required=False)
	purchase = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	sales = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	setting_flags = forms.MultipleChoiceField(
		widget=CheckboxSelectMultipleRow(attrs={AttributeKeys.CLASS: StyleConstants.MULTI_CHECK_BOX}),
		choices=((1, 'Indent'), (2, 'Internal Control'), (4, 'Ignore Credit Note for Purchase'),
		         (8, 'Generate Debit & Credit Notes for Purchase Automatically'),
		         (16, 'Enable scrutiny of auto-generated Vouchers')), required=False)
	is_negative_stock_allowed = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	is_gate_inward_no_mandatory = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	gate_inward_no_flags = forms.MultipleChoiceField(
		widget=CheckboxSelectMultipleRow(attrs={AttributeKeys.CLASS: StyleConstants.MULTI_CHECK_BOX}),
		choices=((1, 'Mandatory'), (2, 'Unique in a Fiscal Year'), (4, 'Automated'), (8, 'Editable')), required=False)
	is_purchase_order_mandatory = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	is_blanket_po = forms.BooleanField(widget=forms.CheckboxInput(), required=False)
	is_icd_request_acknowledgement = forms.BooleanField(widget=forms.CheckboxInput(), required=False)
	is_delivery_schedule = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	is_multiple_units = forms.BooleanField(widget=forms.CheckboxInput(), initial=True, required=False)
	po_doc_reg_items = forms.CharField(widget=forms.HiddenInput(), required=False)
	se_doc_reg_items = forms.CharField(widget=forms.HiddenInput(), required=False)
	is_price_modification_disabled = forms.BooleanField(widget=forms.CheckboxInput(), required=False)
	is_price_modification_disabled_quick_po = forms.BooleanField(widget=forms.CheckboxInput(), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(EnterpriseForm, self).__init__(*args, **kwargs)
		self.fields['claim_heads'] = forms.MultipleChoiceField(
			choices=helper.populateClaimHeadChoices(enterprise_id=enterprise_id),
			widget=forms.SelectMultiple(attrs={AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA}),
			required=False)
		self.fields['expense_heads'] = forms.MultipleChoiceField(
			choices=helper.populateExpenseHeadChoices(enterprise_id=enterprise_id),
			widget=forms.SelectMultiple(attrs={AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA}),
			required=False)


class IndentForm(forms.Form):
	"""

	"""
	indent_no = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Indent No'}), required=False)
	indent_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	indent_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	project_name = forms.ChoiceField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	purpose = forms.CharField(widget=forms.TextInput(attrs={
		"autocomplete": "off", "list": "frequents_purpose_list", AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Purpose', AttributeKeys.MAXLENGTH: '100',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	raised_by = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	raised_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.DATE_INPUT}, format=StyleConstants.DATETIME_FORMAT), required=False)
	modified_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.DATE_INPUT}, format=StyleConstants.DATETIME_FORMAT), required=False)
	modified_by = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	expected_date = forms.DateTimeField(required=False, widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT))
	instructions = forms.CharField(required=False, widget=forms.Textarea(attrs={
		"rows": "5", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	request_qty = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	purchase_qty = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	balance_qty = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False, initial=0.0)
	received_qty = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	financial_year = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	is_stockable = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	modifier_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)

	def __repr__(self):
		return self.as_p()

	def is_valid(self):
		is_valid = super(IndentForm, self).is_valid()
		balance = self.data.get("%sbalance_qty" % ("%s-" % self.prefix if self.prefix else ""))
		if balance and balance < 0:
			negative_balance_error = ErrorDict()
			negative_balance_error['balance_qty'] = "Indent quantity for a material is less than the raised PO quantity"
			try:
				self.errors['balance_qty'].append(negative_balance_error['balance_qty'])
			except KeyError:
				self.errors['balance_qty'] = {negative_balance_error['balance_qty']}
			logger.debug('Form errors - %s' % self.errors)
			return False
		return is_valid

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		indent_type_choices = helper.populateAccountLedgerChoices(enterprise_id=enterprise_id, group_names=(
			PURCHASE_ACCOUNT_GROUP_NAME,))
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, module_table_name='indents',
			frequents_condition="" )
		super(IndentForm, self).__init__(*args, **kwargs)
		self.fields['project_code'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX, AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}),
			choices=project_choices)
		self.fields['purchase_account_id'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}),
			choices=indent_type_choices)


class IndentMaterialForm(forms.Form):
	"""

	"""
	indent_no = forms.IntegerField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Quantity',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)'}))
	item_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}, ))
	drawing_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}, ), required=False)
	description = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}, ))
	make_label = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}, ),
		required=False)
	po_qty = forms.FloatField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}, ), required=False)
	units = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	make_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	alternate_unit_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	scale_factor = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False, initial='1.00')
	is_service = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: 'is_service', AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(IndentMaterialForm, self).__init__(*args, **kwargs)
		self.fields['makes'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}),
			required=False, choices=helper.populateMakes(enterprise_id=enterprise_id))
		self.fields['alternate_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_SELECT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)
		self.fields['all_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_SELECT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)


class SubTaxForm(forms.Form):
	"""
	VO-cum-UI form object that holds the SubTax object
	"""
	parent_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Parent Code',
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Name',AttributeKeys.MAXLENGTH: '100'}), required=True)
	rate = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Rate'}), required=True)


class TaxForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Tax object

	"""
	TYPE_CHOICES = helper.populateTaxType()

	id = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Tax Code', AttributeKeys.MAXLENGTH: '20',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphanumeric_with");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphanumeric_with");'}))
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Name of the Tax', AttributeKeys.MAXLENGTH: '100',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	base_rate = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.VALUE: '0.00'}), required=False)
	is_compound = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	net_rate = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	assess_rate = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.VALUE: '0.00',
		AttributeKeys.MAXLENGTH: "7",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,3)'}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	type = forms.ChoiceField(required=True, choices=TYPE_CHOICES, widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))


class LedgerForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Ledger object
	"""
	id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_15}), required=False)
	group_label = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL,
		AttributeKeys.DISABLED: AttributeKeys.DISABLED,
		AttributeKeys.PLACE_HOLDER: 'Select an Account Group'}), required=False)
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL, AttributeKeys.PLACE_HOLDER: 'Enter Name',
		AttributeKeys.MAXLENGTH: "100",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	group_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)
	description = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL, AttributeKeys.PLACE_HOLDER: 'Enter Description', AttributeKeys.MAXLENGTH: "140",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	is_opening_debit = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	opening_balance = forms.CharField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.PURPOSE_TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	opening_credit = forms.CharField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.PURPOSE_TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	opening_debit = forms.CharField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.PURPOSE_TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	opening_as_on = forms.DateField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	closing_credit = forms.CharField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.PURPOSE_TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	closing_debit = forms.CharField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.PURPOSE_TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	closing_as_on = forms.DateField(required=False, widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}, format=StyleConstants.DATE_FORMAT))
	total_credit = forms.CharField(widget=forms.HiddenInput(
		attrs={AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False, initial='0.00')
	total_debit = forms.CharField(widget=forms.HiddenInput(
		attrs={AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False, initial='0.00')
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	approved_only = forms.BooleanField(widget=forms.CheckboxInput(), required=False)
	is_system_ledger = forms.BooleanField(
		widget=forms.CheckboxInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	billable = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)

	def is_valid(self):
		is_valid = super(LedgerForm, self).is_valid()
		name = self.data.get("%sname" % ("%s-" % self.prefix if self.prefix else ""))
		ledger_id = self.data.get("%sid" % ("%s-" % self.prefix if self.prefix else ""))
		enterprise_id = self.data.get("%senterprise_id" % ("%s-" % self.prefix if self.prefix else ""))
		group_id = self.data.get("%sgroup_id" % ("%s-" % self.prefix if self.prefix else ""))
		if (not ledger_id or ledger_id == '') and (name != '') and SQLASession().query(Ledger.name).filter(
				Ledger.name == name, Ledger.enterprise_id == enterprise_id, Ledger.group_id == group_id).first():
			logger.info('Save: Duplicate Ledger name {enterprise_id: %s, name: %s}' % (enterprise_id, name))
			if 'name' not in self.errors or not self.errors['name']:
				self.errors['name'] = ErrorList()
			self.errors['name'].append(LEDGER_NAME_NOT_UNIQUE)
			is_valid = False
		logger.info('Form errors - %s' % self.errors)
		return is_valid


class VoucherForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Voucher object
	"""
	VOUCHER_TYPE_CHOICES = helper.populateVoucherType(exclude_none=False)

	id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	type_id = forms.ChoiceField(required=False, choices=VOUCHER_TYPE_CHOICES, widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	voucher_no = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DEFAULT: '0'}), required=False)
	code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DEFAULT: '0'}), required=False)
	voucher_date = forms.DateTimeField(
		widget=forms.DateInput(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}),
		required=False)
	narration = forms.CharField(widget=forms.Textarea(attrs={
		"rows": "5", AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Enter Narration', AttributeKeys.MAXLENGTH: "500",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	dr_amount = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: "18",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)'}),
		required=False)
	cr_amount = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: "18",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)'}),
		required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	transaction_instrument_no = forms.CharField(widget=forms.TextInput(attrs={
		"data-enterkey-submit": "save_voucher_button", AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'DD/Cheque/NEFT', AttributeKeys.MAXLENGTH: '30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	transaction_description = forms.CharField(widget=forms.TextInput(attrs={
		"data-enterkey-submit": "save_voucher_button", AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Enter Instrument Description', AttributeKeys.MAXLENGTH: '100',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	status = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=1,
		required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, module_table_name='voucher',
			frequents_condition="" )
		super(VoucherForm, self).__init__(*args, **kwargs)
		self.fields['project_code'] = forms.ChoiceField(required=True, choices=project_choices, widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX, AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}))


class VoucherParticularForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Voucher object
	"""
	item_no = forms.IntegerField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, AttributeKeys.VALUE: 0}),
		required=False)
	voucher_id = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	ledger_id = forms.CharField(required=True, widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'ledger_select')}))
	ledger_label = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'ledger_select_label')}))
	ledger_group = forms.CharField(required=False, show_hidden_initial=True, widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'ledger_select_group')}))
	is_debit = forms.ChoiceField(required=True, choices=helper.populateDrCr(), widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	amount = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.MAXLENGTH: '18',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)'}),
		decimal_places=2)
	ledger_opening = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	billable = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	bills = forms.CharField(widget=forms.HiddenInput, required=False)


class LedgerBillForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Voucher object
	"""
	bill_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, AttributeKeys.VALUE: 0}), required=False)
	bill_no = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.VALUE: 0, 'data-enterkey-submit': 'save_button', AttributeKeys.MAXLENGTH: '45',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	bill_date = forms.CharField(
		widget=forms.TextInput(attrs={"readonly": "", AttributeKeys.CLASS: StyleConstants.DATE_INPUT_PARTIAL}))
	ledger_id = forms.CharField(required=True, widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	is_debit = forms.ChoiceField(required=True, choices=helper.populateDrCr(), widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	is_debit_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	net_value = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	debit_settlement = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL, 
		AttributeKeys.VALUE: 0, 
		AttributeKeys.MAXLENGTH: '18',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)',
		AttributeKeys.ONBLUR: 'calcLedgerTotal();',
		'data-enterkey-submit': 'save_button'}),
		required=False)
	credit_settlement = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL, 
		AttributeKeys.VALUE: 0, 
		AttributeKeys.MAXLENGTH: '18',
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)',
		AttributeKeys.ONBLUR: 'calcLedgerTotal();',
		'data-enterkey-submit': 'save_button'}), 
		required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	voucher_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)


class UserPermissionForm(forms.Form):
	"""
	"""
	# Deliberately left the labels blank, to avoid cluttered rendering of the checkbox group
	_VIEW_FLAG_LABEL = ''
	_EDIT_FLAG_LABEL = ''
	_DELETE_FLAG_LABEL = ''
	_APPROVE_FLAG_LABEL = ''
	_NOTIFY_FLAG_LABEL = ''

	user_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.QTY_TEXT_BOX}), required=False)
	module_code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.QTY_TEXT_BOX}), required=False)
	bit_flags = forms.MultipleChoiceField(widget=CheckboxSelectMultipleRow(
		attrs={AttributeKeys.CLASS: StyleConstants.STYLED_MULTI_CHECK_BOX}),
		choices=(
			(UserPermission._VIEW, _VIEW_FLAG_LABEL), (UserPermission._EDIT, _EDIT_FLAG_LABEL),
			(UserPermission._APPROVE, _APPROVE_FLAG_LABEL), (UserPermission._NOTIFY, _NOTIFY_FLAG_LABEL)),
		required=False)
	enterprise_id = forms.CharField(widget=forms.HiddenInput())
	landing_url = forms.CharField(widget=forms.HiddenInput(), required=False)


class UserForm(forms.Form):
	"""
	Form to render the User model's HTML form.
	"""
	id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	username = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Nickname', AttributeKeys.MAXLENGTH: '25',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		       AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");' }), required=False)
	first_name = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: ' ', AttributeKeys.MAXLENGTH: '30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");' }),
		required=False)
	last_name = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: ' ', AttributeKeys.MAXLENGTH: '30',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");' }),
		required=False)
	email = forms.EmailField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.FLOATING_LABEL_TEXTBOX, AttributeKeys.PLACE_HOLDER: ' ', AttributeKeys.MAXLENGTH: '75',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");' }))
	is_super = forms.BooleanField(
		widget=forms.CheckboxInput(attrs={}), initial=False,
		required=False)
	is_active = forms.BooleanField(widget=forms.CheckboxInput(), initial=True)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	landing_url = forms.CharField(widget=forms.HiddenInput(), required=False)
	profile_img = forms.CharField(widget=forms.HiddenInput(), required=False)
	signature = forms.CharField(widget=forms.HiddenInput(), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		super(UserForm, self).__init__(*args, **kwargs)
		self.fields['claim_heads'] = forms.MultipleChoiceField(
			choices=helper.populateEnterpriseClaimHeadChoices(enterprise_id=enterprise_id),
			widget=forms.SelectMultiple(attrs={AttributeKeys.CLASS: StyleConstants.MOVABLE_MULTISELECT}),
			required=False)
		self.fields['expense_heads'] = forms.MultipleChoiceField(
			choices=helper.populateEnterpriseExpenseHeadChoices(enterprise_id=enterprise_id),
			widget=forms.SelectMultiple(attrs={AttributeKeys.CLASS: StyleConstants.MOVABLE_MULTISELECT}),
			required=False)

	def clean(self):
		"""

		:return:
		"""
		cleaned_data = super(UserForm, self).clean()
		logger.info('Validating User Form...')
		email = cleaned_data.get("email")
		existing_email = SQLASession().query(User.email).filter(User.email == email).first()
		logger.info('Current User Id: %s' % cleaned_data.get("id"))
		if cleaned_data.get("id") is None and existing_email:
			logger.info('Duplicate Email for User')
			raise forms.ValidationError(
				{'email': 'Login ID (Email) already taken. Please change the Email and try again.'})
		return cleaned_data


class InvoiceForm(forms.Form):
	id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	invoice_no = forms.FloatField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	current_user = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	currency_id = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.CURRENCY_DROP_BOX}), required=True, choices=helper.populateCurrencyChoices())
	currency_conversion_rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_CONVERSION_RATE,
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)'}), 
		required=False, initial=1.00)
	po_date = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT),
		required=False)
	po_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "50",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	se_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	se_date = forms.DateField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	order_accept_date = forms.CharField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	order_accept_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	transport_mode = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "50", AttributeKeys.PLACE_HOLDER: 'Enter Transport Mode',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	lr_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45", AttributeKeys.PLACE_HOLDER: 'Enter LR No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	road_permit_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45", AttributeKeys.PLACE_HOLDER: 'Enter Permit No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	packing_slip_no = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45", AttributeKeys.PLACE_HOLDER: 'Enter Packing Slip No',
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	packing_description = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "100", AttributeKeys.PLACE_HOLDER: 'Enter Packing Description',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	remarks = forms.CharField(required=False, widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	notes = forms.CharField(required=False, widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");', 'readonly':'readonly'}))
	payment_terms = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: '150',
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	special_instruction = forms.CharField(widget=forms.Textarea(attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	financial_year = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	status = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
	approved_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	issued_on = forms.DateTimeField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	grand_total = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False, initial='0.00')
	deliver_to = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300'}))
	gstin = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Ship to GSTIN', AttributeKeys.MAXLENGTH: '30'}), required=False)
	ship_to_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Ship to Name', AttributeKeys.MAXLENGTH: '100'}), required=False)
	ecommerce_gstin = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphanumeric");',
		AttributeKeys.ONBLUR: 'validateStringOnBlur(this, event, "alphanumeric");',
		AttributeKeys.PLACE_HOLDER: 'E-Commerce GSTIN', AttributeKeys.MAXLENGTH: '15'}), required=False)
	round_off = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL, 
		AttributeKeys.PLACE_HOLDER: '0.00', 
		AttributeKeys.MAXLENGTH: "8",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,5,2,false,true)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal(this)'}),
		initial='0.00', required=False)
	return_date = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.NORMAL_DATE_INPUT_HIDDEN}, format=StyleConstants.DATE_FORMAT),
		required=False)
	issued_to = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	issued_for = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	super_edit = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)
	tax_payable_on_reverse_charge = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	goods_already_supplied = forms.BooleanField(widget=forms.CheckboxInput(), initial=False, required=False)
	dc_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	project_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	is_courier = forms.BooleanField(widget=forms.CheckboxInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False, initial=False)
	no_of_consignment = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'No of Consignment',
		AttributeKeys.MAXLENGTH: '10', AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,0,true)'}), initial='0', required=False)
	weight = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL,
		AttributeKeys.PLACE_HOLDER: '0.000',
		AttributeKeys.MAXLENGTH: "10",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,5,3,false,true)'	}),
		initial='0.000', required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		po_party_id = kwargs.pop("po_party_id", None)
		type_group = kwargs.pop("type", "internal")
		invoice_id = kwargs.pop("invoice_id", None)
		is_dc_edit = kwargs.pop("is_dc_edit", None)
		current_user = kwargs.pop("current_user", None)
		data = kwargs['data'] if 'data' in kwargs else None
		po_id = None
		# if data:
		# 	data_dict = dict(data.lists())
		# 	po_id = int(data_dict['invoice-job_po_id'][0])
		issue_types_text = "'Others'"
		for issue_type in Invoice.TYPES[type_group]:
			issue_types_text = "'%s', %s" % (issue_type, issue_types_text)
		party_choices = helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=False, assort_frequent_choices=True,
			module_table_name='invoice', frequents_condition="")
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, module_table_name='invoice',
			frequents_condition="AND type IN (%s)" % issue_types_text, is_child=True)
		super(InvoiceForm, self).__init__(*args, **kwargs)
		self.fields['party_id'] = forms.ChoiceField(required=False, choices=party_choices,
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PARTY_DROP_BOX}))
		self.fields['project_code'] = forms.ChoiceField(required=False, choices=project_choices, widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX,
				   AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}))
		self.fields['location_id'] = forms.ChoiceField(required=True,
			  choices=helper.populateLocationChoices(enterprise_id=enterprise_id, user_id=current_user),
			  widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.LOCATION_DROP_BOX}))
		self.fields['type'] = forms.ChoiceField(choices=Invoice.TYPE_CHOICES[type_group], widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=True)
		if type_group == "sales":
			self.fields['sale_account_id'] = forms.ChoiceField(
				required=False, choices=helper.populateSaleAccountChoices(enterprise_id=enterprise_id),
				widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))
		elif type_group == "dc":
			self.fields['job_po_id'] = forms.ChoiceField(
				required=False, choices=helper.populateJOBPOChoices(
					enterprise_id=enterprise_id, party_id=po_party_id, order_type=1, invoice_id=invoice_id, po_id=po_id, is_dc_edit=is_dc_edit),
				widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}), initial=0)
		elif type_group == "internal":
			self.fields['job_po_id'] = forms.ChoiceField(
				required=False, choices=helper.populateJOBPOChoices(
					enterprise_id=enterprise_id, party_id=po_party_id, order_type=2, invoice_id=invoice_id, po_id=po_id),
				widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}), initial=0)


class ItemTaxForm(forms.Form):
	tax_code = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "tax_code"}), required=False)
	tax_type = forms.CharField(widget=forms.HiddenInput(), required=False)
	rate = forms.FloatField(widget=forms.HiddenInput(), required=False, initial='0.00')
	amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False, initial='0.00')


ItemTaxFormset = formset_factory(form=ItemTaxForm, can_delete=True, extra=0, max_num=3)


class InvoiceMaterialsForm(forms.Form):
	invoice_id = forms.IntegerField(required=False, widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	item_id = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, AttributeKeys.CLASS: 'item_id'}))
	item_code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, AttributeKeys.CLASS: 'item_code'}), required=False)
	hsn_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'HSN/SAC',AttributeKeys.ONCHANGE: 'validateHsnWithSuggestion(this)',
		AttributeKeys.MAXLENGTH: '10', 'onKeyPress': 'validateStringOnKeyPress(this,event, "hsn_specialChar");',
		'onblur': 'return validateStringOnBlur(this, event, "alphaSpecialChar");',
		'class': 'form-control inv_hsn_code text-left'}), required=False)
	item_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Item Code & Name',
		'readonly': 'readonly'}), required=False)
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_INVOICE, 
		AttributeKeys.PLACE_HOLDER: 'Quantity',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal()'}), initial='0.00')
	received_qty = forms.IntegerField(required=False, widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, AttributeKeys.DISABLED: AttributeKeys.DISABLED}))
	unit_id = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_UNIT_LABEL, AttributeKeys.DISABLED: AttributeKeys.DISABLED}),
		required=False)
	rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Unit Rate',
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal()'}), initial='0.00')
	discount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: '0.00', 
		AttributeKeys.MAXLENGTH: "6",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,2)',
		AttributeKeys.ONBLUR: 'validatePercentage(this, event); calculateGrandTotal();'}),
		required=False, initial='0.00')
	amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL, AttributeKeys.PLACE_HOLDER: 'Net Value',
		AttributeKeys.DISABLED: AttributeKeys.DISABLED}), required=False, initial='0.00')
	remarks = forms.CharField(widget=forms.Textarea(attrs={
		"rows": "1", AttributeKeys.CLASS: StyleConstants.ITEM_REMARKS_TEXT_AREA, AttributeKeys.PLACE_HOLDER: 'Enter Item Remarks',
		AttributeKeys.MAXLENGTH: "300", 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	item_tax = forms.FloatField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, 'name': 'item_tax'}), required=False)
	make_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX + ' make_id', AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	taxes = ItemTaxFormset(prefix="inv_mat_tax___prefix__", initial=[
		{"tax_code": "", "tax_type": "CGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "SGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "IGST", "rate": "0.00", "amount": "0.00"}])
	is_faulty = forms.BooleanField(widget=forms.HiddenInput(
		attrs={AttributeKeys.CLASS:'is_faulty'}), required=False, initial=False)
	oa_no = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	oa_code = forms.CharField(required=False, widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL_PLAIN, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	dc_no = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	dc_code = forms.CharField(required=False, widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL_PLAIN, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	is_returnable = forms.BooleanField(widget=forms.CheckboxInput(), required=False, initial=False)
	entry_order = forms.IntegerField(widget=forms.HiddenInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_CENTER}), required=False)
	grn_no = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	grn_code = forms.CharField(required=False, widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL_PLAIN, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	alternate_unit_id = forms.IntegerField(widget=forms.TextInput(attrs={
			AttributeKeys.CLASS: StyleConstants.TEXT_BOX + ' alternate_unit_id',
			AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	scale_factor = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False, initial='1.00')
	alternate_unit_list = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	material_type = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	inspection_log = forms.CharField(
		widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "quantity_inspection_review"}), required=False, initial='[]')
	is_service = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: 'is_service', AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)

	def is_valid(self):
		is_valid = super(InvoiceMaterialsForm, self).is_valid() and self.taxes.is_valid()
		logger.info("Invoice Material & Taxes Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(InvoiceMaterialsForm, self).__init__(*args, **kwargs)
		self.fields['makes'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}),
			choices=helper.populateMakes(enterprise_id=enterprise_id), required=False)
		self.fields['alternate_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_SELECT_BOX, AttributeKeys.PLACE_HOLDER: 'Units', AttributeKeys.ONCHANGE: "calculateStockValue(this)"}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)
		self.fields['all_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_SELECT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)


class InvoiceChargeForm(forms.Form):
	invoice_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	item_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT,
		AttributeKeys.PLACE_HOLDER: 'Items not profiled against any Code'}), required=False)
	hsn_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT,
		AttributeKeys.PLACE_HOLDER: 'HSN/SAC',
		AttributeKeys.MAXLENGTH: '9',
		'onKeyPress': 'validateStringOnKeyPress(this,event, "hsn_specialChar");',
		'onblur': 'return validateStringOnBlur(this, event, "alphaSpecialChar");',
		'class': 'form-control charge_hsn_code text-left'}), required=False)
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Quantity',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal();'}),
		required=False, initial='1.00')
	rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_RIGHT, 
		AttributeKeys.PLACE_HOLDER: 'Unit Rate',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal()'}),
		required=False, initial='0.00')
	discount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_RIGHT, 
		AttributeKeys.PLACE_HOLDER: '0.00', 
		AttributeKeys.MAXLENGTH: "6",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,2)',
		AttributeKeys.ONBLUR: 'validatePercentage(this, event); calculateGrandTotal();'}),
		required=False, initial='0.00')
	amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL_RIGHT, AttributeKeys.PLACE_HOLDER: 'Net Value',
		AttributeKeys.DISABLED: AttributeKeys.DISABLED}), 
		required=False, initial='0.00')
	item_tax = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, 'name': 'item_tax'}), required=False)
	is_percent = forms.BooleanField(widget=forms.CheckboxInput(), required=False)
	taxes = ItemTaxFormset(prefix="invoice_charge_tax___prefix__", initial=[
		{"tax_code": "", "tax_type": "CGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "SGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "IGST", "rate": "0.00", "amount": "0.00"}])

	def is_valid(self):
		is_valid = super(InvoiceChargeForm, self).is_valid() and self.taxes.is_valid()
		logger.info("Invoice Charge Form Valid: %s" % is_valid)
		return is_valid


class InvoiceTaxForm(forms.Form):
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	tax_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	invoice_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)


class InvoiceSearchForm(forms.Form):
	from_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	to_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	status = forms.ChoiceField(choices=[
		("", "ALL"), ("1", "Approved"), ("0", "Pending"), ("-1", "Cancelled")],
		widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		type_group = kwargs.pop("type", "internal")
		super(InvoiceSearchForm, self).__init__(*args, **kwargs)
		self.fields['party_name'] = forms.ChoiceField(choices=helper.populateInvoicePartyChoices(
			enterprise_id=enterprise_id),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))
		self.fields['project'] = forms.ChoiceField(choices=helper.populateProjectChoices(
			enterprise_id=enterprise_id, need_blank_first=False),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))


class TagForm(forms.Form):
	""" VO to capture Object Tags"""
	tag = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Type your tags here', AttributeKeys.MAXLENGTH: "40"}))


class ExpenseForm(forms.Form):
	""" Expense Form """
	claimed_user = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	created_by = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=True)
	approved_by = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=-1, required=False)
	checked_by = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=-1, required=False)
	verified_by = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=-1, required=False)
	created_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	confirmed_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	approved_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	checked_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	verified_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	financial_year = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Expense Id'}),
		required=False)
	claim_head_ledger_name = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.DISABLED: AttributeKeys.DISABLED}),
		required=False)
	group_description = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Group Description', AttributeKeys.MAXLENGTH: '150',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}),
		required=True)
	status = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), initial=0,
		required=True)
	status_to_be_changed = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), initial=0,
		required=False)
	status_code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)
	remarks = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.PLACE_HOLDER: 'Remarks', AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	super_edit = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		user_id = kwargs.pop("user_id", None)
		super(ExpenseForm, self).__init__(*args, **kwargs)
		self.fields['claim_head_ledger_id'] = forms.ChoiceField(
			choices=helper.populateUserClaimHeadChoices(
				enterprise_id=enterprise_id, user_id=user_id, include_default=(None, "-- Select Claim Head --")),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))
		self.fields['expense_head_ledger'] = forms.ChoiceField(
			choices=helper.populateUserExpenseHeadChoices(
				enterprise_id=enterprise_id, user_id=user_id, include_default=(None, "-- Select Expense Head --")),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}), required=False)


class ExpenseParticularsForm(forms.Form):
	"""
	Expense Particulars
	"""
	expense_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)
	item_no = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)
	spent_on = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.DATE_INPUT_PARTIAL}),
		required=True)
	description = forms.CharField(widget=forms.TextInput(attrs={
		"data-enterkey-submit": "cmdadd", AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT,
		AttributeKeys.PLACE_HOLDER: 'Description', AttributeKeys.MAXLENGTH: "150",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=True)
	expense_head_ledger_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	expense_head_ledger_name = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.DISABLED: AttributeKeys.DISABLED}),
		required=False)
	amount = forms.FloatField(widget=forms.TextInput(
		attrs={
			AttributeKeys.CLASS: StyleConstants.NEW_CALCULATE_TEXT_BOX, 
			AttributeKeys.PLACE_HOLDER: '0.00',
			AttributeKeys.MAXLENGTH: "16",
			AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,2)'}),
		required=True, initial='0.00')
	approver_debit = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.CALCULATE_TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: '0.00',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,2)'}),
		required=False, initial='0.00')
	audit_debit = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.CALCULATE_TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: '0.00',
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,2)'}), required=False, initial='0.00')
	net_amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.DISABLED: AttributeKeys.DISABLED,
		AttributeKeys.PLACE_HOLDER: '0.00'}), required=False, initial='0.00')
	bill_available = forms.BooleanField(widget=forms.CheckboxInput, required=False)
	remarks = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Remarks', AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	remarks_list = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Remarks'}), required=False)
	document = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)
	document_data = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT}), required=False)


class ExpenseSearchForm(forms.Form):
	since = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	till = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	status = forms.ChoiceField(
		choices=[("", "ALL"), ("0", "Saved"), ("1", "Confirmed"), ("2", "Checked"), ("3", "Verified")],
		widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))


class NoteForm(forms.Form):
	id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	value = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TOTAL_AMT_TEXT_BOX, 'readonly':'readonly', 'tabindex':'-1'}),
		required=False, initial='0.00')
	financial_year = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	round_off = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL,
		AttributeKeys.PLACE_HOLDER: '0.00',
		AttributeKeys.MAXLENGTH: "8",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,5,2,false,true)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal()'}),
		initial='0.00', required=False)
	is_credit = forms.BooleanField(widget=forms.CheckboxInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False, initial=False)
	receipt_code = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45",
		       'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	invoice_date = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	invoice_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	invoice_value = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,2)'}),
		required=False, initial='0.00')
	currency_conversion_rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_CONVERSION_RATE,
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)', }), required=False, initial=1.00)
	ledger_bill_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)

	raised_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	raised_by = forms.CharField(widget=forms.HiddenInput(), required=False, initial=None)
	approved_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	approved_by = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=None)
	last_modified_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	last_modified_by = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=None)
	status = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
	remarks = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Remarks', AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)

	document = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	document_data = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	currency_id = forms.ChoiceField(widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.CURRENCY_DROP_BOX}), required=True, choices=helper.populateCurrencyChoices())

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, module_table_name='crdrnote',
			frequents_condition="")

		super(NoteForm, self).__init__(*args, **kwargs)
		self.fields['supplier_id'] = forms.ChoiceField(widget=forms.Select(attrs={
			AttributeKeys.CLASS: StyleConstants.DROP_BOX}), required=True, choices=helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=False))
		self.fields['project_code'] = forms.ChoiceField(widget=forms.Select(attrs={
			AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX, AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}), required=True, choices=project_choices)


class NoteItemForm(forms.Form):
	note_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	item_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT_ICD,
		AttributeKeys.PLACE_HOLDER: 'Enter Item Name'}), required=False)
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_ICD_QTY, 
		AttributeKeys.PLACE_HOLDER: 'Quantity', 
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal();'}),
		required=False, initial='0.00')
	hsn_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'HSN/SAC',
		AttributeKeys.MAXLENGTH: '9', 'onKeyPress': 'validateStringOnKeyPress(this,event, "hsn_specialChar");',AttributeKeys.ONCHANGE: 'validateHsnWithSuggestion(this)',
		'onblur': 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_ICD_RATE, 
		AttributeKeys.PLACE_HOLDER: 'Unit Rate', 
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal();'}),
		required=False, initial='0.00')
	amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Net Value'}),
		required=False, initial='0.00')
	amount_display = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LABEL, AttributeKeys.DISABLED: AttributeKeys.DISABLED, 'tabindex':'-1'}), required=False, initial='0.00')
	reason_id = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=True, choices=helper.populateNoteReason())

	type = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False, choices=helper.populateNoteType())
	item_tax = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN, 'name': 'item_tax'}), required=False)
	taxes = ItemTaxFormset(prefix="note_item_tax___prefix__", initial=[
		{"tax_code": "", "tax_type": "CGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "SGST", "rate": "0.00", "amount": "0.00"},
		{"tax_code": "", "tax_type": "IGST", "rate": "0.00", "amount": "0.00"}])

	def __init__(self, *args, **kwargs):
		_enterprise_id = kwargs.pop('enterprise_id', None)
		_unit_choices = helper.populateUnit(enterprise_id=_enterprise_id)
		super(NoteItemForm, self).__init__(*args, **kwargs)
		self.fields['unit_id'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.UNIT_SELECT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=_unit_choices)

	def is_valid(self):
		is_valid = super(NoteItemForm, self).is_valid() and self.taxes.is_valid()
		logger.info("Note Item & Taxes Form Valid: %s" % is_valid)
		return is_valid


class NoteSearchForm(forms.Form):
	from_date = forms.DateTimeField(
		widget=forms.DateTimeInput(
			attrs={"readonly": "", AttributeKeys.CLASS: StyleConstants.DATE_INPUT},
			format=StyleConstants.DATETIME_FORMAT), required=False)
	to_date = forms.DateTimeField(
		widget=forms.DateTimeInput(
			attrs={"readonly": "", AttributeKeys.CLASS: StyleConstants.DATE_INPUT},
			format=StyleConstants.DATETIME_FORMAT), required=False)
	status = forms.ChoiceField(
		choices=[("", "ALL"), ("1", "Approved"), ("0", "Pending"), ("-1", "Cancelled")],
		widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		super(NoteSearchForm, self).__init__(*args, **kwargs)
		self.fields['party_name'] = forms.ChoiceField(
			choices=helper.populatePartyChoices(enterprise_id=enterprise_id, populate_all=True, is_supplier=False),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))


class NoteTaxForm(forms.Form):
	enterprise_id = forms.IntegerField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	tax_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	receipt_no = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)


class MakeForm(forms.Form):
	""" VO to capture Object Makes"""
	make = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))


class MaterialMakeProfileForm(forms.Form):
	"""
	Form object that will be used to collect material wise make profile
	"""
	make = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "40", AttributeKeys.PLACE_HOLDER: "Type and press enter to add new make"})
		, required=False)
	part_no = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "20", AttributeKeys.PLACE_HOLDER: "Enter Manufacturer Part Number(optional)"})
		, required=False)
	make_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	item_id = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	drawing_no = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	standard_packing_quantity = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "16", AttributeKeys.PLACE_HOLDER: "Enter Standard Packing Qty"})
		, required=False)


class PayStructureForm(forms.Form):
	id = forms.IntegerField(required=False, widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	enterprise_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	description = forms.CharField(widget=forms.TextInput(attrs={
		"data-enterkey-submit": "save_pay_button", AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "50",
		AttributeKeys.PLACE_HOLDER: 'Pay Structure Name'}), required=True)


class PayStructureDetailsForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the 'SupplierWiseMaterial' model
	"""
	pay_structure_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	type = forms.ChoiceField(
		required=True, choices=helper.populatePayType(), widget=forms.Select(attrs={
			AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
	description = forms.CharField(widget=forms.TextInput(attrs={
		"data-enterkey-submit": "add_new_pay_details", AttributeKeys.ONBLUR: "onChangePayStructureItem(this)",
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45",
		AttributeKeys.PLACE_HOLDER: 'Description'}), required=True)


class EmployeePayStructureForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the 'SupplierWiseMaterial' model
	"""
	employee_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.CharField(widget=forms.HiddenInput(), required=False)
	type = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=True)
	description = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=True)
	amount = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Amount'}),
		required=True)


class DepartmentForm(forms.Form):
	"""
	VO-cum-UI form object that holds the Ledger object
	"""
	id = forms.IntegerField(
		widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	department_label = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL,
		AttributeKeys.PLACE_HOLDER: 'Select an Department'}),
		required=False)
	name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL, AttributeKeys.PLACE_HOLDER: 'Enter Department Name',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=True)
	enterprise_id = forms.CharField(widget=forms.HiddenInput(), required=False)

	def __init__(self, enterprise_id=None, *args, **kwargs):
		super(DepartmentForm, self).__init__(*args, **kwargs)
		self.fields['parent_id'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.BOOTSTRAP_FORM_CONTROL}),
			choices=helper.populateDepartment(enterprise_id=enterprise_id, with_new_department=False), required=False)


class OAForm(forms.Form):
	id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	currency_id = forms.ChoiceField(widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.CURRENCY_DROP_BOX}), choices=helper.populateCurrencyChoices())
	currency_conversion_rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_CONVERSION_RATE,
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)'}), 
		required=False, initial=1.00)
	po_date = forms.DateField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	po_no = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_FIXED_WIDTH_MEDIUM, AttributeKeys.MAXLENGTH: "45",}), required=False)
	oa_no = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False, initial=0)
	se_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	se_date = forms.DateField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	prepared_on = forms.CharField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.DATETIME_FORMAT, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	delivery_due_date = forms.CharField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	se_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	payment_terms = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_FIXED_WIDTH_MEDIUM, AttributeKeys.MAXLENGTH: "150",
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	special_instructions = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	financial_year = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	status = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
	approved_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.DATE_INPUT}, format=StyleConstants.DATE_FORMAT), required=False)
	grand_total = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False, initial='0.00')
	round_off = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL, 
		AttributeKeys.PLACE_HOLDER: '0.00',
		AttributeKeys.MAXLENGTH: "7",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,4,2,false,true)',
		AttributeKeys.ONBLUR: 'calculateGrandTotal()'}),
		initial='0.00', required=False)

	document = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	document_data = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	document_description = forms.CharField(widget=forms.TextInput(
		attrs={
			AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Document Description', AttributeKeys.MAXLENGTH: "100",
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}),
		required=False)
	remarks = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		super(OAForm, self).__init__(*args, **kwargs)
		type_group = kwargs.pop("type", "oa")
		party_choices = helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=False, assort_frequent_choices=True,
			module_table_name='order_acknowledgement', frequents_condition="")
		self.fields['party_id'] = forms.ChoiceField(
			required=True, choices=party_choices,
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PARTY_DROP_BOX}))

		type_choices = OA.TYPE_CHOICES[type_group]
		self.fields['type'] = forms.ChoiceField(required=True, choices=type_choices, widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, frequents_condition="", is_child=True)
		self.fields['project_code'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX, AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}),
			choices=project_choices)


class OAParticularsForm(forms.Form):
	oa_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	item_id = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	item_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	hsn_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'HSN/SAC',AttributeKeys.ONCHANGE: 'validateHsnWithSuggestion(this)',
		AttributeKeys.MAXLENGTH: "9", 'onKeyPress': 'validateStringOnKeyPress(this, event, "hsn_specialChar");',
		'onBlur': 'return validateStringOnBlur(this, event, "alphaSpecialChar");',
		'class': 'form-control oa_hsn_code text-right'}), required=False)
	item_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Item Code & Name', AttributeKeys.MAXLENGTH: "100"}),
		required=False)
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Quantity', 
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		'class': 'form-control oa_quantity textbox_amt text-right'}))
	unit_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s textbox_amt text-center' % StyleConstants.TEXT_BOX_LABEL,
		"readonly": "", "tabindex": "-1"}), required=False)
	price = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s oa_unitPrice textbox_amt text-right' % StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Unit Rate',
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)'}))
	discount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: '0.00', 
		AttributeKeys.MAXLENGTH: "6",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,2)',
		AttributeKeys.ONBLUR: 'validatePercentage(this, event);',
		'class': 'form-control oa_discount text-right'}),
		required=False, initial='0.00')
	amount = forms.FloatField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s oa_price text-right' % StyleConstants.TEXT_BOX_LABEL,
		"readonly": "", "tabindex": "-1"}))
	remarks = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Remarks (optional)', AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}),
		required=False)
	make_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	is_faulty = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)
	is_service = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)
	inv_quantity = forms.FloatField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	alternate_unit_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX + ' alternate_unit_id',
		       AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	scale_factor = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False, initial='1.00')
	internal_price = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s internal_price iwo-price text-right' % StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Internal Price',
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN
	}), initial = '0.00')
	project_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN,
		AttributeKeys.CLASS: 'iwo-project'
	}),	initial=False)
	internal_oa_id = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), initial=False)

	def is_valid(self):
		is_valid = super(OAParticularsForm, self).is_valid()
		logger.info("OA Particulars Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(OAParticularsForm, self).__init__(*args, **kwargs)
		self.fields['makes'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}),
			choices=helper.populateMakes(enterprise_id=enterprise_id), required=False)
		self.fields['alternate_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={
				AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Units',
				AttributeKeys.ONCHANGE: "loadAlternateUnitPrice()"}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)
		self.fields['all_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)


class OATaxForm(forms.Form):
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	tax_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	oa_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)


class OASearchForm(forms.Form):
	from_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	to_date = forms.DateTimeField(widget=forms.DateTimeInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	status = forms.ChoiceField(widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), choices=[
		("", "ALL"), ("1", "Approved"), ("0", "Pending"), ("-1", "Cancelled")])

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(OASearchForm, self).__init__(*args, **kwargs)
		self.fields['party_name'] = forms.ChoiceField(choices=helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=True, is_customer=True),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))


class BankReconciliationForm(forms.Form):
	"""
	VO-cum-UI form object that holds the BRS object
	"""
	voucher_id = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	voucher_date = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'voucher_date')}))
	ledger_label = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'ledger_select_label')}))
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	bank_reconciliation_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		"class": "form-control rec_bank_date hide", 'onBlur': 'calcTotal();'}, format=StyleConstants.DATE_FORMAT),
		required=False)
	instrument_no = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'instrument_no'),
		AttributeKeys.MAXLENGTH: "30"}))
	voucher_no = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'voucher_no')}))
	narration = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'narration')}))
	amount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s text_box_label oa_price_ns textbox_amt text-right' % StyleConstants.TEXT_BOX,
		"readonly": "", "tabindex": "-1",
		AttributeKeys.MAXLENGTH: "18",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,15,2)'}), required=False, initial='0.00')
	is_debit = forms.CharField(required=False, widget=forms.TextInput(attrs={
		"enterKey": "do_nothing", AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'is_debit')}))
	ledger_id = forms.CharField(required=True, widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: "%s %s" % (StyleConstants.TEXT_BOX, 'ledger_select')}))


class BRSSearchForm(forms.Form):
	from_date = forms.DateTimeField(
		widget=forms.DateTimeInput(format=StyleConstants.DATETIME_FORMAT,  attrs={
			AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN}), required=False)
	to_date = forms.DateTimeField(
		widget=forms.DateTimeInput(format=StyleConstants.DATETIME_FORMAT, attrs={
			AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}), required=False)

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(BRSSearchForm, self).__init__(*args, **kwargs)
		self.fields['ledger_id'] = forms.ChoiceField(choices=helper.populateBankLedgerChoices(
			enterprise_id=enterprise_id), widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))


class InvoiceTemplateConfigForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the template details for the enterprise
	"""
	id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	template_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	module = forms.CharField(widget=forms.HiddenInput(), required=False)
	last_modified_by = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	last_modified_on = forms.IntegerField(widget=forms.HiddenInput(), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTemplateConfigForm, self).is_valid()
		logger.info("Invoice Template Config Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateConfigForm, self).__init__(*args, **kwargs)


class InvoiceTemplateGeneralConfigForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the general configuration details for the template page
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	page_size = forms.CharField(widget=forms.HiddenInput(), required=False)
	alignment = forms.CharField(widget=forms.HiddenInput(), required=False)
	tab_retain = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	margin_top = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "page_margin_top"}), required=True)
	margin_bottom = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "page_margin_bottom"}), required=True)
	margin_right = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "page_margin_right"}), required=True)
	margin_left = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "page_margin_left"}), required=True)
	inv_doc_datetime_format = forms.CharField(widget=forms.HiddenInput(), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTemplateGeneralConfigForm, self).is_valid()
		logger.info("Invoice Template General Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateGeneralConfigForm, self).__init__(*args, **kwargs)
		self.fields['base_font'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}),
			choices=helper.populateInvoiceTemplateBaseFont(), required=True)


class InvoiceTemplateHeaderConfigForm(forms.Form):
	"""
	Form object that will be used to collect from and present to the UI, information about the configuration details for
	the template header page.
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	include_logo = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "type": "checkbox", "checkboxfor": "pdf_company_logo"}), required=False, initial=True)
	logo_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "slider", "type": "range", "min": "0", "max": "100", "step": "1"}), required=True)
	include_name = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_company_name"}), required=False, initial=True)
	name_font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "value": "10", "min": "0", "max": "100", "step": "1", "spinner_for": "pdf_company_name"}), required=True)
	font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "value": "10", "min": "0", "max": "100", "step": "1", "spinner_for": "pdf_company_details"}), required=True)
	form_name_font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "value": "10", "min": "0", "max": "100", "step": "1", "spinner_for": "pdf_form_name"}), required=True)
	invoice_number_font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "value": "10", "min": "0", "max": "100", "step": "1", "spinner_for": "pdf_invoice_number"}), required=True)
	include_address = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_company_address"}), required=False, initial=True)
	include_email = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_company_email"}), required=False, initial=True)
	include_phone_no = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_company_contact"}), required=False, initial=True)
	include_fax = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_company_fax"}), required=False, initial=True)
	include_cin = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_cin_number"}), required=False, initial=True)
	include_gstin = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_gst_number"}), required=False, initial=True)
	include_pan = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_pan_number"}), required=False, initial=True)
	include_tin = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_tin_number"}), required=False, initial=True)
	include_ecc_no = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_ecc_number"}), required=False, initial=True)
	include_tan = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_tan_number"}), required=False, initial=True)
	include_cst_no = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_cst_number"}), required=False, initial=True)
	include_billingaddress = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_bill_to_address"}), required=False, initial=True)
	billingaddress_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "bill_to_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_shippingaddress = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_ship_to_address"}), required=False, initial=True)
	shippingaddress_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "ship_to_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_partycode = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_bill_to_enterprise_code"}), required=False, initial=True)
	include_partyname = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_bill_to_enterprise_name"}), required=False, initial=True)
	include_party_email = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_bill_to_email"}), required=False, initial=True)
	include_party_phoneno = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_bill_to_contact"}), required=False, initial=True)
	gst_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	service_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	trading_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	billofsupply_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	others_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	invoiceno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_invoice_prefix", AttributeKeys.MAXLENGTH: "30"}), required=False)
	invoicedate_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_invoice_datetime", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_paymentterms = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_payment_term"}), required=False, initial=True)
	paymentterms_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_payment_term_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	
	include_pono = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_po_no"}), required=False, initial=True)
	pono_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_po_no_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)

	include_podate = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_po_date_span"}), required=False, initial=True)
	podate_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_po_date_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)

	include_transportmode = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_transport_mode"}), required=False, initial=True)
	transportmode_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_transport_mode_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_splinstructions = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "tr_special_instruction"}), required=False, initial=True)
	splinstructions_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "tr_special_instruction_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_roadpermitno = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_road_permit_no"}), required=False, initial=True)
	roadpermitno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_road_permit_no_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_lrnodate = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_lr_no"}), required=False, initial=True)
	lrnodate_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_lr_no_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_packingslipno = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_packing_slip_no"}), required=False, initial=True)
	packingslipno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_packing_slip_no_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_packingdescription = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_packing_desc"}), required=False, initial=True)
	packingdescription_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_packing_desc_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_authorizedsign = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_auth_sign"}), required=False, initial=True)
	authorizedsign_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_auth_sign_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_approver_signature = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "show_approver_signature"}), required=False, initial=False)
	include_preparedsign = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_prepared_sign"}), required=False, initial=False)
	preparedsign_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_prepared_sign_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	dc_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_form_name_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	dcno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_invoice_prefix", AttributeKeys.MAXLENGTH: "30"}), required=False)
	included_reg_items = forms.CharField(widget=forms.HiddenInput(), required=False)
	inv_number_format = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: "form-control form-number_format", "textboxfor": "pdf_invoice_number", AttributeKeys.PLACE_HOLDER: '{fy}/{type:.1}{number:05}{sub_number}',
		AttributeKeys.MAXLENGTH: 45}))
	include_estimate_nodate = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "required_checkbox", "checkboxfor": "pdf_estimate_no_date"}),
		required=False, initial=True)
	estimate_nodate_label = forms.CharField(
		widget=forms.TextInput(attrs={
			AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_estimate_no_date_txt",
			AttributeKeys.MAXLENGTH: "30"}), required=False)
	scan_code_option = forms.ChoiceField(
		widget=RadioSelectAsRow(), choices=(
			(1, 'None'), (2, 'QR Code'), (3, 'Bar Code')), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTemplateHeaderConfigForm, self).is_valid()
		logger.info("Invoice Template Header Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateHeaderConfigForm, self).__init__(*args, **kwargs)
		self.fields['name_font'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}),
			choices=helper.populateInvoiceTemplateBaseFont(), required=True)


class InvoiceTemplateItemsConfigForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the configuration details for the material table.
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "table_font_size"}), required=True)
	include_sno = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox width_calc_checkbox", "checkboxfor": "td_sno"}), required=False)
	sno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_sno_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	sno_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_sno"}), required=False)
	itemdetails_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_description_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	itemdetails_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_description", "disabled": "disabled"}), required=False)
	itemdetails_width_hidden_field = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	include_itemcode = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_drawing_number"}), required=False)
	itemcode_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_drawing_number_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	name_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_name_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_make = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_make"}), required=False)
	make_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_make_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_partno = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_part"}), required=False)
	partno_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_part_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_oano = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_oa"}), required=False)
	oano_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_oa_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_dc_no = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "pdf_item_dc_no"}), required=False)
	dc_no_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_dc_no_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_dc_qty = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_dc_qty"}), required=False)
	include_dc_date = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_dc_date"}), required=False)
	include_description = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_desc"}), required=False)
	description_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_desc_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_remarks = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "pdf_item_remarks"}), required=False)
	remarks_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_remarks_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	include_hsnsac = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox", "checkboxfor": "pdf_item_hsn_code"}), required=False)
	hsnsac_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "pdf_item_hsn_code_txt", AttributeKeys.MAXLENGTH: "30"}), required=False)
	hsnsac_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_hsn_code"}), required=False)
	hsnsac_part_of_itemdetails = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "pdf_hsn_in_description"}), required=False)
	include_quantity = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox", "checkboxfor": "td_qty"}), required=False)
	quantity_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_qty_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	quantity_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_qty_text"}), required=False)
	include_units = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox", "checkboxfor": "td_uom"}), required=False)
	units_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_uom_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	units_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_uom_text"}), required=False)
	units_in_quantity_column = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "pdf_unit_in_price"}), required=False)
	include_primary_qty = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "pdf_primary_unit"}), required=False)
	include_unit_price = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox width_calc_checkbox", "checkboxfor": "td_price"}), required=False)
	unit_price_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_price_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	unit_price_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_price_text"}), required=False)
	include_discount = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox width_calc_checkbox", "checkboxfor": "td_disc"}), required=False)
	discount_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_disc_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	discount_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_disc_text"}), required=False)
	include_taxable_amount = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox", "checkboxfor": "td_tax"}), required=False)
	taxable_amount_label = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "form-control form-alternate-text", "textboxfor": "td_tax_text", AttributeKeys.MAXLENGTH: "30"}), required=False)
	taxable_amount_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_tax_text"}), required=False)
	show_tax_for_dc = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox taxes_for_dc"}), required=False, initial=True)
	include_tax = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "taxable_item"}), required=False)
	tax_type = forms.ChoiceField(
		widget=RadioSelectAsRow(), choices=(
			(1, 'Columns'), (2, 'Consolidated Summary'), (3, 'Part of Item Details'), (4, 'Rate & Amount in one Column')),
		required=False)
	tax_type_hidden_field = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	include_taxrate = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox taxable_item", "checkboxfor": "tax_rate_column"}), required=False)
	include_taxrate_hidden_field = forms.BooleanField(widget=forms.HiddenInput(), required=False)
	taxrate_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width taxable_item", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_tax_rate"}), required=False)
	include_taxamount = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox width_calc_checkbox taxable_item", "checkboxfor": "tax_amount_column"}), required=False)
	include_taxamount_hidden_field = forms.BooleanField(widget=forms.HiddenInput(), required=False)
	taxamount_width = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner input_spinner_set_width taxable_item", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "td_tax_amt"}), required=False)
	include_row_separator = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "row_seperator"}), required=False)
	include_column_separator = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "column_seperator"}), required=False)
	include_alternate_row_shading = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "row_shading"}), required=False)
	item_sort_order = forms.ChoiceField(
		widget=RadioSelectAsRow(), choices=(
			(1, 'Order of Entry'), (2, 'Item Name'), (3, 'Item Code/ Drawing No')), required=False)
	tax_payable_on_reverse_charge = forms.ChoiceField(
		widget=RadioSelectAsRow(), choices=((1, 'Print Always'), (2, 'Print only when Yes')), required=False)
	item_table_space = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "200", "step": "1", "spinner_for": "table_bottom_height"}), required=False)

	data_separator = forms.ChoiceField(widget=forms.Select(attrs={
		AttributeKeys.CLASS: StyleConstants.DROP_BOX}), required=True, choices=[('<br />', 'New Line'), (',', ','), ('/', '/'), ('|', '|')])

	def is_valid(self):
		is_valid = super(InvoiceTemplateItemsConfigForm, self).is_valid()
		logger.info("Invoice Template Items Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateItemsConfigForm, self).__init__(*args, **kwargs)


class InvoiceTemplateSummaryConfigForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the configuration details for the summary table.
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "total_font_size"}), required=True)
	include_total = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "show_total_section"}), required=False, initial=True)
	include_subtotal = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "show_sub_total_section"}), required=False, initial=True)
	include_qty_total = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox customized_checkbox", "checkboxfor": "show_quantity_total"}), required=False, initial=True)
	include_total_in_words = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "show_total_in_words"}), required=False, initial=True)
	hsn_tax_font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "value":"10", "spinner_for": "hsn_tax_font_size"}), required=True)
	include_hsn_summary = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "hsn_summary_contianer"}), required=False, initial=True)

	def is_valid(self):
		is_valid = super(InvoiceTemplateSummaryConfigForm, self).is_valid()
		logger.info("Invoice Template Summary Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateSummaryConfigForm, self).__init__(*args, **kwargs)


class InvoiceTemplateMiscConfigForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the configuration details for the Notes section.
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	font_size = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: "input_spinner", "type": "number", "min": "0", "max": "100", "step": "1", "spinner_for": "misc_font_size"}), required=True)
	include_page_no_in_footer = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox required_checkbox", "checkboxfor": "page_number"}), required=False, initial=True)
	notes = forms.CharField(widget=forms.Textarea(
		attrs={AttributeKeys.CLASS: "form-control", "style": "min-height : 75px", "textboxfor": "notes"}), required=False)
	include_first_page_summary = forms.BooleanField(widget=forms.CheckboxInput(
		attrs={AttributeKeys.CLASS: "checkbox"}), required=False, initial=True)
	foot_note = forms.CharField(widget=forms.Textarea(
		attrs={AttributeKeys.CLASS: "form-control", "style": "min-height : 120px", "textboxfor": "footer_notes",
			   AttributeKeys.MAXLENGTH: "150", 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
			   AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}),
		required=False)
	footer_page_no_alignment = forms.ChoiceField(
		widget=RadioSelectAsRow(), choices=((1, 'Left'), (2, 'Right')), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTemplateMiscConfigForm, self).is_valid()
		logger.info("Invoice Template Misc Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateMiscConfigForm, self).__init__(*args, **kwargs)


class InvoiceTemplateBannerForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the configuration details for the Banner section.
	"""
	enterprise_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	header_size = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	footer_size = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	header_left_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)
	header_center_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)
	header_right_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)
	footer_left_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)
	footer_center_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)
	footer_right_attachment = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTemplateBannerForm, self).is_valid()
		logger.info("Invoice Template Banner Image Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTemplateBannerForm, self).__init__(*args, **kwargs)


class InvoiceTempBannerForm(forms.Form):
	"""
		Form object that will be used to collect from and present to the UI, information about the configuration details for the Banner section.
	"""
	config_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	section = forms.CharField(widget=forms.HiddenInput(), required=False)
	position = forms.CharField(widget=forms.HiddenInput(), required=False)
	size = forms.IntegerField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "banner-image-size"}), required=False)
	attachment_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	uploaded_banner_image = forms.CharField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "hidden-attachment"}),
											required=False)
	is_removed = forms.BooleanField(widget=forms.HiddenInput(attrs={AttributeKeys.CLASS: "remove-attachment"}),
											required=False)
	banner_image = forms.CharField(widget=forms.HiddenInput(), required=False)

	def is_valid(self):
		is_valid = super(InvoiceTempBannerForm, self).is_valid()
		logger.info("Invoice Template Header Banner Image Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		super(InvoiceTempBannerForm, self).__init__(*args, **kwargs)


class SalesEstimateForm(forms.Form):
	id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	project_name = forms.ChoiceField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX}), required=False)
	purpose = forms.CharField(widget=forms.TextInput(attrs={
		"autocomplete": "off", "list": "frequents_purpose_list", AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Purpose', AttributeKeys.MAXLENGTH: '100',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	currency_id = forms.ChoiceField(widget=forms.Select(
		attrs={AttributeKeys.CLASS: StyleConstants.CURRENCY_DROP_BOX}), choices=helper.populateCurrencyChoices())
	currency_conversion_rate = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_CONVERSION_RATE,
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,10,5)'}), 
		required=False, initial=1.00)
	ref_date = forms.CharField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	ref_no = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.MAXLENGTH: "45", AttributeKeys.PLACE_HOLDER: 'Enter Ref No',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	se_no = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False, initial=0)
	prepared_on = forms.CharField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.DATETIME_FORMAT, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN},
		format=StyleConstants.DATE_FORMAT), required=False)
	payment_terms = forms.CharField(
		widget=forms.TextInput(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX_FIXED_WIDTH_MEDIUM, AttributeKeys.MAXLENGTH: "150",
			'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	special_instructions = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: '300',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	notes = forms.CharField(required=False, widget=forms.Textarea(attrs={
		"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA,
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");', 'readonly':'readonly'}))
	financial_year = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	status = forms.IntegerField(widget=forms.HiddenInput(), required=False, initial=0)
	approved_on = forms.DateField(widget=forms.DateInput(attrs={
		AttributeKeys.CLASS: StyleConstants.DATE_INPUT}, format=StyleConstants.DATE_FORMAT), required=False)
	expiry_date = forms.CharField(widget=forms.DateInput(
		attrs={AttributeKeys.CLASS: StyleConstants.SINGLE_DATE_INPUT_HIDDEN}, format=StyleConstants.DATE_FORMAT), required=False)
	grand_total = forms.FloatField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TOTAL_AMT_TEXT_BOX, 'readonly':'readonly'}), required=False, initial='0.00')
	round_off = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.INV_TXT_VAL, 
		AttributeKeys.PLACE_HOLDER: '0.00',
		AttributeKeys.MAXLENGTH: "8",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,5,2,false,true)',
		AttributeKeys.ONBLUR: 'CalculateGrandTotal()'}),
		initial='0.00', required=False)
	remarks = forms.CharField(required=False, widget=forms.Textarea(
		attrs={"rows": "3", AttributeKeys.CLASS: StyleConstants.DESCRIPTION_TEXT_AREA, AttributeKeys.MAXLENGTH: "300",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");', AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop("enterprise_id", None)
		super(SalesEstimateForm, self).__init__(*args, **kwargs)
		party_choices = helper.populatePartyChoices(
			enterprise_id=enterprise_id, populate_all=False, assort_frequent_choices=True,
			module_table_name='sales_estimate', frequents_condition="")
		self.fields['party_id'] = forms.ChoiceField(
			required=True, choices=party_choices,
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PARTY_DROP_BOX}))
		project_choices = helper.populateProjectChoices(
			enterprise_id=enterprise_id, assort_frequent_choices=True, module_table_name='sales_estimate',
			frequents_condition="")
		self.fields['project_code'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.PROJECT_DROP_BOX, AttributeKeys.ONCHANGE: 'validateProjectChange(this)'}),
			required=True, choices=project_choices)
		type_choices = SalesEstimate.TYPE_CHOICES
		self.fields['type'] = forms.ChoiceField(required=True, choices=type_choices, widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))


class SEParticularsForm(forms.Form):
	se_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	enterprise_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	item_id = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	item_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	item_name = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'Item Code & Name',AttributeKeys.MAXLENGTH: "100"}),
		required=False)
	hsn_code = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX_LEFT, AttributeKeys.PLACE_HOLDER: 'HSN/SAC',AttributeKeys.ONCHANGE: 'validateHsnWithSuggestion(this)',
		AttributeKeys.MAXLENGTH: "9", 'onKeyPress': 'validateStringOnKeyPress(this, event, "hsn_specialChar");',
		'onBlur': 'return validateStringOnBlur(this, event, "alphaSpecialChar");',
		'class': 'form-control se_hsn_code text-right'}), required=False)
	quantity = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Quantity', 
		AttributeKeys.MAXLENGTH: "16",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,12,3)',
		'class': 'form-control se_quantity textbox_amt text-right'}))
	unit_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s textbox_amt text-center' % StyleConstants.TEXT_BOX_LABEL,
		"readonly": "", "tabindex": "-1"}), required=False)
	unit_price = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s se_unitPrice textbox_amt text-right' % StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Unit Rate', 
		AttributeKeys.MAXLENGTH: "19",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,13,5)'}))
	discount = forms.FloatField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: '0.00', 
		AttributeKeys.MAXLENGTH: "6",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,3,2)',
		AttributeKeys.ONBLUR: 'validatePercentage(this, event);',
		'class': 'form-control se_discount text-right'}),
		required=False, initial='0.00')
	price = forms.FloatField(required=False, initial='0.00', widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: '%s se_price text-right' % StyleConstants.TEXT_BOX_LABEL,
		"readonly": "", "tabindex": "-1"}))
	make_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	is_faulty = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)
	is_service = forms.BooleanField(widget=forms.HiddenInput(), required=False, initial=False)
	alternate_unit_id = forms.IntegerField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX + 'alternate_unit_id', AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)
	scale_factor = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False, initial='1.00')
	alternate_unit_list = forms.CharField(widget=forms.TextInput(
		attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}),
		required=False)

	def is_valid(self):
		is_valid = super(SEParticularsForm, self).is_valid()
		logger.info("SE Particulars Form Valid: %s" % is_valid)
		return is_valid

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(SEParticularsForm, self).__init__(*args, **kwargs)
		self.fields['makes'] = forms.ChoiceField(
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}),
			choices=helper.populateMakes(enterprise_id=enterprise_id), required=False)
		self.fields['all_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)
		self.fields['alternate_units'] = forms.ChoiceField(widget=forms.Select(
			attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX,  AttributeKeys.ONCHANGE: "loadAlternateUnitPrice()",
                   AttributeKeys.PLACE_HOLDER: 'Units'}),
			choices=helper.populateUnit(enterprise_id=enterprise_id), required=False)


class SETaxForm(forms.Form):
	enterprise_id = forms.IntegerField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)
	tax_code = forms.CharField(widget=forms.TextInput(attrs={AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}))
	se_id = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.HIDDEN: AttributeKeys.HIDDEN}), required=False)


class SESearchForm(forms.Form):
	from_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.FROM_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	to_date = forms.DateTimeField(widget=forms.DateTimeInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TO_DATE_INPUT_HIDDEN}, format=StyleConstants.DATETIME_FORMAT),
		required=False)
	status = forms.ChoiceField(choices=[
		("", "ALL"), ("2", "Approved"), ("0", "Draft"), ("1", "Reviewed"), ("3", "Client Approved"), ("4", "Client Rejected"),
		("-1", "Cancelled")],
		widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.TEXT_BOX}))

	def __init__(self, *args, **kwargs):
		enterprise_id = kwargs.pop('enterprise_id', None)
		super(SESearchForm, self).__init__(*args, **kwargs)
		self.fields['party_name'] = forms.ChoiceField(choices=helper.populateInvoicePartyChoices(
			enterprise_id=enterprise_id),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))
		self.fields['project'] = forms.ChoiceField(choices=helper.populateProjectChoices(
			enterprise_id=enterprise_id, need_blank_first=False),
			widget=forms.Select(attrs={AttributeKeys.CLASS: StyleConstants.DROP_BOX}))


class SpecificationForm(forms.Form):
	"""
		Form object that will used to collect specification information of material
	"""
	material_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	enterprise_id = forms.IntegerField(widget=forms.HiddenInput(), required=False)
	parameter = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Parameter',
		AttributeKeys.MAXLENGTH: '100', 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}))
	comments = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Comment',
		AttributeKeys.MAXLENGTH: '300', 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	inspection_method = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Inspection Method',
		AttributeKeys.MAXLENGTH: '100', 'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	min_value = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter min value',
		AttributeKeys.MAXLENGTH: "11",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,7,3,true)'}),
		initial=0, decimal_places=3, required=False)
	max_value = forms.DecimalField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, 
		AttributeKeys.PLACE_HOLDER: 'Enter max value',
		AttributeKeys.MAXLENGTH: "11",
		AttributeKeys.ONFOCUS: 'setNumberRangeOnFocus(this,7,3,true)'}),
		initial=0, decimal_places=3, required=False)
	unit = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX, AttributeKeys.PLACE_HOLDER: 'Enter Unit', AttributeKeys.MAXLENGTH: '10',
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)
	qc_critical = forms.BooleanField(widget=forms.CheckboxInput(attrs={AttributeKeys.CLASS: "checkbox"}), required=False)
	reaction_plan = forms.CharField(widget=forms.TextInput(attrs={
		AttributeKeys.CLASS: StyleConstants.TEXT_BOX,
		AttributeKeys.PLACE_HOLDER: 'Enter Reaction Plan', AttributeKeys.MAXLENGTH: "150",
		'onKeyPress': 'return validateStringOnKeyPress(this,event, "alphaSpecialChar");',
		AttributeKeys.ONBLUR: 'return validateStringOnBlur(this, event, "alphaSpecialChar");'}), required=False)

