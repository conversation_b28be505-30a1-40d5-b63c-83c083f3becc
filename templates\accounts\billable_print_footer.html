<!DOCTYPE html>
<script>
	function subst1() {
		var vars={};
		var x=document.location.search.substring(1).split('&');
		for (var i in x) {
			var z=x[i].split('=',2);
			vars[z[0]] = unescape(z[1]);
		}
		var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
		for (var i in x) {
			var y = document.getElementsByClassName(x[i]);
			for (var j=0; j<y.length; ++j)
				y[j].textContent = vars[x[i]];
			if(vars['page'] == vars['topage']){
			   document.getElementById("footer_div").style.display = 'block';
			}
			else {
			   document.getElementById("footer_div").style.display = 'none';
			}
		}
	}
</script>
<body onload="subst1()">
	<div class="col-sm-12" id="footer_div">
		<div><hr></div>
		<div style="width:32.33%"></div>

		<div class="col-sm-3" style="width:32.33%;float:right;text-align:center;">
			<span> {{first_name}} {{ last_name }}</span><br>
			<span>Prepared By</span>
		</div>
	</div>
</body>