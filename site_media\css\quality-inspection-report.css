.edit_quality_inspection {
	position: absolute;
    right: 0px;
    margin-top: -10px;
    cursor: pointer;
    margin: -10px -4px !important;
    padding:  8px !important;
}

.edit_quality_inspection img{
	width: 24px;
    padding: 0px;
    border-radius: 50px;
    background: #ccc;
}
.quality-inspection-headers{
	border-top: none;
}

.quality-inspection-headers td {
	padding:  4px	!important;
}

.inspection_reports-content input[type='text'] {
    padding: 8px;
    height: 30px;
    font-size: 12px;
}

.quality-inspection-headers th {
	min-width: 150px;
}

.quality-inspection-headers th:first-child {
	min-width: 200px;
}

.delete-inspection-icon {
	float: left;
    font-size: 18px;
    margin-top: 5px;
    cursor: pointer;
}

.inspection-sample-id {
	float: right;
    display: inline-block;
    width: calc(100% - 22px);
}