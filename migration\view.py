import json
from threading import Thread

import pymongo
from dateutil.relativedelta import relativedelta

from erp.accounts.backend import AccountService
from erp.auth import USER_IN_SESSION_KEY, SESSION_KEY
from erp.auth.request_handler import RequestHandler
from erp.helper import getUser
from migration import logger
from django.http import HttpResponse
from migration.backend import ExportService, initiate_export_thread
from settings import MONGODB_DB
from util.api_util import response_code

MONGODB_CONNECTION_URL = "mongodb://{USER}:{PASSWORD}@{HOST}:{PORT}/{NAME}".format(**MONGODB_DB['default'])
MongoDbConnect = pymongo.MongoClient(MONGODB_CONNECTION_URL)[MONGODB_DB['default']['NAME']]
collection = 'export_details'


def exportAsTallyXML(request):
	try:
		logger.info("Export to Tally")
		request_handler = RequestHandler(request)
		from_date = request_handler.getPostData('from_date')
		to_date = request_handler.getPostData('to_date')
		is_inventory_transaction = request_handler.getPostData('is_inventory_transaction')
		is_closing_balance_as_opening = request_handler.getPostData('is_closing_balance_as_opening')
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		user_id = request_handler.getSessionAttribute(SESSION_KEY)
		user = getUser(enterprise_id=enterprise_id, user_id=user_id)
		export_service = ExportService()
		result = export_service.insertExportDetails(
			user=user, enterprise_id=enterprise_id, from_date=from_date, to_date=to_date,
			is_inventory_transaction=is_inventory_transaction, is_closing_balance_as_opening=is_closing_balance_as_opening,
			status='Pending')
		if result:
			response = response_code.success()
			logger.info("initiate_export_thread")
			Thread(target=initiate_export_thread, kwargs=dict(status='Pending')).start()
		else:
			response = response_code.failure()
	except Exception as e:
		logger.info("Error for Inserting Export Details:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchExportDetails(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		export_service = ExportService()
		result = export_service.fetchExportDetails(enterprise_id=enterprise_id)
		if result:
			response = response_code.success()
		else:
			response = response_code.failure()
		response['result'] = result
	except Exception as e:
		logger.info("Error for fetch export details:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def updateExportDetails(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		unique_id = request_handler.getPostData('export_id')
		export_service = ExportService()
		result = export_service.updateExportDetails(enterprise_id=enterprise_id, unique_id=unique_id)
		if result:
			response = response_code.success()
		else:
			response = response_code.failure()
		response['result'] = result
	except Exception as e:
		logger.info("Error for update export details:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def deleteExportDetails(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		unique_id = request_handler.getPostData('export_id')
		export_service = ExportService()
		result = export_service.deleteExportDetails(enterprise_id=enterprise_id, unique_id=unique_id)
		if result:
			response = response_code.success()
		else:
			response = response_code.failure()
	except Exception as e:
		logger.info("Error for delete export details:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def getFirstTransactionDate(request):
	try:

		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		accounts_service = AccountService()
		closure_voucher = accounts_service.accounts_dao.getPreviousBookClosureVoucher(enterprise_id=enterprise_id)
		earliest_transaction_date = accounts_service.accounts_dao.getEarliestTransactionDate(
			enterprise_id=enterprise_id)
		previous_closure_date = closure_voucher.voucher_date if closure_voucher else None
		response = response_code.success()
		response['earliest_transaction_date'] = str(earliest_transaction_date) if earliest_transaction_date else ""
		response['previous_closure_date'] = str(previous_closure_date) if previous_closure_date else ""

	except Exception as e:
		logger.info("Error for fetch first transaction date:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


def fetchExportLogdetails(request):
	try:
		request_handler = RequestHandler(request)
		enterprise_id = request_handler.getSessionAttribute(USER_IN_SESSION_KEY).enterprise.id
		export_service = ExportService()
		unique_id = request_handler.getPostData('export_id')
		result = export_service.fetchExportLogdetails(enterprise_id=enterprise_id, unique_id=unique_id)
		if result:
			response = response_code.success()
		else:
			response = response_code.failure()
		response['result'] = result
		response['status'], response['download_file_name'] = export_service.fetchExportStatus(
			enterprise_id=enterprise_id, unique_id=unique_id)

	except Exception as e:
		logger.info("Error for fetch export log details:%s" % e.message)
		response = response_code.internalError()
	return HttpResponse(json.dumps(response), 'content-type=text/json')


