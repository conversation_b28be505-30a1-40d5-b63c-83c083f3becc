$(function(){

    $("#reportview").click(function(){
        url = [];
        var list="";
        var po_no=""
        frmdate =  $("#fromdate").val();
        todate =  $("#todate").val();
        repname =$("#reportname option:selected").text();
        var [itemId, makeId, isFaulty] = $('#id-material').val().split("[::]");
        var selected_stock_type=""
        var multi_select_stock_type =  $("select#stock_type").next('.btn-group').find('ul').find('li input:checked');
        var selected_stock_types = [];
        multi_select_stock_type.each(function () {
            if ($(this).val() != 'multiselect-all'){
                selected_stock_types.push($(this).val());
            }
        });
        var selected_location_id=""
        var multi_select_location =  $("select#location_id").next('.btn-group').find('ul').find('li input:checked');
        var selected_location_ids = [];
        multi_select_location.each(function () {
            if ($(this).val() != 'multiselect-all'){
                selected_location_ids.push($(this).val());
            }
        });
        if ( frmdate != ""  && todate != "" ){
            var searchCriteria = {
                since: frmdate, till: todate,
                party_id: $("#cmbsupplier option:selected").val(),
                repname : repname,
                item_id: itemId, make_id: makeId, is_faulty: isFaulty,
                reptype: $("#cmbreptype option:selected").val(),
                stock_type: selected_stock_types.join(','),
                location: selected_location_ids.join(','),
            };
            $('#loading').show();
            if ( repname == "Closing Stock") {
                if(selected_stock_types == "" || selected_stock_types == null) {
                    swal("","Please select Material Type to generate report.","warning");
                    $('#loading').hide();
                    return false;
                }
                $('#Closing_Stock').show();
                $('#Po_Pending').hide();
                $('#Ind_Pending').hide();
                $('#nms_report').hide();
                $('#pp_blocked').hide();
                $('#msl_stock').hide();
                $.ajax({
                    url: "/erp/stores/json/stockreport/",
                    type: "post",
                    datatype:"json",
                    data: searchCriteria,
                    success: function(response){
                        $("#tablesorter").find("tr:gt(0)").remove();
						$('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().clear();
						$('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().destroy();
                        j=0
                        $.each(response, function(i, item) {
                            if (item[3]!=0 || item[4]!=0 ||item[5]!=0){
                                make_name = ""
                                if (item[18]!=""){
                                    make_name = constructDifferentMakeName(item[18]);
                                    if (make_name!=""){
                                        make_name = " [" + make_name + "]"
                                    }
                                }
                                item_name = item[1] + make_name
                                var row = ` <tr>
                                                <td class='text-center'>${(j+1)}</td>
                                                <td>${item[16]}</td>
                                                <td>${item[13]}</td>
                                                <td>${item_name}</td>
                                                <td>${item[17] !== null ? item[17] : ''} </td>
                                                <td class='text-center'>${item[8]}</td>
                                                <td class='text-right'>${item[2].toFixed(5)}</td>
                                                <td class='text-right cs-opening-qty'>${item[3].toFixed(2)}</td>
                                                <td class='text-right cs-receipt-qty'>${item[4].toFixed(2)}</td>
                                                <td class='text-right cs-issue-qty'>${item[5].toFixed(2)}</td>
                                                <td class='text-right cs-closing-qty'>${item[6].toFixed(2)}</td>
                                                <td class='text-right cs-opening-value'>${(item[3] * item[2]).toFixed(2)}</td>
                                                <td class='text-right cs-receipt-value'>${(item[4] * item[2]).toFixed(2)}</td>
                                                <td class='text-right cs-issue-value'>${(item[5] * item[2]).toFixed(2)}</td>
                                                <td class='text-right cs-closing-value'>${(item[6] *item[2]).toFixed(2)}</td>
                                                <td>${item[19].toFixed(2)}</td>
                                            </tr>`;
                                $('#tablesorter tbody').append(row);
                                j=j+1
                            }
                        });
                        calctotal();
                        $('#loading').hide();
						FieldRequiredInTable();
						TableHeaderFixedCS();
                    }
                });
            }
            else if ( repname == "PO Pending Report") {
                $('#Po_Pending').show();
                $('#Closing_Stock').hide();
                $('#Ind_Pending').hide();
                $('#nms_report').hide();
                $('#pp_blocked').hide();
                $('#msl_stock').hide();
                $.ajax({
                    url: "/erp/stores/json/poreport/",
                    type: "post",
                    datatype:"json",
                    data: searchCriteria,
                    success: function(response){
                        $("#po_pending_table").find("tr:gt(0)").remove();
                        $('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().clear();
                        $('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().destroy();
                        $.each(response, function(i, item) {
							if(item[1]  !="") {
								var setdateFormat = item[1].split('/');
								setdateFormat = setdateFormat[1]+"/"+setdateFormat[0]+"/"+setdateFormat[2];
							}
							else {
								var setdateFormat="";
							}
							if (item[10]==0){
							    po_no= item[11]+ "/PO/" + item[0]
							}else{
							    po_no= item[11]+ "/JO/" + item[0]
							}
                            make_name = ""
                            if (item[12]!=""){
                                make_name = constructDifferentMakeName(item[12]);
                                if (make_name!=""){
                                    make_name = " [" + make_name + "]"
                                }
                            }
                            item_name = item[4] + make_name

							var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
							"<td>"+(i+1)+ "</td><td align='left' data-sort="+item[0]+">" +
							po_no  + "</td><td align='left'>" +
							moment(setdateFormat).format('MMM D, YYYY')  + "</td><td align='left'>" +
							item[2]  + "</td><td align='left'>" +
							item[3]  + "</td><td align='left'>" +
							item_name  + "</td><td align='right'>" +
							item[5]  + "</td><td align='left'>" +
							item[6]  + "</td><td align='right'>" +
							item[7].toFixed(2)  + "</td><td align='right'>" +
							item[8].toFixed(2)  + "</td><td align='right'>" +
							item[9].toFixed(2)  + "</td></tr>"
							$('#po_pending_table').append(row).addClass('tbl');
                        });// css for update the table row color and border line
                        calc_po_total();
                        $('#loading').hide();
						FieldRequiredInTable();
						TableHeaderFixedPO();
                    }
                });
            }
            else if ( repname == "Indent Pending Report") {
                $('#Ind_Pending').show();
                $('#Po_Pending').hide();
                $('#Closing_Stock').hide();
                $('#nms_report').hide();
                $('#pp_blocked').hide();
                $('#msl_stock').hide();
                $.ajax({
                    url: "/erp/stores/json/indreport/",
                    type: "post",
                    datatype:"json",
                    data: searchCriteria,
                    success: function(response){
                        $("#ind_pending_table").find("tr:gt(0)").remove();
                        $('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().clear();
                        $('#tablesorter, #po_pending_table, #ind_pending_table').DataTable().destroy();
                        $.each(response, function(i, item) {
							if(item[1] !="") {
								var setDateTime = item[1].split(' ');
								var setdateFormat = setDateTime[0].split('/');
								setdateFormat = setdateFormat[1]+"/"+setdateFormat[0]+"/"+setdateFormat[2];
							}
							else {
								var setdateFormat="";
							}

							indent_no = item[0]
							if(indent_no == '0'){
                                indent_no = '-NA-';
                            }
                            else{
                                while(indent_no.length < 7){
                                    indent_no = '0' + indent_no;
                                    }
                            }
                            var make_name = constructDifferentMakeName(item[13]);
                            var item_description = item[4];
                            if (make_name != ""){
                                item_description += " [" + make_name + "]"
                            }
							var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
							"<td>"+(i+1)+ "</td><td align='center'>"+ item[10] + "/IND/" +
							indent_no + "</td><td align='center'>" +
							moment(setdateFormat).format('MMM D, YYYY') + " "+ setDateTime[1]   + "</td><td align='left'>" +
							item[2]  + "</td><td align='left'>" +
							item[3]  + "</td><td align='left'>" +
							item_description  + "</td><td align='right'>" +
							item[5].toFixed(2)  + "</td><td align='left'>" +
							item[6]  + "</td><td align='right'>" +
							item[7].toFixed(2)  + "</td><td align='right'>" +
							item[8].toFixed(2)  + "</td><td align='right'>" +
							item[9].toFixed(2)  + "</td><td align='right'>" +
							(item[7]-item[9]).toFixed(2)  + "</td></tr>"
							$('#ind_pending_table').append(row).addClass('tbl');
                        });// css for update the table row color and border line
                        calc_ind_total();
                        $('#loading').hide();
						FieldRequiredInTable();
						TableHeaderFixedIndent();
                    }
                });
            }
            else if ( repname == "Non Moving Stock Report") {
                $('#nms_report').show();
                $('#Po_Pending').hide();
                $('#Closing_Stock').hide();
                $('#Ind_Pending').hide();
                $('#pp_blocked').hide();
                $('#msl_stock').hide();
                $.ajax({
                    url: "/erp/stores/json/nmsstockreport/",
                    type: "post",
                    datatype:"json",
                    data: {"enterprise_id" : $('#enterprise_id').val(), "selected_option":$('#daysRange').val()},
                    success: function(response){
                        $("#nms_report_table").find("tr:gt(0)").remove();
                        $('#tablesorter, #po_pending_table, #nms_report_table').DataTable().clear();
                        $('#tablesorter, #po_pending_table, #nms_report_table').DataTable().destroy();
                        $.each(response, function(i, item) {
                            var materialName= item.name
                            if (item.make!=""){
                                    make_name = constructDifferentMakeName(item.make);
                                    if (make_name!=""){
                                        make_name = " [" + make_name + "]"
                                    }
                                }
                                materialName = item.name+ make_name
							var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
							"<td>" + (i+1) + "</td>" +
                            "<td align='center'>" + item.category + "</td>" +
                            "<td align='center'>" + item.unit_name + "</td>" +
                            "<td align='center'>" + item.Latest_date + "</td>" +
                            "<td align='center'>" + materialName + "</td>" +
                            "<td align='center'>" + item.price + "</td>" +
                            "<td align='center'>" + (item.drawing_no ? item.drawing_no : '') + "</td>" +
                            "<td align='center'>" + (item.location ? item.location : '') + "</td>" +
                            "<td align='center'>" + item.closing_qty + "</td>";

							$('#nms_report_table').append(row).addClass('tbl');
                        });
                        $('#loading').hide();
						TableHeaderFixedNms();
                    }
                });
            }
            else if ( repname == "Blocked Stock Report") {
                var since = frmdate;
                var till = todate;
                $('#nms_report').hide();
                $('#Po_Pending').hide();
                $('#Closing_Stock').hide();
                $('#Ind_Pending').hide();
                $('#pp_blocked').show();
                $('#msl_stock').hide();
                $.ajax({
                    url: "/erp/stores/json/blockedreport/",
                    type: "post",
                    datatype:"json",
                    data: {"enterprise_id" : $('#enterprise_id').val(), "since":since, "till":till},
                    success: function(response){
                        $("#pp_blocked_table").find("tr:gt(0)").remove();
                        $('#tablesorter, #po_pending_table, #pp_blocked_table').DataTable().clear();
                        $('#tablesorter, #po_pending_table, #pp_blocked_table').DataTable().destroy();
                        $.each(response, function(i, item) {
                            var materialName= item.name
                            console.log("materialName",materialName)
                            if (item.make!=""){
                                    make_name = constructDifferentMakeName(item.make);
                                    if (make_name!=""){
                                        make_name = " [" + make_name + "]"
                                    }
                                }
                                materialName = item.name+ make_name
							var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
							"<td>" + (i+1) + "</td>" +
                            "<td align='center'>" + (item.drawing_no ? item.drawing_no : '') + "</td>" +
                            "<td align='start'>" + materialName + "</td>" +
                            "<td align='center'>" + item.pp_no + "</td>" +
                            "<td align='center'>" + item.required_qty + "</td>" +
                            "<td align='center'>" + item.allocated_qty + "</td>" +
                            "<td align='center'>" + item.issued_qty + "</td>" +
                            "<td align='center'>" + item.blocked_qty + "</td>";

							$('#pp_blocked_table').append(row).addClass('tbl');
                        });
                        $('#loading').hide();
						TableHeaderFixedBlockRep();
                    }
                });
            }else if ( repname == "MSL Stock Report") {
                var since = frmdate;
                var till = todate;
                $('#msl_stock').show();
                $('#nms_report').hide();
                $('#Po_Pending').hide();
                $('#Closing_Stock').hide();
                $('#Ind_Pending').hide();
                $('#pp_blocked').hide();
                $.ajax({
                    url: "/erp/stores/json/msl_report/",
                    type: "post",
                    datatype:"json",
                    data: {"enterprise_id" : $('#enterprise_id').val(), "since":since, "till":till},
                    success: function(response){
                    console.log("response", response)
                        $("#msl_report_table").find("tr:gt(0)").remove();
                        $('#tablesorter, #po_pending_table, #msl_report_table').DataTable().clear();
                        $('#tablesorter, #po_pending_table, #msl_report_table').DataTable().destroy();
                        $.each(response, function(i, item) {
                            var materialName= item.name
                            if (item.make!=""){
                                    make_name = constructDifferentMakeName(item.make);
                                    if (make_name!=""){
                                        make_name = " [" + make_name + "]"
                                    }
                                }
                                materialName = item.name+ make_name
							var row = "<tr bgcolor='#ececec' border='0' align='center' style='font-size:11px; font-weight:normal;'>"+
							"<td>" + (i+1) + "</td>" +
                            "<td align='center'>" + (item.drawing_no ? item.drawing_no : '') + "</td>" +
                            "<td align='start'>" + materialName + "</td>" +
                            "<td align='center'>" + item.unit + "</td>" +
                            "<td align='center'>" + item.minimum_stock_level + "</td>" +
                            "<td align='center'>" + item.qty + "</td>" +
                            "<td align='center'>" + (item.purchased < 0 ? 0 : item.purchased) + "</td>"

							$('#msl_report_table').append(row).addClass('tbl');
                        });
                        $('#loading').hide();
						TableHeaderMSLRep();
                    }
                });
            }
            else {
                alert("Session Expired, Login to Continue");
                window.location = 'http://'+urlpath+'/erp/'
            }
        }
        else {
            alert("Please Select From and To Date Correctly");
        }
    });

    $("select#reportname").change(function(){
        var reportname = $("#reportname option:selected").text();
        if( reportname  == "Closing Stock") {
            $('#select_div').show();
            $('#stock_type_div').show();
            $('#location_div').show();
        }
		else {
            $('#select_div').hide();
            $('#stock_type_div').hide();
            $('#location_div').hide();
        }
        if( reportname  == "PO Pending Report" || reportname  == "Indent Pending Report"){
			$('#pendingdet, .pendingdet').show();
			$('#location_div').hide();
		}
		else {
			$('#pendingdet, .pendingdet').hide();
			$('#location_div').show();
		}
        if(reportname  == "Indent Pending Report") {
            $("#cmbsupplier").attr("disabled",true).trigger("chosen:updated");
        }
		else if(reportname  == "PO Pending Report") {
            $("#cmbsupplier").attr("disabled",false).trigger("chosen:updated");;
        }
        if(reportname  == "Non Moving Stock Report") {
           $('#dateRange').hide();
           $('#days_range').show();
           $('#location_div').hide();
        }
        else{
            $('#days_range').hide();
        }
        if(reportname == "Blocked Stock Report"){
            $('#select_div').hide();
            $('#stock_type_div').hide();
            $('#stock_location').hide();
            $('#dateRange').show();
        }if(reportname == "MSL Stock Report"){
            $('#select_div').hide();
            $('#stock_type_div').hide();
            $('#stock_location').hide();
            $('#dateRange').show();
        }
	});

});


$(document).ready(function(){
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
	$(".chosen-select").chosen();
	//used to format the date displayed
	/*$( "#fromdate" ).datepicker({
		dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
	});
	$('#fromdate').datepicker('setDate', '-30d');
	$( "#todate" ).datepicker({
		dateFormat: "yy-mm-dd",
		autoclose: true,
		orientation: 'bottom auto'
	});
	$('#todate').datepicker('setDate', '+0');
	$('#todate').datepicker('setStartDate', '-30d');*/
	$("#reporttype").removeAttr("disabled");
    loadAllMaterials("All", "1", "0");
	FromToDateValidation();

});

$('.nav-pills li').removeClass('active');
$('#nav-reports').addClass('active');

function FromToDateValidation(){
	$('#fromdate').on('changeDate', function(){
		$('#todate').datepicker('setStartDate', new Date($(this).val()));
		var start = new Date($('#fromdate').val()),
		end   = new Date($('#todate').val()),
		diff  = new Date(end - start),
		days  = diff/1000/60/60/24;
		if(days < 0) {
			$('#todate').datepicker('update', $('#fromdate').val());
		}
	});
}

function loadAllMaterials(itemId, makeId, isFaulty) {
    var dataToSend = {
        'type': $("#id_invoice-type option:selected").val(),
        'party_id': $("#id_invoice-party_id option:selected").val(),
        'module': 'invoice',
        'particulars':'invoice_materials'
    }
    generateMaterialAsComboBox(itemId, makeId, isFaulty, dataToSend)
}