$(function () {

    $("#saveEmployee").click(function(){
		$(".error-border").removeClass('error-border');
	    $(".custom-error-message").remove();

	    var ControlCollections = [
	        {
	            controltype: 'textbox',
	            controlid: 'id_emp_code',
	            isrequired: true,
	            errormsg: 'Code is required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_first_name',
	            isrequired: true,
	            errormsg: 'First Name is required.'
	        },
	        {
	            controltype: 'textbox',
	            controlid: 'id_email',
	            isemail: true,
	            emailerrormsg: 'Invalid Email Address.'
	        },
            {
	            controltype: 'textbox',
	            controlid: 'id_phone_no',
	            isrequired: true,
	            errormsg: 'Mobile number is required.'
	        },
	        {
				controltype: 'dropdown',
				controlid: 'id_department_id',
				isrequired: true,
				errormsg: 'Department is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_employment_type',
				isrequired: true,
				errormsg: 'Employment Type is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_status',
				isrequired: true,
				errormsg: 'Employment Status is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_pay_structure_id',
				isrequired: true,
				errormsg: 'Pay Structure is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_gender',
				isrequired: true,
				errormsg: 'Gender is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_martial_status',
				isrequired: true,
				errormsg: 'Marital Status is required.'
			},
			{
				controltype: 'dropdown',
				controlid: 'id_account_type',
				isrequired: true,
				errormsg: 'Account Type is required.'
			},
			{
	            controltype: 'textbox',
	            controlid: 'id_aadhar_number',
	            isrequired: true,
	            errormsg: 'Aadhaar No is required.'
	        },
		];



		var result = JSCustomValidator.JSvalidate(ControlCollections);
		if(!result) {
		    if(!$("#collapseOne").hasClass('in')) {
				$("#accordion .accordion-toggle[data-target='#collapseOne']").click();
			}
		}
		if($("#id_ifsc_code").val()!=""){
		    if($("#ifcs_bank_name").text()=='Invalid IFSC Code'){
		        swal("","Invalid IFSC Code","error");
		        $("#id_ifsc_code").focus()
		        return;
		    }
		}
		if (result){
            if ($("#id_hidden_emp_code").val()!="") {
                if ($('#id_emp_code1').is(':disabled')){
                    addPayitems();
                    return;
                } else {
                    $.ajax({
                        url: "/erp/hr/json/employee/checkempcode/",
                        type: "POST",
                        dataType: "json",
                        data: {emp_code: $("#id_emp_code").val()},
                        success: function (json) {
                             if (json == 1) {
                                swal("WARNING", "Employee Code Already Exist .", "warning");
                                $("#id_emp_code").focus();
                                return;
                            }
                            else {
                                addPayitems();
                            }
                        },
                        error: function (xhr, errmsg, err) {
                            console.log(xhr.status + ": " + xhr.responseText);

                        }
                    });
                }
            }
		}
		return result;
	});



    $(document).ready(function(){

        $('#save_department_button').click(function(){
            $(".error-border").removeClass('error-border');
            $(".custom-error-message").remove();

            var ControlCollections = [
                {
                    controltype: 'textbox',
                    controlid: 'id_department-name',
                    isrequired: true,
                    errormsg: 'Department Name is required.'
                }
             ];
            var result = JSCustomValidator.JSvalidate(ControlCollections);
            if(result){
                clickButton('saveDepartment');
            }
        });
        Calculate();

        emp_id = $("#id_emp_id").val()
        if (emp_id !=""){
            $.ajax({
                url: "/erp/hr/json/employee/employee_pay_items/",
                type: "post",
                datatype:"json",
                data: {employee_id: emp_id},
                success: function(response){
                    $("#pay_list").find("tr:gt(0)").remove();
                    $.each(response, function (i, item) {
                        var row = "<tr class='tr-ledger-bill' border='0' id='employee_pay_structure-"+i+"'  style='font-size:11px; font-weight:normal;' align='center'><td>" +
                            " " +item[0]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-type'  value='" + item[0] +"' />" + "</td><td>" +
                            " " +item[1]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-description' value='" + item[1] +"' />" +
                            "</td><td><input type='text' id='id_employee_pay_structure-" + i + "-amount' name='pay_items' class='form-control text-right' maxlength='13' onfocus='setNumberRangeOnFocus(this,10,2)' onchange='Calculate()' value='"+item[2]+"'/></td></tr>";
                        $('#pay_list').append(row).addClass('tbl');
                    });

                    var row= "<tr class='table-total-col'><td colspan='2'></td><td><input id='total_amount' class='form-control' value='0.00' style='text-align:right;' /></td></tr>"
                    $('#pay_list').append(row);
                    Calculate();
                }
            });
        }
    });

$("#id_department_id").change(function(){
    if($("#id_department_id option:selected").text() == "[+] Add New Department"){
       $("#modal_department_creation").modal('show');
    }
});
$("#id_department_id").click(function(){
    if($("#id_department_id option:selected").text() == "[+] Add New Department"){
       $("#modal_department_creation").modal('show');
    }
});

$("#id_pay_structure_id").change(function(){
    if($("#id_pay_structure_id option:selected").text() != "--Pay Structure--"){
       loadPayStructureList()
    }
});

$("#id_pay_structure_id").click(function(){
    if($("#id_pay_structure_id option:selected").text() != "--Pay Structure--"){
       loadPayStructureList()
    }
});

$("#id_ifsc_code").blur(function(){
    validateIfsc();
});

$("#id_ifsc_code").ready(function(){
    validateIfsc();
});

$('#id_ifsc_code').keydown(function (e){
    if(e.keyCode == 13){
        validateIfsc();
    }
});

});

function validateIfsc(){
    if($("#id_ifsc_code").val() != undefined) {
        if ($("#id_ifsc_code").val().trim() != ''){
            getBankdetails();
            $('#ifcs_bank_details').show();
        }
    }
}

function getBankdetails(){
    var url = "https://ifsc.razorpay.com/"+$("#id_ifsc_code").val();
    $.ajax({
        url: url,
        datatype: "json",
        success: function(response){
           var bank_name = response['BANK'];
           var bank_branch = response['BRANCH'];
           var name = bank_name+ ', ' + bank_branch+ '.';
           name = name.replace("┬á", "");
           $("#ifcs_bank_name").text(name).css({color: 'black'});
        }, error: function(response){
           $("#ifcs_bank_name").text('Invalid IFSC Code').css({color: 'red'});
        }
    });
}

function showPayStructure() {

        emp_id = $("#id_emp_id").val()
        $.ajax({
            url: "/erp/hr/json/employee/employee_pay_items/",
            type: "post",
            datatype:"json",
            data: {employee_id: emp_id},
            success: function(response){
                $("#pay_list_show").find("tr:gt(0)").remove();
                $.each(response, function (i, item) {
                    var row = "<tr class='tr-ledger-bill' border='0' id='employee_pay_structure-"+i+"'  style='font-size:11px; font-weight:normal;' align='center'><td>" +
                        " " +item[0]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-type-display'  value='" + item[0] +"' />" + "</td><td>" +
                        " " +item[1]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-description-display' value='" + item[1] +"' />" +
                        "</td><td><input type='text' class='form-control text-right' disabled='disabled' id='id_employee_pay_structure-" + i + "-amount-display'  value='"+item[2]+"'/></td></tr>";
                    $('#pay_list_show').append(row).addClass('tbl');
                });

                var row= "<tr class='table-total-col'><td colspan='2'></td><td><input id='total_amount_dis' disabled='disabled' class='form-control' value='0.00' style='text-align:right;' /></td></tr>"
                $('#pay_list_show').append(row).addClass('tbl');
                Calculate_Show();
            }
        });
    $("#employee_pay_structure_show_div").modal('show');
}

function addPayitems(){

    var table = document.getElementById('pay_list')
    var rowcount = 0
    input_obj = document.getElementsByName('pay_items');
    for(i =0;i < input_obj.length; i++)	{
        var form_idx = parseInt($('#id_employee_pay_structure-TOTAL_FORMS').val());
        $('#id_employee_pay_structure-TOTAL_FORMS').val(form_idx + 1);
        $('#id_employee_pay_structure-INITIAL_FORMS').val(form_idx + 1);
        type =   $(table.rows[i+1].cells[0]).text().trim()
        description =  $(table.rows[i+1].cells[1]).text().trim()
        amount =  input_obj[i].value
        var row = "<tr hidden='hidden' class='tr-ledger-bill' border='0' id='employee_pay_structure-"+form_idx+"' name='employee_pay_structure-"+form_idx+"' style='font-size:11px; font-weight:normal;' align='center'><td>" +
            " " + type +" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + form_idx + "-type' name='employee_pay_structure-" + form_idx + "-type'  value='" + type +"' />" + "</td><td>" +
            " " + description+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + form_idx + "-description' name='employee_pay_structure-" + form_idx + "-description'  value='" + description +"' />" +
            "</td><td><input type='text' id='id_employee_pay_structure-" + form_idx + "-amount' name='employee_pay_structure-" + form_idx + "-amount' class='form-control text-right' maxlength='13' onfocus='setNumberRangeOnFocus(this,10,2)' onchange='Calculate()' value='"+amount+"'/></td></tr>";
        $('#pay_list').append(row).addClass('tbl');
    }
    $("#saveEmployee").text('Processing...').addClass('btn-processing');
    $("#addCustomer").submit();
    var event_action = $('#id_emp_code1').is(':disabled') ? "Create" : "Update";
    ga('send', 'event', 'Employee', event_action, $('#enterprise_label').val(), 1);
}


function editPayStructure() {
    if ($("#id_pay_structure_id option:selected").val()=="None"){
        swal("WARNING", "Please Select Pay Structure.", "warning");
        return;
    }
    loadPayStructureList()
    $("#employee_pay_structure_div").modal('show');
}


function loadPayStructureList(){

    pay_id = $("#id_pay_structure_id option:selected").val()
    hidden_pay_id = $("#id_hidden_pay_structure_id").val()
    if(pay_id != "")
    {
        emp_id = $("#id_emp_id").val()
        if (emp_id !="" && pay_id == hidden_pay_id){
            $.ajax({
                url: "/erp/hr/json/employee/employee_pay_items/",
                type: "post",
                datatype:"json",
                data: {employee_id: emp_id},
                success: function(response){
                    $("#pay_list").find("tr:gt(0)").remove();
                    $.each(response, function (i, item) {
                        var row = "<tr class='tr-ledger-bill' border='0' id='employee_pay_structure-"+i+"'  style='font-size:11px; font-weight:normal;' align='center'><td>" +
                            " " +item[0]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-type'  value='" + item[0] +"' />" + "</td><td>" +
                            " " +item[1]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-description' value='" + item[1] +"' />" +
                            "</td><td><input type='text' id='id_employee_pay_structure-" + i + "-amount' name='pay_items' class='form-control text-right' maxlength='13' onfocus='setNumberRangeOnFocus(this,10,2)' onchange='Calculate()' value='"+item[2]+"'/></td></tr>";
                        $('#pay_list').append(row).addClass('tbl');
                    });

                    var row= "<tr class='table-total-col'><td colspan='2'></td><td><input id='total_amount' disabled='disabled' class='form-control' value='0.00' style='text-align:right;' /></td></tr>"
                    $('#pay_list').append(row).addClass('tbl');
                    Calculate();
                }
            });
        }
        else{
            $.ajax({
                url: "/erp/hr/json/employee/pay_structure_items/",
                type: "post",
                datatype:"json",
                data: {pay_structure_id: pay_id},
                success: function(response){
                    $("#pay_list").find("tr:gt(0)").remove();
                    $.each(response, function (i, item) {
                        var row = "<tr class='tr-ledger-bill' border='0' id='employee_pay_structure-"+i+"'  style='font-size:11px; font-weight:normal;' align='center'><td>" +
                            " " +item[0]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-type'  value='" + item[0] +"' />" + "</td><td>" +
                            " " +item[1]+" <input type='text' hidden='hidden' id='id_employee_pay_structure-" + i + "-description' value='" + item[1] +"' />" +
                            "</td><td><input type='text' id='id_employee_pay_structure-" + i + "-amount' name='pay_items' class='form-control text-right' maxlength='13' onfocus='setNumberRangeOnFocus(this,10,2)' onchange='Calculate()' value='0'/></td></tr>";
                        $('#pay_list').append(row).addClass('tbl');
                    });
                    var row= "<tr class='table-total-col'><td colspan='2'></td><td><input id='total_amount' disabled='disabled' class='form-control' value='0.00' style='text-align:right;' /></td></tr>"
                    $('#pay_list').append(row).addClass('tbl');
                }
            });
        }
    }

}

function Calculate(){
    setTimeout(function(){
        var table = document.getElementById('pay_list');
        var data_count = document.getElementById('pay_list').rows.length - 2;
        var total = 0
        var earnings=0,deduction=0;
        for (i = 0; i < data_count; i++) {
           if ($('#id_employee_pay_structure-' + i + '-type').val() == "Earning") {
                earnings = earnings + parseFloat($('#id_employee_pay_structure-' + i + '-amount').val())
           }else if($('#id_employee_pay_structure-' + i + '-type').val() == "Deduction"){
                deduction = deduction +  parseFloat($('#id_employee_pay_structure-' + i + '-amount').val())
           }
        }
        $("#total_amount").val((earnings - deduction).toFixed(2));
    },100);
}

function Calculate_Show(){
    var table = document.getElementById('pay_list_show');
    var data_count = document.getElementById('pay_list_show').rows.length - 2;
    var total = 0
    var earnings=0,deduction=0;
    for (i = 0; i < data_count; i++) {
       if ($('#id_employee_pay_structure-' + i + '-type-display').val() == "Earning") {
            earnings = earnings + parseFloat($('#id_employee_pay_structure-' + i + '-amount-display').val())
       }else if($('#id_employee_pay_structure-' + i + '-type-display').val() == "Deduction"){
            deduction = deduction +  parseFloat($('#id_employee_pay_structure-' + i + '-amount-display').val())
       }
    }
    $("#total_amount_dis").val((earnings - deduction).toFixed(2));
}