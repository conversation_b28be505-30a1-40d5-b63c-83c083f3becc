<div class="modal fade" id="modalProjectDetails" role="dialog">
	<div class="modal-dialog">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" id="close_img" data-dismiss="modal">&times;</button>
				<h4 class="modal-title">Add Project</h4>
			</div>
			<div class="modal-body">
				<form action="/erp/masters/json/projects/save/" method="post" id="add_project_form">{% csrf_token %}
					<div class="row">
						<div class="col-sm-12">
							<div class="form-group">
								<label>Project Name<span class="mandatory_mark"> *</span></label>
								<input type="text" id="project_name" name="project_name" class="form-control" placeholder="Enter Project Name" data-enterkey-submit="cmdSaveProject" autocomplete="off" maxlength="50" />
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<input type="submit" class="btn btn-cancel" value="Cancel" data-dismiss="modal" id="cmdcancelproject" />
				{% if logged_in_user|canApprove:'ADMIN' %}
					<input type="submit" class="btn btn-save" value="Save" id="cmdSaveProject" onClick="validateNewProject()" />
				{% else %}
					<span class="btn btn-save" data-tooltip="tooltip" data-placement="left" title="You do not have adequate permission to create Project. Please contact the administrator." disabled>Save</span>
				{% endif %}
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(document).ready(function() {
 		enterKeySubmitEvent();
 	});
</script>