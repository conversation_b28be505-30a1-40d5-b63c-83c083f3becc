<!DOCTYPE html>
<style type="text/css">
	body{
		color: #000;
		font-family: 'Times New Roman';
	}
	table{
		width:100%;
	}
	table, th, td {
		border-collapse: collapse;
	}

	.table.row-seperator th,
	.table.row-seperator td,
	.table.row-seperator th,
	.table.row-seperator td {
	    border-top: 1px solid #666666 !important;
	    border-bottom: 1px solid #666666 !important;
	}

	.table.column-seperator th,
	.table.column-seperator td,
	.table.column-seperator th,
	.table.column-seperator td {
	    border-right: 1px solid #666666 !important;
	    border-left: 1px solid #666666 !important;
	}

	th, td {
		text-align: center;
		line-height: 25px;
		font-size:10pt;
	}
	th{
		font-size:11pt;
	}
	.enterprise_details{
		font-size:10.5pt;
	}
	.page_title{
		font-size: 16pt;
		float:right;
		width:230px;
	}
	.total_gst{
		text-align: right;
		border-bottom:hidden;
		border-left:hidden;
		border-right:hidden;
		line-height:25px;
	}
	.sub_total th ,td{
		text-align:right;
		font-weight: normal;
		padding-right:3px;
		font-size:11pt;
		word-break: break-all;
	}
	.grand_total {
		border-left:hidden;
		border-right:hidden;
		border-bottom:hidden;
		font-size:11pt;
	}
	.icd{
		margin-left: -4px;
		font-size:10pt;
	}
	.subject{
		text-align: justified;
		margin-top: 6px;
		font-size: 12pt;
	}

	.icd_list{
		width: 85px;
		float: left;
		font-weight: bold;
	}
	.icd_data{
		width: calc(60% - 1em);
		float: left;
	}
	.annexture_icd{
	font-size:11pt;
	text-align:center;
	 margin-bottom:30px;
	 margin-top:30px;
	}

	@font-face {
        font-family: 'Times New Roman';
        font-style: normal;
        font-weight: 400;
        src: url(https://www.xserp.in/site_media/fonts/times.ttf) format('truetype');
	}

	.divTable {
        display:  table;
        width:auto;
    }

    .divRow {
       display:table-row;
       width:auto;
    }

    .divCell{
        float:left;
        display:table-column;
        width:75px;
        font-size: 13px;
    }

    .divCell-full {
    	float:left;
        display:table-column;
        width:calc(100% - 300px);
        font-size: 16px;
        word-wrap: break-word;
    }

    .clearfix {
    	clear: both;
	}
</style>
<link type="text/css" rel="stylesheet" href="/site_media/css/styles/bootstrap.css?v={{ current_version }}">
<link type="text/css" rel="stylesheet" href="/site_media/css/jquery-ui.css?v={{ current_version }}">
<body>
	<div class="container" style="border-color: transparent; width: 100%;">
		<div>
			<div class="col-sm-8">
				<img src="{{enterprise_logo}}" style="max-height: 10mm">
				<div class="enterprise_details">{{ source.enterprise.name }}</div>
				<div class="enterprise_details"> {{ enterprise_address }}</div>
				<div class="enterprise_details"><b>Ph:</b>{% if source.enterprise.primary_contact_details.contact.phone_no %} {{ source.enterprise.primary_contact_details.contact.phone_no }} {% endif %}<b>E-mail:</b>{% if source.enterprise.primary_contact_details.contact.email %} {{ source.enterprise.primary_contact_details.contact.email }} {% endif %}</div>
			</div>

			<div class="col-sm-4">
				<div class="col-sm-6"></div>
				<div class="col-sm-6" style="margin-top:20px;">
					<span class="page_title" >{{ form_name }}</span>
				</div>
				<div class="col-sm-4 icd"><b>{{ gst_label }}</b></div>
				<div class="col-sm-8 icd">{{ gst_detail }}</div>
			</div>
		</div>
		<div class="clearfix"></div>
		<div class="col-sm-6" style="margin-top:6px;">
			<div class="vendor_list">
				<b style="font-size: 16px; text-decoration: underline;">Vendor Details </b><br />
				<b>{{ source.party.name }}</b><br />
				{% if source.party.address_1 and source.party.address_1.strip %}{{ source.party.address_1 }}, {% endif %}{% if source.party.address_2 and source.party.address_2.strip %}{{ source.party.address_2 }},{% endif %}
{% if source.party.city and source.party.city.strip %}{{ source.party.city }}, {% endif %}{% if source.party.state and source.party.state.strip %}{{ source.party.state }},{% endif %}
				{% for country in country_list %}
					{% if country.country_code == source.party.country_code %}
						{{ country.country_name|upper }}
					{% endif %}
				{% endfor %}<br />
				<b>Code: </b> {{ source.party.code }}&nbsp;&nbsp;
				<b>GSTIN: </b>
				{% for reg_detail in source.party.registration_details %}
					{% if reg_detail.label == "GSTIN" %}
						{{ reg_detail.details }}
					{% endif %}
				{% endfor %}
				<br />
				<b>Phone No: </b>  {{ source.party.primary_contact_details.contact.phone_no }}
			</div>
			{% if irn_details == "" %}
				</div>
				<div class="col-sm-6" style="margin-top:6px;">
			{% else %}
				<div class="col-sm-12 remove-padding">
					<hr style="margin:10px 0;border: 1pt solid #666; " />
				</div>
			{% endif %}
			<div>
				<div class="icd_list">Note No.</div>
				<div class="icd_data"><b>:</b> {{ source.getCode }} <i>{% if source.revisions.length > 0 %} (#{{ source.revisions.length }}) {% endif %}</i> </div>
			</div>
			<div class="clearfix"></div>
			<div>
				<div class="icd_list">Date</div>
				<div class="icd_data"><b>:</b> {{ note_date }} </div>
			</div>
			<div class="clearfix"></div>
			<div>
				<div class="icd_list">GRN No</div>
				<div class="icd_data"><b>:</b> {% if source.note_receipt_map %} {{ source.note_receipt_map.receipt.getCode }} {% else %} -NA- {% endif %} </div>
			</div>
			<div class="clearfix"></div>
			<div>
				<div class="icd_list">GRN Date</div>
				<div class="icd_data"><b>:</b> {{ receipt_date }}</div>
			</div>
		</div>
		{% if irn_details != "" %}
			<div class="col-sm-6 remove-padding">
				<table class="table item_table" style="margin-bottom: 7px; width: 100%; vertical-align: top;">
					<tr style="vertical-align: top;">
						<td class="text-left" style="width: 275px;">
							<img src="{{irn_details.irn_scanning_code}}" style="min-width: 275px; max-width: 275px; width: 275px;">
							<br />
							<span style="font-size: 14px;">Digitally Signed by NIC-IRP</span>
						</td>
						<td class="text-left" style="vertical-align: top !important; font-size: 12px;">
							<b style="font-weight: bold;">Ack No.: </b><br />
							{{irn_details.ack_no}}<br/><br />
							<b style="font-weight: bold;">Ack Date: </b><br />
							{{irn_details.ack_date}}<br/><br />
							<b style="font-weight: bold;">IRN: </b><br />
							<span class="word-break: break-word;">{{irn_details.irn}}</span>
						</td>
					</tr>
				</table>
			</div>
		{% endif %}
	</div>

	<div class="container">
		<div class="col-sm-12 subject">
			<span>Dear Sir/Madam,</span>
			<div style="text-align: center;">In Reference to {%if source.is_credit %} our {% else %} your {% endif %}  <b>Invoice No.</b>: {{ source.inv_no }} (<i>Date: {{ source.inv_date |date:"M d, Y" }} Value: {{ source.inv_value }})</i></div>
			<div style="text-align: justified;">We wish to advice having {%if source.is_credit %} credited {% else %} debited {% endif %} your Account with us as follows :</div>

			{% if appendix_pages > 0 %}
				<table border=1 bordercolor="#A9A9A9" class="row-seperator column-seperator">
					<tr>
						<th rowspan="2" style="width:40px;">S.No</th>
						<th rowspan="2" style="width:350px;">Drawing No<br>Description</th>
						<th rowspan="2" style="width:100px;">Reason</th>
						<th rowspan="2" style="width:100px;">Quantity</th>
						<th rowspan="2" style="width:50px;">Unit</th>
						<th rowspan="2" style="width:130px;">Rate</th>
						<th rowspan="2" style="width:150px;">Amount</th>
						<th colspan="2" style="width:30px;">CGST</th>
						<th colspan="2" style="width:30px;">SGST</th>
						<th colspan="2" style="width:30px;">IGST</th>
					</tr>
					<tr>
						<th style="width:40px;">%</th>
						<th style="width:40px;">({{ source.currency }})</th>
						<th style="width:40px;">%</th>
						<th style="width:40px;">({{ source.currency }})</th>
						<th style="width:40px;">%</th>
						<th style="width:40px;">({{ source.currency }})</th>
					</tr>
					<tr class="sub_total">
						<td colspan="13" style="text-align: center; line-height:30px; font-size:15px;">AS PER ANNEXURE TO NOTE NO:<br><b style="font-size:14px;">{{ source.getCode }}</b>
						</td>
					</tr>
					<tr class="sub_total">
						<td colspan="2" style="border-left:hidden;border-bottom:hidden;border-right:hidden;">
						<td colspan="2" style="border-right:hidden;"><b>Sub Total</b></td>
						<td colspan="3" style="border-right:hidden;"><b>{{ summary_details.total }} </b></td>
						<td colspan="2" style="border-right:hidden;">{{ summary_details.net_cgst }}</td>
						<td colspan="2" style="border-right:hidden;">{{ summary_details.net_sgst }}</td>
						<td colspan="2" style="border-right:hidden;">{{ summary_details.net_igst }}</td>
					</tr>
					{% for tax in note_taxes %}
						<tr>
							<td class="total_gst"></td>
							<td colspan="3" class="total_gst">{{ tax.tax_name }}</td>
							<td colspan="1" class="total_gst">{{ tax.tax_rate }}</td>
							<td colspan="1" class="total_gst" style="text-align:left">%</td>
							<td class="total_gst">{{ tax.tax_value }}</td>
							<td class="total_gst"></td>
						</tr>
					{% endfor %}
					{% if icd_taxes %}
						<tr>
							<td style=":border-top:1px solid #000;"></td>
						</tr>
					{% endif %}
					{% if source.round_off and source.round_off != 0 %}
						<tr>
							<td colspan="2" class="grand_total"></td>
							<td colspan="2" class="grand_total" style="border-bottom:1px;"><b>Round-off</b></td>
							<td class="grand_total" style="border-bottom:1px;">{{ source.currency }}</td>
							<td colspan="2" class="grand_total" style="border-bottom:1px;"><b>{{ summary_details.round_off }}</b></td>
							<td colspan="2" class="grand_total" style="border-bottom:1px;"></td>
						</tr>
					{% endif %}
					<tr>
						<td colspan="4" class="grand_total" style="border-bottom:1px;"><b>Total Value</b></td>
						<td class="grand_total" style="border-bottom:1px;">{{ source.currency }}</td>
						<td colspan="2" class="grand_total" style="border-bottom:1px;"><b>{{ summary_details.grand_total }}</b></td>
						<td colspan="7" class="grand_total" style="border-bottom:1px;"></td>
					</tr>
				</table>
			{% else %}
				{% include "auditing/icd_print_item_table.html" %}
			{% endif %}
			<div>
				<span class="grand_total" style="border-top:1px solid #000;"></span>
			</div>
			<div>
				{% if summary_details.grand_total_in_words != "" %}
					<span class="grand_total" style="text-align:left;">({{ source.currency }} {{ summary_details.grand_total_in_words|upper }})</span>
				{% endif %}
			</div>
			  {% if source.remarks|length > 0 %}
			<tr><b>Remarks:</b>
			    {% for item in source.remarks %}
				<td colspan="4" style="word-break: break-all"> {{ item.remarks }}{% if not forloop.last %}, {% endif %}</td>
				{% endfor %}
				{% endif %}
			</tr>
		</div>
		{% if appendix_pages > 0 %}
			<div style="page-break-after: always"></div>
		{% endif %}
		{% if appendix_pages > 0 %}
			<div class="annexture_icd">ANNEXURE TO NOTE {{ source.getCode }} {% if verified_date %} DATED {{ verified_date }} {% endif %}</div>
			<div class="col-sm-12" id="icd_material_table">
				{% include "auditing/icd_print_item_table.html" %}
			</div>
		{% endif %}
	</div>
</body>